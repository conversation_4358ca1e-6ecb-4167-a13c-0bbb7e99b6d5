from flask import Flask, request, jsonify, send_file
import os
import cv2
import numpy as np
import tempfile
import shutil
import json
import zipfile
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

@app.route('/')
def health_check():
    """Simple health check endpoint."""
    return jsonify({'status': 'healthy', 'message': 'WINmemory Processing API is running'})

@app.route('/process_video', methods=['POST'])
def process_video():
    # Example: receive a video file, process it, and return a result
    if 'video' not in request.files:
        return jsonify({'error': 'No video file provided'}), 400
    video_file = request.files['video']
    video_path = os.path.join('/tmp', video_file.filename)
    video_file.save(video_path)

    # Example OpenCV processing: get frame count
    cap = cv2.VideoCapture(video_path)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    cap.release()
    os.remove(video_path)

    return jsonify({'frame_count': frame_count})

@app.route('/create_video', methods=['POST'])
def create_video_endpoint():
    # Accepts multiple images and returns a video file
    if 'images' not in request.files:
        return jsonify({'error': 'No images provided'}), 400
    images = request.files.getlist('images')
    fps = int(request.form.get('fps', 30))

    with tempfile.TemporaryDirectory() as tmpdir:
        image_paths = []
        for img in images:
            img_path = os.path.join(tmpdir, img.filename)
            img.save(img_path)
            image_paths.append(img_path)

        # Read the first image to get dimensions
        first_image = cv2.imread(image_paths[0])
        height, width, layers = first_image.shape
        size = (width, height)
        video_path = os.path.join(tmpdir, 'output.mp4')
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(video_path, fourcc, fps, size)
        for img_path in image_paths:
            img = cv2.imread(img_path)
            if img.shape[:2] != (height, width):
                img = cv2.resize(img, size)
            out.write(img)
        out.release()
        return send_file(video_path, as_attachment=True, download_name='output.mp4')

@app.route('/decompose_video', methods=['POST'])
def decompose_video_endpoint():
    # Accepts a video and metadata, returns a zip of extracted images
    if 'video' not in request.files or 'metadata' not in request.files:
        return jsonify({'error': 'Video and metadata required'}), 400
    video_file = request.files['video']
    metadata_file = request.files['metadata']
    metadata = json.load(metadata_file)

    with tempfile.TemporaryDirectory() as tmpdir:
        video_path = os.path.join(tmpdir, video_file.filename)
        video_file.save(video_path)
        output_dir = os.path.join(tmpdir, 'extracted')
        os.makedirs(output_dir, exist_ok=True)

        cap = cv2.VideoCapture(video_path)
        frame_count = 0
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            photo_path_from_metadata = metadata.get(str(frame_count))
            if photo_path_from_metadata:
                original_filename = os.path.basename(photo_path_from_metadata)
                output_photo_path = os.path.join(output_dir, original_filename)
            else:
                output_photo_path = os.path.join(output_dir, f"frame_{frame_count:06d}.png")
            cv2.imwrite(output_photo_path, frame)
            frame_count += 1
        cap.release()

        # Zip the extracted images
        zip_path = os.path.join(tmpdir, 'extracted_images.zip')
        with zipfile.ZipFile(zip_path, 'w') as zipf:
            for root, _, files in os.walk(output_dir):
                for file in files:
                    zipf.write(os.path.join(root, file), arcname=file)
        return send_file(zip_path, as_attachment=True, download_name='extracted_images.zip')

application = app

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)