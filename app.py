from flask import Flask, request, jsonify, send_file
import os
import cv2
import numpy as np
import tempfile
import shutil
import json
import zipfile
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

@app.route('/')
def health_check():
    """Simple health check endpoint."""
    return jsonify({'status': 'healthy', 'message': 'WINmemory Processing API is running'})

@app.route('/process_video', methods=['POST'])
def process_video():
    # Example: receive a video file, process it, and return a result
    if 'video' not in request.files:
        return jsonify({'error': 'No video file provided'}), 400
    video_file = request.files['video']
    video_path = os.path.join('/tmp', video_file.filename)
    video_file.save(video_path)

    # Example OpenCV processing: get frame count
    cap = cv2.VideoCapture(video_path)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    cap.release()
    os.remove(video_path)

    return jsonify({'frame_count': frame_count})

@app.route('/create_video', methods=['POST'])
def create_video_endpoint():
    # Accepts multiple images and returns a video file
    if 'images' not in request.files:
        return jsonify({'error': 'No images provided'}), 400
    images = request.files.getlist('images')
    fps = int(request.form.get('fps', 30))

    with tempfile.TemporaryDirectory() as tmpdir:
        image_paths = []
        for img in images:
            img_path = os.path.join(tmpdir, img.filename)
            img.save(img_path)
            image_paths.append(img_path)

        # Read the first image to get dimensions
        first_image = cv2.imread(image_paths[0])
        height, width, layers = first_image.shape
        size = (width, height)
        video_path = os.path.join(tmpdir, 'output.mp4')
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(video_path, fourcc, fps, size)
        for img_path in image_paths:
            img = cv2.imread(img_path)
            if img.shape[:2] != (height, width):
                img = cv2.resize(img, size)
            out.write(img)
        out.release()
        return send_file(video_path, as_attachment=True, download_name='output.mp4')

    session_id = str(uuid4())
    temp_dir = os.path.join(app.config['UPLOAD_FOLDER'], f'session_{session_id}')
    os.makedirs(temp_dir, exist_ok=True)

    saved_photo_paths = []
    for file in uploaded_files:
        filename = file.filename
        file_path = os.path.join(temp_dir, filename)
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        file.save(file_path)
        saved_photo_paths.append(file_path)

    output_video_path = os.path.join(app.config['OUTPUT_FOLDER'], output_video_name)
    output_metadata_path = os.path.join(app.config['OUTPUT_FOLDER'], output_metadata_name)

    threading.Thread(target=run_create_process_web, args=(temp_dir, output_video_path, output_metadata_path, output_video_name, output_metadata_name, session_id)).start()
    session['video_filename'] = output_video_name
    session['metadata_filename'] = output_metadata_name
    session['status_message'] = 'Processing started. Please wait and refresh for results.'
    session['status_type'] = 'info'
    return redirect(url_for('index'))

def run_create_process_web(input_dir, output_video_path, output_metadata_path, video_name, metadata_name, session_id):
    """Runs the video creation process in a separate thread for the web UI."""
    print(f"Web: Starting video creation from {input_dir}")
    try:
        photo_paths = gather_photos(input_dir)
        if not photo_paths:
            print("Web: No photos found or error gathering photos.")
            set_status('No photos found in the uploaded folder.', 'error')
            return

        metadata = create_metadata(photo_paths)
        save_metadata(metadata, output_metadata_path)

        create_video(photo_paths, output_video_path)

        print(f"Web: Video creation complete: {output_video_path}")
        set_status('Video created successfully! Download links are below.', 'success')
    except Exception as e:
        print(f"Web: An error occurred during video creation: {e}")
        set_status(f'Error during video creation: {e}', 'error')
    finally:
        # Optionally clean up temp_dir
        shutil.rmtree(input_dir, ignore_errors=True)

@app.route('/decompose_video', methods=['POST'])
def decompose_video_web():
    """Handles the web request to decompose a video to photos."""
    if 'input_video_file' not in request.files or 'input_metadata_file' not in request.files:
        # TODO: Provide better error feedback to the user
        print("Missing file uploads for video decomposition.")
        return redirect(url_for('index'))

    input_video_file = request.files['input_video_file']
    input_metadata_file = request.files['input_metadata_file']
    output_photos_dir_name = request.form.get('output_photos_dir_name')

    if input_video_file.filename == '' or input_metadata_file.filename == '' or not output_photos_dir_name:
        # TODO: Provide better error feedback to the user
        print("Missing file names or output directory name for video decomposition.")
        return redirect(url_for('index'))

    # Save uploaded files temporarily
    video_filename = input_video_file.filename
    metadata_filename = input_metadata_file.filename

    input_video_path = os.path.join(app.config['UPLOAD_FOLDER'], video_filename)
    input_metadata_path = os.path.join(app.config['UPLOAD_FOLDER'], metadata_filename)
    output_dir_path = os.path.join(app.config['OUTPUT_FOLDER'], output_photos_dir_name)

    try:
        input_video_file.save(input_video_path)
        input_metadata_file.save(input_metadata_path)
    except IOError as e:
        print(f"Error saving uploaded files: {e}")
        # TODO: Provide error feedback to the user
        return redirect(url_for('index'))


    # Run the process in a separate thread to avoid blocking the web server
    # TODO: Implement a way to show progress/status to the user
    threading.Thread(target=run_decompose_process_web, args=(input_video_path, input_metadata_path, output_dir_path)).start()

    # TODO: Redirect to a status page or show a success message
    return redirect(url_for('index'))

def run_decompose_process_web(input_video_path, input_metadata_path, output_dir_path):
    """Runs the video decomposition process in a separate thread for the web UI."""
    print(f"Web: Starting video decomposition for {input_video_path}")
    try:
        metadata = read_metadata(input_metadata_path)
        if not metadata:
            print("Web: Could not read metadata.")
            # TODO: Log this or update a status for the user
            return

        decompose_video(input_video_path, output_dir_path, metadata)

        print(f"Web: Video decomposition complete. Photos saved to {output_dir_path}")
        # TODO: Update a status for the user
    except Exception as e:
        print(f"Web: An error occurred during video decomposition: {e}")
        # TODO: Log this or update a status for the user
    finally:
        # Clean up uploaded files (optional, depending on requirements)
        # os.remove(input_video_path)
        # os.remove(input_metadata_path)
        pass

@app.route('/download/<filename>')
def download_file(filename):
    file_path = os.path.join(app.config['OUTPUT_FOLDER'], filename)
    if os.path.exists(file_path):
        return send_file(file_path, as_attachment=True)
    return "File not found", 404

def set_status(message, status_type):
    with app.app_context():
        session['status_message'] = message
        session['status_type'] = status_type

application = app

if __name__ == '__main__':
    # Running in debug mode for development
    # In production, use a production-ready WSGI server
    app.run(debug=True)