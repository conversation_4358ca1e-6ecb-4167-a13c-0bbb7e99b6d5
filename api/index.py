from flask import Flask, render_template, request, redirect, url_for, send_file, session
import os
import shutil
import requests
from requests.exceptions import RequestException

app = Flask(
    __name__,
    template_folder=os.path.join(os.path.dirname(__file__), '../templates'),
    static_folder=os.path.join(os.path.dirname(__file__), '../static')
)
app.secret_key = 'supersecretkey'  # Needed for session

UPLOAD_FOLDER = '/tmp/uploads'
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER, exist_ok=True)
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

OUTPUT_FOLDER = '/tmp/output'
if not os.path.exists(OUTPUT_FOLDER):
    os.makedirs(OUTPUT_FOLDER, exist_ok=True)
app.config['OUTPUT_FOLDER'] = OUTPUT_FOLDER

@app.route('/')
def index():
    video_filename = session.pop('video_filename', None)
    metadata_filename = session.pop('metadata_filename', None)
    status_message = session.pop('status_message', None)
    status_type = session.pop('status_type', None)
    return render_template('index.html', video_filename=video_filename, metadata_filename=metadata_filename, status_message=status_message, status_type=status_type)

@app.route('/test_backend')
def test_backend():
    """Test endpoint to check backend connectivity"""
    try:
        aws_api_url = 'https://winmemory-processing-env.eba-gu58unjs.us-west-2.elasticbeanstalk.com/'
        response = requests.get(aws_api_url, timeout=10)
        return {
            'status': 'success',
            'backend_status': response.status_code,
            'backend_response': response.text[:200]
        }
    except Exception as e:
        return {
            'status': 'error',
            'error': str(e)
        }

@app.route('/create_video', methods=['POST'])
def create_video_web():
    uploaded_files = request.files.getlist('input_photos_dir')
    output_video_name = request.form.get('output_video_name')
    output_metadata_name = request.form.get('output_metadata_name')

    if not uploaded_files or not output_video_name or not output_metadata_name:
        session['status_message'] = 'Missing input. Please select a folder and provide output filenames.'
        session['status_type'] = 'error'
        return redirect(url_for('index'))

    output_video_path = os.path.join(app.config['OUTPUT_FOLDER'], output_video_name)

    try:
        # Prepare files for AWS API
        files = []
        for file in uploaded_files:
            if file.filename:
                files.append(('images', (file.filename, file.read(), file.mimetype)))

        data = {'fps': request.form.get('fps', 30)}
        aws_api_url = 'https://winmemory-processing-env.eba-gu58unjs.us-west-2.elasticbeanstalk.com/create_video'
        aws_response = requests.post(aws_api_url, files=files, data=data, timeout=120)
        aws_response.raise_for_status()

        # Save the video file
        with open(output_video_path, 'wb') as f:
            f.write(aws_response.content)

        session['video_filename'] = output_video_name
        session['metadata_filename'] = output_metadata_name
        session['status_message'] = 'Video created successfully! Download link below.'
        session['status_type'] = 'success'
    except RequestException as e:
        session['status_message'] = f'Error: Could not connect to the processing server. {str(e)}'
        session['status_type'] = 'error'
    except Exception as e:
        session['status_message'] = f'Error: {str(e)}'
        session['status_type'] = 'error'

    return redirect(url_for('index'))

@app.route('/decompose_video', methods=['POST'])
def decompose_video_web():
    if 'input_video_file' not in request.files or 'input_metadata_file' not in request.files:
        return redirect(url_for('index'))

    input_video_file = request.files['input_video_file']
    input_metadata_file = request.files['input_metadata_file']
    output_photos_dir_name = request.form.get('output_photos_dir_name')

    if input_video_file.filename == '' or input_metadata_file.filename == '' or not output_photos_dir_name:
        return redirect(url_for('index'))

    try:
        # Prepare files for AWS API
        files = {
            'video': (input_video_file.filename, input_video_file.read(), input_video_file.mimetype),
            'metadata': (input_metadata_file.filename, input_metadata_file.read(), input_metadata_file.mimetype)
        }
        aws_api_url = 'https://winmemory-processing-env.eba-gu58unjs.us-west-2.elasticbeanstalk.com/decompose_video'
        aws_response = requests.post(aws_api_url, files=files, timeout=120)
        aws_response.raise_for_status()

        # Save the extracted images zip
        output_zip_path = os.path.join(app.config['OUTPUT_FOLDER'], 'extracted_images.zip')
        with open(output_zip_path, 'wb') as f:
            f.write(aws_response.content)

        session['status_message'] = 'Decomposition complete! Download your images below.'
        session['status_type'] = 'success'
    except RequestException as e:
        session['status_message'] = f'Error: Could not connect to the processing server. {str(e)}'
        session['status_type'] = 'error'
    except Exception as e:
        session['status_message'] = f'Error: {str(e)}'
        session['status_type'] = 'error'
    return redirect(url_for('index'))

@app.route('/download/<filename>')
def download_file(filename):
    file_path = os.path.join(app.config['OUTPUT_FOLDER'], filename)
    if os.path.exists(file_path):
        return send_file(file_path, as_attachment=True)
    return "File not found", 404

# Do NOT include app.run() here! 