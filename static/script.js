document.addEventListener('DOMContentLoaded', (event) => {
    const statusDiv = document.getElementById('status');

    // Function to update the status message
    function updateStatus(message) {
        if (statusDiv) {
            statusDiv.textContent = message;
            // Optional: Add a class for temporary visual feedback
            statusDiv.classList.add('visible');
            // Remove the class after a few seconds
            setTimeout(() => {
                statusDiv.classList.remove('visible');
            }, 5000); // Message visible for 5 seconds
        }
    }

    // Modern drag-and-drop for all .drag-drop-zone elements
    document.querySelectorAll('.drag-drop-zone').forEach(zone => {
        const input = zone.querySelector('input');
        if (!input) return;
        // Prevent default drag behaviors
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            zone.addEventListener(eventName, preventDefaults, false);
        });
        // Highlight on dragenter/dragover
        ['dragenter', 'dragover'].forEach(eventName => {
            zone.addEventListener(eventName, () => zone.classList.add('highlight'), false);
        });
        // Unhighlight on dragleave/drop
        ['dragleave', 'drop'].forEach(eventName => {
            zone.addEventListener(eventName, () => zone.classList.remove('highlight'), false);
        });
        // Handle drop
        zone.addEventListener('drop', (e) => {
            const dt = e.dataTransfer;
            const files = dt.files;
            if (input.type === 'file') {
                input.files = files;
                updateDropZoneLabel(zone, files);
                updateStatus(`Dropped file: ${files[0]?.name || 'unknown'}`);
            } else if (input.type === 'text') {
                if (files.length > 0 && files[0].webkitRelativePath) {
                    input.value = files[0].webkitRelativePath.split('/')[0];
                    updateDropZoneLabel(zone, files);
                    updateStatus(`Dropped directory: ${input.value}`);
                } else if (files.length > 0 && files[0].name) {
                    input.value = files[0].name;
                    updateDropZoneLabel(zone, files);
                    updateStatus(`Dropped: ${files[0].name}`);
                } else {
                    updateStatus("Drag and drop for directories might not be fully supported in your browser. Please use the 'Browse' button.");
                }
            }
        }, false);
        // Optional: clicking the zone opens the file explorer
        zone.addEventListener('click', () => input.click());
        // Show file/folder name on manual selection
        input.addEventListener('change', (e) => {
            updateDropZoneLabel(zone, input.files);
        });
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    function updateDropZoneLabel(zone, files) {
        const label = zone.querySelector('.drag-drop-text');
        if (!label) return;
        if (files.length === 0) {
            label.textContent = zone.dataset.defaultLabel || label.textContent;
        } else if (files.length === 1) {
            label.textContent = files[0].name;
        } else {
            label.textContent = `${files.length} files selected`;
        }
    }

    // Disable submit button on form submit
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function(e) {
            const btn = form.querySelector('button[type="submit"]');
            if (btn) {
                btn.disabled = true;
                btn.classList.add('disabled');
                btn.textContent = 'Processing...';
            }
        });
    });

    // Optional: Add some simple animations or transitions via CSS classes
    const buttons = document.querySelectorAll('button');
    buttons.forEach(button => {
        button.addEventListener('click', () => {
            button.classList.add('clicked');
            setTimeout(() => {
                button.classList.remove('clicked');
            }, 300);
        });
    });

    // Initial status message
    updateStatus("UI loaded. Ready.");

    // Dashboard Tabs logic
    function setupDashboardTabs() {
        const tabs = document.querySelectorAll('.tab-btn');
        const panels = document.querySelectorAll('.tab-panel');
        tabs.forEach((tab, idx) => {
            tab.addEventListener('click', () => {
                tabs.forEach(t => { t.classList.remove('active'); t.setAttribute('aria-selected', 'false'); });
                panels.forEach(p => p.style.display = 'none');
                tab.classList.add('active');
                tab.setAttribute('aria-selected', 'true');
                panels[idx].style.display = 'block';
            });
            tab.addEventListener('keydown', (e) => {
                if (e.key === 'ArrowRight' || e.key === 'ArrowLeft') {
                    let newIdx = idx + (e.key === 'ArrowRight' ? 1 : -1);
                    if (newIdx < 0) newIdx = tabs.length - 1;
                    if (newIdx >= tabs.length) newIdx = 0;
                    tabs[newIdx].focus();
                }
            });
        });
    }
    setupDashboardTabs();

    // Sidebar toggle functionality
    const sidebar = document.getElementById('sidebar');
    const sidebarToggle = document.getElementById('sidebar-toggle');
    if (sidebar && sidebarToggle) {
        sidebarToggle.addEventListener('click', () => {
            const isCollapsed = sidebar.classList.toggle('collapsed');
            sidebarToggle.setAttribute('aria-expanded', !isCollapsed);
            // On mobile, show/hide sidebar
            if (window.innerWidth <= 900) {
                if (sidebar.classList.contains('collapsed')) {
                    sidebar.style.width = '220px';
                } else {
                    sidebar.style.width = '0';
                }
            }
        });
        // Accessibility: allow toggle with Enter/Space
        sidebarToggle.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                sidebarToggle.click();
            }
        });
    }
});