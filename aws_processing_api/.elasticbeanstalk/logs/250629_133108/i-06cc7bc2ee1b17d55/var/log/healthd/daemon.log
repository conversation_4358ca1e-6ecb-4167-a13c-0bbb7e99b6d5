# Logfile created on 2025-06-29 11:46:31 +0000 by logger.rb/v1.6.6
A, [2025-06-29T11:46:32.219493 #2094]   ANY -- : healthd daemon 1.0.7 initialized
W, [2025-06-29T11:46:32.288158 #2094]  WARN -- : log file "/var/log/nginx/healthd/application.log.2025-06-29-11" does not exist
W, [2025-06-29T11:46:37.289837 #2094]  WARN -- : log file "/var/log/nginx/healthd/application.log.2025-06-29-11" does not exist
W, [2025-06-29T11:46:42.290836 #2094]  WARN -- : log file "/var/log/nginx/healthd/application.log.2025-06-29-11" does not exist
F, [2025-06-29T11:49:07.682460 #2094] FATAL -- : /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/puma-6.6.0/lib/puma/launcher.rb:447:in 'block in Puma::Launcher#setup_signals': SIGTERM (SignalException)
	from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/puma-6.6.0/lib/puma/single.rb:63:in 'Thread#join'
	from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/puma-6.6.0/lib/puma/single.rb:63:in 'Puma::Single#run'
	from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/puma-6.6.0/lib/puma/launcher.rb:203:in 'Puma::Launcher#run'
	from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/puma-6.6.0/lib/puma/cli.rb:75:in 'Puma::CLI#run'
	from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/bin/healthd:112:in 'block in <top (required)>'
	from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/bin/healthd:19:in 'Dir.chdir'
	from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/bin/healthd:19:in '<top (required)>'
	from /opt/elasticbeanstalk/lib/ruby/bin/healthd:25:in 'Kernel#load'
	from /opt/elasticbeanstalk/lib/ruby/bin/healthd:25:in '<main>'
A, [2025-06-29T11:49:09.881859 #2624]   ANY -- : healthd daemon 1.0.7 initialized
W, [2025-06-29T11:49:19.968894 #2624]  WARN -- : discarding statistic item after validation error (Invalid timestamp): {id: "0", namespace: "application", timestamp: **********, data: "{\"duration\":10,\"latency_histogram\":[[0.0,7]],\"http_counters\":{\"status_502\":7,\"request_count\":7}}"}
W, [2025-06-29T12:00:01.100675 #2624]  WARN -- : log file "/var/log/nginx/healthd/application.log.2025-06-29-12" does not exist
F, [2025-06-29T12:29:54.600407 #2624] FATAL -- : /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/puma-6.6.0/lib/puma/launcher.rb:447:in 'block in Puma::Launcher#setup_signals': SIGTERM (SignalException)
	from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/puma-6.6.0/lib/puma/single.rb:63:in 'Thread#join'
	from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/puma-6.6.0/lib/puma/single.rb:63:in 'Puma::Single#run'
	from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/puma-6.6.0/lib/puma/launcher.rb:203:in 'Puma::Launcher#run'
	from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/puma-6.6.0/lib/puma/cli.rb:75:in 'Puma::CLI#run'
	from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/bin/healthd:112:in 'block in <top (required)>'
	from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/bin/healthd:19:in 'Dir.chdir'
	from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/bin/healthd:19:in '<top (required)>'
	from /opt/elasticbeanstalk/lib/ruby/bin/healthd:25:in 'Kernel#load'
	from /opt/elasticbeanstalk/lib/ruby/bin/healthd:25:in '<main>'
A, [2025-06-29T12:29:56.930988 #4730]   ANY -- : healthd daemon 1.0.7 initialized
