2025/06/29 11:46:33 [warn] 2217#2217: could not build optimal types_hash, you should increase either types_hash_max_size: 1024 or types_hash_bucket_size: 64; ignoring types_hash_bucket_size
2025/06/29 11:46:34 [warn] 2252#2252: could not build optimal types_hash, you should increase either types_hash_max_size: 1024 or types_hash_bucket_size: 64; ignoring types_hash_bucket_size
2025/06/29 11:46:34 [warn] 2253#2253: could not build optimal types_hash, you should increase either types_hash_max_size: 1024 or types_hash_bucket_size: 64; ignoring types_hash_bucket_size
2025/06/29 11:46:42 [error] 2260#2260: *1 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET / HTTP/1.1", upstream: "http://127.0.0.1:8000/", host: "************"
2025/06/29 11:46:57 [error] 2259#2259: *3 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET / HTTP/1.1", upstream: "http://127.0.0.1:8000/", host: "************"
2025/06/29 11:47:03 [error] 2260#2260: *5 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "POST /hello.world?%ADd+allow_url_include%3d1+%ADd+auto_prepend_file%3dphp://input HTTP/1.1", upstream: "http://127.0.0.1:8000/hello.world?%ADd+allow_url_include%3d1+%ADd+auto_prepend_file%3dphp://input", host: "***********"
2025/06/29 11:47:04 [error] 2260#2260: *5 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:06 [error] 2260#2260: *8 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /vendor/phpunit/phpunit/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/vendor/phpunit/phpunit/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:07 [error] 2260#2260: *10 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /vendor/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/vendor/phpunit/src/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:08 [error] 2260#2260: *12 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /vendor/phpunit/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/vendor/phpunit/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:10 [error] 2260#2260: *14 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /vendor/phpunit/phpunit/LICENSE/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/vendor/phpunit/phpunit/LICENSE/eval-stdin.php", host: "***********"
2025/06/29 11:47:11 [error] 2260#2260: *16 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /vendor/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/vendor/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:12 [error] 2260#2260: *18 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET / HTTP/1.1", upstream: "http://127.0.0.1:8000/", host: "************"
2025/06/29 11:47:13 [error] 2260#2260: *20 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/phpunit/phpunit/src/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:15 [error] 2260#2260: *22 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /phpunit/phpunit/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/phpunit/phpunit/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:17 [error] 2260#2260: *24 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/phpunit/src/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:19 [error] 2260#2260: *26 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /phpunit/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/phpunit/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:20 [error] 2260#2260: *28 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /lib/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/lib/phpunit/phpunit/src/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:21 [error] 2260#2260: *30 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /lib/phpunit/phpunit/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/lib/phpunit/phpunit/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:23 [error] 2260#2260: *32 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /lib/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/lib/phpunit/src/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:24 [error] 2260#2260: *34 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /lib/phpunit/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/lib/phpunit/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:26 [error] 2260#2260: *36 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /lib/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/lib/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:27 [error] 2259#2259: *38 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET / HTTP/1.1", upstream: "http://127.0.0.1:8000/", host: "************"
2025/06/29 11:47:28 [error] 2259#2259: *40 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /laravel/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/laravel/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:31 [error] 2259#2259: *42 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /www/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/www/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:31 [error] 2259#2259: *44 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: , request: "GET / HTTP/1.1", upstream: "http://127.0.0.1:8000/", host: "************"
2025/06/29 11:47:33 [error] 2259#2259: *46 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /ws/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/ws/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:34 [error] 2259#2259: *48 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /yii/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/yii/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:35 [error] 2259#2259: *50 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /zend/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/zend/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:37 [error] 2259#2259: *52 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /ws/ec/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/ws/ec/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:38 [error] 2259#2259: *54 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /V2/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/V2/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:40 [error] 2259#2259: *56 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /tests/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/tests/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:41 [error] 2259#2259: *58 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /test/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/test/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:43 [error] 2259#2259: *60 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET / HTTP/1.1", upstream: "http://127.0.0.1:8000/", host: "************"
2025/06/29 11:47:43 [error] 2259#2259: *62 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /testing/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/testing/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:45 [error] 2259#2259: *64 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /api/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/api/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:46 [error] 2259#2259: *66 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: , request: "GET / HTTP/1.1", upstream: "http://127.0.0.1:8000/", host: "************"
2025/06/29 11:47:47 [error] 2259#2259: *68 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /demo/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/demo/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:49 [error] 2260#2260: *70 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /cms/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/cms/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:51 [error] 2260#2260: *72 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /crm/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/crm/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:52 [error] 2260#2260: *74 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /admin/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/admin/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:53 [error] 2260#2260: *76 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /backup/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/backup/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:55 [error] 2260#2260: *78 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /blog/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/blog/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:56 [error] 2260#2260: *80 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /workspace/drupal/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/workspace/drupal/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:47:58 [error] 2260#2260: *82 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET / HTTP/1.1", upstream: "http://127.0.0.1:8000/", host: "************"
2025/06/29 11:47:58 [error] 2260#2260: *84 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /panel/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/panel/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php", host: "***********"
2025/06/29 11:48:00 [error] 2260#2260: *86 connect() failed (111: Connection refused) while connecting to upstream, client: *************, server: , request: "GET /public/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1", upstream: "http://127.0.0.1:8000/public/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php", host: "***********"
