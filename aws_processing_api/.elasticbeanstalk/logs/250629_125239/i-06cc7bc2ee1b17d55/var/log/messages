Jun 29 11:43:49 ip-172-31-27-8 kernel: Command line: BOOT_IMAGE=(hd0,gpt1)/boot/vmlinuz-6.1.141-155.222.amzn2023.x86_64 root=UUID=8ccb215f-5a99-42c1-8ecd-1a3ec537135b ro console=tty0 console=ttyS0,115200n8 nvme_core.io_timeout=********** rd.emergency=poweroff rd.shell=0 selinux=1 security=selinux quiet
Jun 29 11:43:49 ip-172-31-27-8 kernel: KASLR enabled
Jun 29 11:43:49 ip-172-31-27-8 kernel: BIOS-provided physical RAM map:
Jun 29 11:43:49 ip-172-31-27-8 kernel: BIOS-e820: [mem 0x0000000000000000-0x000000000009ffff] usable
Jun 29 11:43:49 ip-172-31-27-8 kernel: BIOS-e820: [mem 0x0000000000100000-0x00000000390cdfff] usable
Jun 29 11:43:49 ip-172-31-27-8 kernel: BIOS-e820: [mem 0x00000000390ce000-0x000000003934dfff] reserved
Jun 29 11:43:49 ip-172-31-27-8 kernel: BIOS-e820: [mem 0x000000003934e000-0x000000003935dfff] ACPI data
Jun 29 11:43:49 ip-172-31-27-8 kernel: BIOS-e820: [mem 0x000000003935e000-0x00000000393ddfff] ACPI NVS
Jun 29 11:43:49 ip-172-31-27-8 kernel: BIOS-e820: [mem 0x00000000393de000-0x000000003d37bfff] usable
Jun 29 11:43:49 ip-172-31-27-8 kernel: BIOS-e820: [mem 0x000000003d37c000-0x000000003d3fffff] reserved
Jun 29 11:43:49 ip-172-31-27-8 kernel: NX (Execute Disable) protection: active
Jun 29 11:43:49 ip-172-31-27-8 kernel: extended physical RAM map:
Jun 29 11:43:49 ip-172-31-27-8 kernel: reserve setup_data: [mem 0x0000000000000000-0x000000000009ffff] usable
Jun 29 11:43:49 ip-172-31-27-8 kernel: reserve setup_data: [mem 0x0000000000100000-0x00000000372be017] usable
Jun 29 11:43:49 ip-172-31-27-8 kernel: reserve setup_data: [mem 0x00000000372be018-0x00000000372c6e57] usable
Jun 29 11:43:49 ip-172-31-27-8 kernel: reserve setup_data: [mem 0x00000000372c6e58-0x00000000390cdfff] usable
Jun 29 11:43:49 ip-172-31-27-8 kernel: reserve setup_data: [mem 0x00000000390ce000-0x000000003934dfff] reserved
Jun 29 11:43:49 ip-172-31-27-8 kernel: reserve setup_data: [mem 0x000000003934e000-0x000000003935dfff] ACPI data
Jun 29 11:43:49 ip-172-31-27-8 kernel: reserve setup_data: [mem 0x000000003935e000-0x00000000393ddfff] ACPI NVS
Jun 29 11:43:49 ip-172-31-27-8 kernel: reserve setup_data: [mem 0x00000000393de000-0x000000003d37bfff] usable
Jun 29 11:43:49 ip-172-31-27-8 kernel: reserve setup_data: [mem 0x000000003d37c000-0x000000003d3fffff] reserved
Jun 29 11:43:49 ip-172-31-27-8 kernel: efi: EFI v2.70 by EDK II
Jun 29 11:43:49 ip-172-31-27-8 kernel: efi: SMBIOS=0x3926a000 ACPI=0x3935d000 ACPI 2.0=0x3935d014 MEMATTR=0x37a3ca98 
Jun 29 11:43:49 ip-172-31-27-8 kernel: SMBIOS 2.7 present.
Jun 29 11:43:49 ip-172-31-27-8 kernel: DMI: Amazon EC2 t3.micro/, BIOS 1.0 10/16/2017
Jun 29 11:43:49 ip-172-31-27-8 kernel: Hypervisor detected: KVM
Jun 29 11:43:49 ip-172-31-27-8 kernel: kvm-clock: Using msrs 4b564d01 and 4b564d00
Jun 29 11:43:49 ip-172-31-27-8 kernel: kvm-clock: using sched offset of 1638026718 cycles
Jun 29 11:43:49 ip-172-31-27-8 kernel: clocksource: kvm-clock: mask: 0xffffffffffffffff max_cycles: 0x1cd42e4dffb, max_idle_ns: 881590591483 ns
Jun 29 11:43:49 ip-172-31-27-8 kernel: tsc: Detected 2500.004 MHz processor
Jun 29 11:43:49 ip-172-31-27-8 kernel: last_pfn = 0x3d37c max_arch_pfn = 0x400000000
Jun 29 11:43:49 ip-172-31-27-8 kernel: x86/PAT: Configuration [0-7]: WB  WC  UC- UC  WB  WP  UC- WT  
Jun 29 11:43:49 ip-172-31-27-8 kernel: Using GB pages for direct mapping
Jun 29 11:43:49 ip-172-31-27-8 kernel: Secure boot disabled
Jun 29 11:43:49 ip-172-31-27-8 kernel: RAMDISK: [mem 0x330ef000-0x34097fff]
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: Early table checksum verification disabled
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: RSDP 0x000000003935D014 000024 (v02 AMAZON)
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: XSDT 0x000000003935C0E8 00006C (v01 AMAZON AMZNFACP 00000001      01000013)
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: FACP 0x0000000039355000 000114 (v01 AMAZON AMZNFACP 00000001 AMZN 00000001)
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: DSDT 0x0000000039356000 00115A (v01 AMAZON AMZNDSDT 00000001 AMZN 00000001)
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: FACS 0x00000000393D0000 000040
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: WAET 0x000000003935B000 000028 (v01 AMAZON AMZNWAET 00000001 AMZN 00000001)
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: SLIT 0x000000003935A000 00006C (v01 AMAZON AMZNSLIT 00000001 AMZN 00000001)
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: APIC 0x0000000039359000 000076 (v01 AMAZON AMZNAPIC 00000001 AMZN 00000001)
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: SRAT 0x0000000039358000 0000A0 (v01 AMAZON AMZNSRAT 00000001 AMZN 00000001)
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: HPET 0x0000000039354000 000038 (v01 AMAZON AMZNHPET 00000001 AMZN 00000001)
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: SSDT 0x0000000039353000 000759 (v01 AMAZON AMZNSSDT 00000001 AMZN 00000001)
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: SSDT 0x0000000039352000 00007F (v01 AMAZON AMZNSSDT 00000001 AMZN 00000001)
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: BGRT 0x0000000039351000 000038 (v01 AMAZON AMAZON   00000002      01000013)
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: Reserving FACP table memory at [mem 0x39355000-0x39355113]
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: Reserving DSDT table memory at [mem 0x39356000-0x39357159]
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: Reserving FACS table memory at [mem 0x393d0000-0x393d003f]
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: Reserving WAET table memory at [mem 0x3935b000-0x3935b027]
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: Reserving SLIT table memory at [mem 0x3935a000-0x3935a06b]
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: Reserving APIC table memory at [mem 0x39359000-0x39359075]
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: Reserving SRAT table memory at [mem 0x39358000-0x3935809f]
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: Reserving HPET table memory at [mem 0x39354000-0x39354037]
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: Reserving SSDT table memory at [mem 0x39353000-0x39353758]
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: Reserving SSDT table memory at [mem 0x39352000-0x3935207e]
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: Reserving BGRT table memory at [mem 0x39351000-0x39351037]
Jun 29 11:43:49 ip-172-31-27-8 kernel: SRAT: PXM 0 -> APIC 0x00 -> Node 0
Jun 29 11:43:49 ip-172-31-27-8 kernel: SRAT: PXM 0 -> APIC 0x01 -> Node 0
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: SRAT: Node 0 PXM 0 [mem 0x00000000-0x3fffffff]
Jun 29 11:43:49 ip-172-31-27-8 kernel: NODE_DATA(0) allocated [mem 0x3b2ca000-0x3b2f4fff]
Jun 29 11:43:49 ip-172-31-27-8 kernel: Zone ranges:
Jun 29 11:43:49 ip-172-31-27-8 kernel:  DMA      [mem 0x0000000000001000-0x0000000000ffffff]
Jun 29 11:43:49 ip-172-31-27-8 kernel:  DMA32    [mem 0x0000000001000000-0x000000003d37bfff]
Jun 29 11:43:49 ip-172-31-27-8 kernel:  Normal   empty
Jun 29 11:43:49 ip-172-31-27-8 kernel:  Device   empty
Jun 29 11:43:49 ip-172-31-27-8 kernel: Movable zone start for each node
Jun 29 11:43:49 ip-172-31-27-8 kernel: Early memory node ranges
Jun 29 11:43:49 ip-172-31-27-8 kernel:  node   0: [mem 0x0000000000001000-0x000000000009ffff]
Jun 29 11:43:49 ip-172-31-27-8 kernel:  node   0: [mem 0x0000000000100000-0x00000000390cdfff]
Jun 29 11:43:49 ip-172-31-27-8 kernel:  node   0: [mem 0x00000000393de000-0x000000003d37bfff]
Jun 29 11:43:49 ip-172-31-27-8 kernel: Initmem setup node 0 [mem 0x0000000000001000-0x000000003d37bfff]
Jun 29 11:43:49 ip-172-31-27-8 kernel: On node 0, zone DMA: 1 pages in unavailable ranges
Jun 29 11:43:49 ip-172-31-27-8 kernel: On node 0, zone DMA: 96 pages in unavailable ranges
Jun 29 11:43:49 ip-172-31-27-8 kernel: On node 0, zone DMA32: 784 pages in unavailable ranges
Jun 29 11:43:49 ip-172-31-27-8 kernel: On node 0, zone DMA32: 11396 pages in unavailable ranges
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: PM-Timer IO Port: 0xb008
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: LAPIC_NMI (acpi_id[0xff] dfl dfl lint[0x1])
Jun 29 11:43:49 ip-172-31-27-8 kernel: IOAPIC[0]: apic_id 0, version 32, address 0xfec00000, GSI 0-23
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: INT_SRC_OVR (bus 0 bus_irq 5 global_irq 5 high level)
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: INT_SRC_OVR (bus 0 bus_irq 9 global_irq 9 high level)
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: INT_SRC_OVR (bus 0 bus_irq 10 global_irq 10 high level)
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: INT_SRC_OVR (bus 0 bus_irq 11 global_irq 11 high level)
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: Using ACPI (MADT) for SMP configuration information
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: HPET id: 0x8086a201 base: 0xfed00000
Jun 29 11:43:49 ip-172-31-27-8 kernel: TSC deadline timer available
Jun 29 11:43:49 ip-172-31-27-8 kernel: smpboot: Allowing 2 CPUs, 0 hotplug CPUs
Jun 29 11:43:49 ip-172-31-27-8 kernel: PM: hibernation: Registered nosave memory: [mem 0x00000000-0x00000fff]
Jun 29 11:43:49 ip-172-31-27-8 kernel: PM: hibernation: Registered nosave memory: [mem 0x000a0000-0x000fffff]
Jun 29 11:43:49 ip-172-31-27-8 kernel: PM: hibernation: Registered nosave memory: [mem 0x390ce000-0x393ddfff]
Jun 29 11:43:49 ip-172-31-27-8 kernel: [mem 0x3d400000-0xffffffff] available for PCI devices
Jun 29 11:43:49 ip-172-31-27-8 kernel: Booting paravirtualized kernel on KVM
Jun 29 11:43:49 ip-172-31-27-8 kernel: clocksource: refined-jiffies: mask: 0xffffffff max_cycles: 0xffffffff, max_idle_ns: 19112604462750000 ns
Jun 29 11:43:49 ip-172-31-27-8 kernel: setup_percpu: NR_CPUS:8192 nr_cpumask_bits:2 nr_cpu_ids:2 nr_node_ids:1
Jun 29 11:43:49 ip-172-31-27-8 kernel: percpu: Embedded 60 pages/cpu s208896 r8192 d28672 u1048576
Jun 29 11:43:49 ip-172-31-27-8 kernel: kvm-guest: PV spinlocks enabled
Jun 29 11:43:49 ip-172-31-27-8 kernel: PV qspinlock hash table entries: 256 (order: 0, 4096 bytes, linear)
Jun 29 11:43:49 ip-172-31-27-8 kernel: Fallback order for Node 0: 0 
Jun 29 11:43:49 ip-172-31-27-8 kernel: Built 1 zonelists, mobility grouping on.  Total pages: 245790
Jun 29 11:43:49 ip-172-31-27-8 kernel: Policy zone: DMA32
Jun 29 11:43:49 ip-172-31-27-8 kernel: Kernel command line: BOOT_IMAGE=(hd0,gpt1)/boot/vmlinuz-6.1.141-155.222.amzn2023.x86_64 root=UUID=8ccb215f-5a99-42c1-8ecd-1a3ec537135b ro console=tty0 console=ttyS0,115200n8 nvme_core.io_timeout=********** rd.emergency=poweroff rd.shell=0 selinux=1 security=selinux quiet
Jun 29 11:43:49 ip-172-31-27-8 kernel: Unknown kernel command line parameters "BOOT_IMAGE=(hd0,gpt1)/boot/vmlinuz-6.1.141-155.222.amzn2023.x86_64", will be passed to user space.
Jun 29 11:43:49 ip-172-31-27-8 kernel: random: crng init done
Jun 29 11:43:49 ip-172-31-27-8 kernel: Dentry cache hash table entries: 131072 (order: 8, 1048576 bytes, linear)
Jun 29 11:43:49 ip-172-31-27-8 kernel: Inode-cache hash table entries: 65536 (order: 7, 524288 bytes, linear)
Jun 29 11:43:49 ip-172-31-27-8 kernel: mem auto-init: stack:off, heap alloc:off, heap free:off
Jun 29 11:43:49 ip-172-31-27-8 kernel: Memory: 199368K/999468K available (14347K kernel code, 9300K rwdata, 8892K rodata, 2448K init, 18696K bss, 141556K reserved, 0K cma-reserved)
Jun 29 11:43:49 ip-172-31-27-8 kernel: SLUB: HWalign=64, Order=0-3, MinObjects=0, CPUs=2, Nodes=1
Jun 29 11:43:49 ip-172-31-27-8 kernel: Kernel/User page tables isolation: enabled
Jun 29 11:43:49 ip-172-31-27-8 kernel: ftrace: allocating 41721 entries in 163 pages
Jun 29 11:43:49 ip-172-31-27-8 kernel: ftrace: allocated 163 pages with 4 groups
Jun 29 11:43:49 ip-172-31-27-8 kernel: Dynamic Preempt: none
Jun 29 11:43:49 ip-172-31-27-8 kernel: rcu: Preemptible hierarchical RCU implementation.
Jun 29 11:43:49 ip-172-31-27-8 kernel: rcu: #011RCU restricting CPUs from NR_CPUS=8192 to nr_cpu_ids=2.
Jun 29 11:43:49 ip-172-31-27-8 kernel: #011Trampoline variant of Tasks RCU enabled.
Jun 29 11:43:49 ip-172-31-27-8 kernel: #011Rude variant of Tasks RCU enabled.
Jun 29 11:43:49 ip-172-31-27-8 kernel: #011Tracing variant of Tasks RCU enabled.
Jun 29 11:43:49 ip-172-31-27-8 kernel: rcu: RCU calculated value of scheduler-enlistment delay is 10 jiffies.
Jun 29 11:43:49 ip-172-31-27-8 kernel: rcu: Adjusting geometry for rcu_fanout_leaf=16, nr_cpu_ids=2
Jun 29 11:43:49 ip-172-31-27-8 kernel: NR_IRQS: 524544, nr_irqs: 440, preallocated irqs: 16
Jun 29 11:43:49 ip-172-31-27-8 kernel: rcu: srcu_init: Setting srcu_struct sizes based on contention.
Jun 29 11:43:49 ip-172-31-27-8 kernel: Console: colour dummy device 80x25
Jun 29 11:43:49 ip-172-31-27-8 kernel: printk: console [tty0] enabled
Jun 29 11:43:49 ip-172-31-27-8 kernel: printk: console [ttyS0] enabled
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: Core revision ********
Jun 29 11:43:49 ip-172-31-27-8 kernel: clocksource: hpet: mask: 0xffffffff max_cycles: 0xffffffff, max_idle_ns: *********** ns
Jun 29 11:43:49 ip-172-31-27-8 kernel: APIC: Switch to symmetric I/O mode setup
Jun 29 11:43:49 ip-172-31-27-8 kernel: x2apic enabled
Jun 29 11:43:49 ip-172-31-27-8 kernel: Switched APIC routing to physical x2apic.
Jun 29 11:43:49 ip-172-31-27-8 kernel: clocksource: tsc-early: mask: 0xffffffffffffffff max_cycles: 0x24093d6e846, max_idle_ns: ************ ns
Jun 29 11:43:49 ip-172-31-27-8 kernel: Calibrating delay loop (skipped) preset value.. 5000.00 BogoMIPS (lpj=********)
Jun 29 11:43:49 ip-172-31-27-8 kernel: Last level iTLB entries: 4KB 64, 2MB 8, 4MB 8
Jun 29 11:43:49 ip-172-31-27-8 kernel: Last level dTLB entries: 4KB 64, 2MB 32, 4MB 32, 1GB 4
Jun 29 11:43:49 ip-172-31-27-8 kernel: Spectre V1 : Mitigation: usercopy/swapgs barriers and __user pointer sanitization
Jun 29 11:43:49 ip-172-31-27-8 kernel: Spectre V2 : Mitigation: Retpolines
Jun 29 11:43:49 ip-172-31-27-8 kernel: Spectre V2 : Spectre v2 / SpectreRSB: Filling RSB on context switch and VMEXIT
Jun 29 11:43:49 ip-172-31-27-8 kernel: RETBleed: WARNING: Spectre v2 mitigation leaves CPU vulnerable to RETBleed attacks, data leaks possible!
Jun 29 11:43:49 ip-172-31-27-8 kernel: RETBleed: Vulnerable
Jun 29 11:43:49 ip-172-31-27-8 kernel: Speculative Store Bypass: Vulnerable
Jun 29 11:43:49 ip-172-31-27-8 kernel: MDS: Vulnerable: Clear CPU buffers attempted, no microcode
Jun 29 11:43:49 ip-172-31-27-8 kernel: MMIO Stale Data: Vulnerable: Clear CPU buffers attempted, no microcode
Jun 29 11:43:49 ip-172-31-27-8 kernel: GDS: Unknown: Dependent on hypervisor status
Jun 29 11:43:49 ip-172-31-27-8 kernel: ITS: Mitigation: Aligned branch/return thunks
Jun 29 11:43:49 ip-172-31-27-8 kernel: x86/fpu: Supporting XSAVE feature 0x001: 'x87 floating point registers'
Jun 29 11:43:49 ip-172-31-27-8 kernel: x86/fpu: Supporting XSAVE feature 0x002: 'SSE registers'
Jun 29 11:43:49 ip-172-31-27-8 kernel: x86/fpu: Supporting XSAVE feature 0x004: 'AVX registers'
Jun 29 11:43:49 ip-172-31-27-8 kernel: x86/fpu: Supporting XSAVE feature 0x008: 'MPX bounds registers'
Jun 29 11:43:49 ip-172-31-27-8 kernel: x86/fpu: Supporting XSAVE feature 0x010: 'MPX CSR'
Jun 29 11:43:49 ip-172-31-27-8 kernel: x86/fpu: Supporting XSAVE feature 0x020: 'AVX-512 opmask'
Jun 29 11:43:49 ip-172-31-27-8 kernel: x86/fpu: Supporting XSAVE feature 0x040: 'AVX-512 Hi256'
Jun 29 11:43:49 ip-172-31-27-8 kernel: x86/fpu: Supporting XSAVE feature 0x080: 'AVX-512 ZMM_Hi256'
Jun 29 11:43:49 ip-172-31-27-8 kernel: x86/fpu: Supporting XSAVE feature 0x200: 'Protection Keys User registers'
Jun 29 11:43:49 ip-172-31-27-8 kernel: x86/fpu: xstate_offset[2]:  576, xstate_sizes[2]:  256
Jun 29 11:43:49 ip-172-31-27-8 kernel: x86/fpu: xstate_offset[3]:  832, xstate_sizes[3]:   64
Jun 29 11:43:49 ip-172-31-27-8 kernel: x86/fpu: xstate_offset[4]:  896, xstate_sizes[4]:   64
Jun 29 11:43:49 ip-172-31-27-8 kernel: x86/fpu: xstate_offset[5]:  960, xstate_sizes[5]:   64
Jun 29 11:43:49 ip-172-31-27-8 kernel: x86/fpu: xstate_offset[6]: 1024, xstate_sizes[6]:  512
Jun 29 11:43:49 ip-172-31-27-8 kernel: x86/fpu: xstate_offset[7]: 1536, xstate_sizes[7]: 1024
Jun 29 11:43:49 ip-172-31-27-8 kernel: x86/fpu: xstate_offset[9]: 2560, xstate_sizes[9]:    8
Jun 29 11:43:49 ip-172-31-27-8 kernel: x86/fpu: Enabled xstate features 0x2ff, context size is 2568 bytes, using 'compacted' format.
Jun 29 11:43:49 ip-172-31-27-8 kernel: Freeing SMP alternatives memory: 32K
Jun 29 11:43:49 ip-172-31-27-8 kernel: pid_max: default: 32768 minimum: 301
Jun 29 11:43:49 ip-172-31-27-8 kernel: LSM: Security Framework initializing
Jun 29 11:43:49 ip-172-31-27-8 kernel: Yama: becoming mindful.
Jun 29 11:43:49 ip-172-31-27-8 kernel: SELinux:  Initializing.
Jun 29 11:43:49 ip-172-31-27-8 kernel: LSM support for eBPF active
Jun 29 11:43:49 ip-172-31-27-8 kernel: Mount-cache hash table entries: 2048 (order: 2, 16384 bytes, linear)
Jun 29 11:43:49 ip-172-31-27-8 kernel: Mountpoint-cache hash table entries: 2048 (order: 2, 16384 bytes, linear)
Jun 29 11:43:49 ip-172-31-27-8 kernel: smpboot: CPU0: Intel(R) Xeon(R) Platinum 8259CL CPU @ 2.50GHz (family: 0x6, model: 0x55, stepping: 0x7)
Jun 29 11:43:49 ip-172-31-27-8 kernel: cblist_init_generic: Setting adjustable number of callback queues.
Jun 29 11:43:49 ip-172-31-27-8 kernel: cblist_init_generic: Setting shift to 1 and lim to 1.
Jun 29 11:43:49 ip-172-31-27-8 kernel: cblist_init_generic: Setting adjustable number of callback queues.
Jun 29 11:43:49 ip-172-31-27-8 kernel: cblist_init_generic: Setting shift to 1 and lim to 1.
Jun 29 11:43:49 ip-172-31-27-8 kernel: cblist_init_generic: Setting adjustable number of callback queues.
Jun 29 11:43:49 ip-172-31-27-8 kernel: cblist_init_generic: Setting shift to 1 and lim to 1.
Jun 29 11:43:49 ip-172-31-27-8 kernel: Performance Events: unsupported p6 CPU model 85 no PMU driver, software events only.
Jun 29 11:43:49 ip-172-31-27-8 kernel: signal: max sigframe size: 3632
Jun 29 11:43:49 ip-172-31-27-8 kernel: rcu: Hierarchical SRCU implementation.
Jun 29 11:43:49 ip-172-31-27-8 kernel: rcu: #011Max phase no-delay instances is 1000.
Jun 29 11:43:49 ip-172-31-27-8 kernel: smp: Bringing up secondary CPUs ...
Jun 29 11:43:49 ip-172-31-27-8 kernel: x86: Booting SMP configuration:
Jun 29 11:43:49 ip-172-31-27-8 kernel: .... node  #0, CPUs:      #1
Jun 29 11:43:49 ip-172-31-27-8 kernel: MDS CPU bug present and SMT on, data leak possible. See https://www.kernel.org/doc/html/latest/admin-guide/hw-vuln/mds.html for more details.
Jun 29 11:43:49 ip-172-31-27-8 kernel: MMIO Stale Data CPU bug present and SMT on, data leak possible. See https://www.kernel.org/doc/html/latest/admin-guide/hw-vuln/processor_mmio_stale_data.html for more details.
Jun 29 11:43:49 ip-172-31-27-8 kernel: smp: Brought up 1 node, 2 CPUs
Jun 29 11:43:49 ip-172-31-27-8 kernel: smpboot: Max logical packages: 1
Jun 29 11:43:49 ip-172-31-27-8 kernel: smpboot: Total of 2 processors activated (10000.01 BogoMIPS)
Jun 29 11:43:49 ip-172-31-27-8 kernel: node 0 deferred pages initialised in 0ms
Jun 29 11:43:49 ip-172-31-27-8 kernel: devtmpfs: initialized
Jun 29 11:43:49 ip-172-31-27-8 kernel: x86/mm: Memory block size: 128MB
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: PM: Registering ACPI NVS region [mem 0x3935e000-0x393ddfff] (524288 bytes)
Jun 29 11:43:49 ip-172-31-27-8 kernel: clocksource: jiffies: mask: 0xffffffff max_cycles: 0xffffffff, max_idle_ns: 19112604462750000 ns
Jun 29 11:43:49 ip-172-31-27-8 kernel: futex hash table entries: 512 (order: 3, 32768 bytes, linear)
Jun 29 11:43:49 ip-172-31-27-8 kernel: NET: Registered PF_NETLINK/PF_ROUTE protocol family
Jun 29 11:43:49 ip-172-31-27-8 kernel: DMA: preallocated 128 KiB GFP_KERNEL pool for atomic allocations
Jun 29 11:43:49 ip-172-31-27-8 kernel: DMA: preallocated 128 KiB GFP_KERNEL|GFP_DMA pool for atomic allocations
Jun 29 11:43:49 ip-172-31-27-8 kernel: DMA: preallocated 128 KiB GFP_KERNEL|GFP_DMA32 pool for atomic allocations
Jun 29 11:43:49 ip-172-31-27-8 kernel: audit: initializing netlink subsys (disabled)
Jun 29 11:43:49 ip-172-31-27-8 kernel: audit: type=2000 audit(1751197430.016:1): state=initialized audit_enabled=0 res=1
Jun 29 11:43:49 ip-172-31-27-8 kernel: thermal_sys: Registered thermal governor 'fair_share'
Jun 29 11:43:49 ip-172-31-27-8 kernel: thermal_sys: Registered thermal governor 'step_wise'
Jun 29 11:43:49 ip-172-31-27-8 kernel: thermal_sys: Registered thermal governor 'user_space'
Jun 29 11:43:49 ip-172-31-27-8 kernel: cpuidle: using governor ladder
Jun 29 11:43:49 ip-172-31-27-8 kernel: cpuidle: using governor menu
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpiphp: ACPI Hot Plug PCI Controller Driver version: 0.5
Jun 29 11:43:49 ip-172-31-27-8 kernel: PCI: Using configuration type 1 for base access
Jun 29 11:43:49 ip-172-31-27-8 kernel: kprobes: kprobe jump-optimization is enabled. All kprobes are optimized if possible.
Jun 29 11:43:49 ip-172-31-27-8 kernel: HugeTLB: registered 1.00 GiB page size, pre-allocated 0 pages
Jun 29 11:43:49 ip-172-31-27-8 kernel: HugeTLB: 16380 KiB vmemmap can be freed for a 1.00 GiB page
Jun 29 11:43:49 ip-172-31-27-8 kernel: HugeTLB: registered 2.00 MiB page size, pre-allocated 0 pages
Jun 29 11:43:49 ip-172-31-27-8 kernel: HugeTLB: 28 KiB vmemmap can be freed for a 2.00 MiB page
Jun 29 11:43:49 ip-172-31-27-8 kernel: cryptd: max_cpu_qlen set to 1000
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: Added _OSI(Module Device)
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: Added _OSI(Processor Device)
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: Added _OSI(3.0 _SCP Extensions)
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: Added _OSI(Processor Aggregator Device)
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: 3 ACPI AML tables successfully acquired and loaded
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: Interpreter enabled
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: PM: (supports S0 S4 S5)
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: Using IOAPIC for interrupt routing
Jun 29 11:43:49 ip-172-31-27-8 kernel: PCI: Using host bridge windows from ACPI; if necessary, use "pci=nocrs" and report a bug
Jun 29 11:43:49 ip-172-31-27-8 kernel: PCI: Using E820 reservations for host bridge windows
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: Enabled 2 GPEs in block 00 to 0F
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: PCI Root Bridge [PCI0] (domain 0000 [bus 00-ff])
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpi PNP0A03:00: _OSC: OS supports [ASPM ClockPM Segments MSI HPX-Type3]
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpi PNP0A03:00: _OSC: not requesting OS control; OS requires [ExtendedConfig ASPM ClockPM MSI]
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpi PNP0A03:00: fail to add MMCONFIG information, can't access extended PCI configuration space under this bridge.
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpiphp: Slot [3] registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpiphp: Slot [4] registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpiphp: Slot [5] registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpiphp: Slot [6] registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpiphp: Slot [7] registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpiphp: Slot [8] registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpiphp: Slot [9] registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpiphp: Slot [10] registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpiphp: Slot [11] registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpiphp: Slot [12] registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpiphp: Slot [13] registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpiphp: Slot [14] registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpiphp: Slot [15] registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpiphp: Slot [16] registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpiphp: Slot [17] registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpiphp: Slot [18] registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpiphp: Slot [19] registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpiphp: Slot [20] registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpiphp: Slot [21] registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpiphp: Slot [22] registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpiphp: Slot [23] registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpiphp: Slot [24] registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpiphp: Slot [25] registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpiphp: Slot [26] registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpiphp: Slot [27] registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpiphp: Slot [28] registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpiphp: Slot [29] registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpiphp: Slot [30] registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: acpiphp: Slot [31] registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: PCI host bridge to bus 0000:00
Jun 29 11:43:49 ip-172-31-27-8 kernel: pci_bus 0000:00: root bus resource [io  0x0000-0x0cf7 window]
Jun 29 11:43:49 ip-172-31-27-8 kernel: pci_bus 0000:00: root bus resource [io  0x0d00-0xffff window]
Jun 29 11:43:49 ip-172-31-27-8 kernel: pci_bus 0000:00: root bus resource [mem 0x000a0000-0x000bffff window]
Jun 29 11:43:49 ip-172-31-27-8 kernel: pci_bus 0000:00: root bus resource [mem 0x80000000-0xfebfffff window]
Jun 29 11:43:49 ip-172-31-27-8 kernel: pci_bus 0000:00: root bus resource [mem 0x100000000-0x2000ffffffff window]
Jun 29 11:43:49 ip-172-31-27-8 kernel: pci_bus 0000:00: root bus resource [bus 00-ff]
Jun 29 11:43:49 ip-172-31-27-8 kernel: pci 0000:00:00.0: [8086:1237] type 00 class 0x060000
Jun 29 11:43:49 ip-172-31-27-8 kernel: pci 0000:00:01.0: [8086:7000] type 00 class 0x060100
Jun 29 11:43:49 ip-172-31-27-8 kernel: pci 0000:00:01.3: [8086:7113] type 00 class 0x000000
Jun 29 11:43:49 ip-172-31-27-8 kernel: pci 0000:00:01.3: quirk: [io  0xb000-0xb03f] claimed by PIIX4 ACPI
Jun 29 11:43:49 ip-172-31-27-8 kernel: pci 0000:00:01.3: PIIX4 devres E PIO at fff0-ffff
Jun 29 11:43:49 ip-172-31-27-8 kernel: pci 0000:00:01.3: PIIX4 devres F MMIO at ffc00000-ffffffff
Jun 29 11:43:49 ip-172-31-27-8 kernel: pci 0000:00:01.3: PIIX4 devres G PIO at fff0-ffff
Jun 29 11:43:49 ip-172-31-27-8 kernel: pci 0000:00:01.3: PIIX4 devres H MMIO at ffc00000-ffffffff
Jun 29 11:43:49 ip-172-31-27-8 kernel: pci 0000:00:01.3: PIIX4 devres I PIO at fff0-ffff
Jun 29 11:43:49 ip-172-31-27-8 kernel: pci 0000:00:01.3: PIIX4 devres J PIO at fff0-ffff
Jun 29 11:43:49 ip-172-31-27-8 kernel: pci 0000:00:03.0: [1d0f:1111] type 00 class 0x030000
Jun 29 11:43:49 ip-172-31-27-8 kernel: pci 0000:00:03.0: reg 0x10: [mem 0x80000000-0x803fffff pref]
Jun 29 11:43:49 ip-172-31-27-8 kernel: pci 0000:00:03.0: reg 0x30: [mem 0xffff0000-0xffffffff pref]
Jun 29 11:43:49 ip-172-31-27-8 kernel: pci 0000:00:03.0: Video device with shadowed ROM at [mem 0x000c0000-0x000dffff]
Jun 29 11:43:49 ip-172-31-27-8 kernel: pci 0000:00:04.0: [1d0f:8061] type 00 class 0x010802
Jun 29 11:43:49 ip-172-31-27-8 kernel: pci 0000:00:04.0: reg 0x10: [mem 0x80404000-0x80407fff]
Jun 29 11:43:49 ip-172-31-27-8 kernel: pci 0000:00:05.0: [1d0f:ec20] type 00 class 0x020000
Jun 29 11:43:49 ip-172-31-27-8 kernel: pci 0000:00:05.0: reg 0x10: [mem 0x80400000-0x80403fff]
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: PCI: Interrupt link LNKA configured for IRQ 10
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: PCI: Interrupt link LNKB configured for IRQ 10
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: PCI: Interrupt link LNKC configured for IRQ 11
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: PCI: Interrupt link LNKD configured for IRQ 11
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: PCI: Interrupt link LNKS configured for IRQ 9
Jun 29 11:43:49 ip-172-31-27-8 kernel: iommu: Default domain type: Translated 
Jun 29 11:43:49 ip-172-31-27-8 kernel: iommu: DMA domain TLB invalidation policy: lazy mode 
Jun 29 11:43:49 ip-172-31-27-8 kernel: pps_core: LinuxPPS API ver. 1 registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: pps_core: Software ver. 5.3.6 - Copyright 2005-2007 Rodolfo Giometti <<EMAIL>>
Jun 29 11:43:49 ip-172-31-27-8 kernel: PTP clock support registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: EDAC MC: Ver: 3.0.0
Jun 29 11:43:49 ip-172-31-27-8 kernel: Registered efivars operations
Jun 29 11:43:49 ip-172-31-27-8 kernel: NetLabel: Initializing
Jun 29 11:43:49 ip-172-31-27-8 kernel: NetLabel:  domain hash size = 128
Jun 29 11:43:49 ip-172-31-27-8 kernel: NetLabel:  protocols = UNLABELED CIPSOv4 CALIPSO
Jun 29 11:43:49 ip-172-31-27-8 kernel: NetLabel:  unlabeled traffic allowed by default
Jun 29 11:43:49 ip-172-31-27-8 kernel: PCI: Using ACPI for IRQ routing
Jun 29 11:43:49 ip-172-31-27-8 kernel: hpet0: at MMIO 0xfed00000, IRQs 2, 8, 0, 0, 0, 0, 0, 0
Jun 29 11:43:49 ip-172-31-27-8 kernel: hpet0: 8 comparators, 32-bit 62.500000 MHz counter
Jun 29 11:43:49 ip-172-31-27-8 kernel: clocksource: Switched to clocksource kvm-clock
Jun 29 11:43:49 ip-172-31-27-8 kernel: VFS: Disk quotas dquot_6.6.0
Jun 29 11:43:49 ip-172-31-27-8 kernel: VFS: Dquot-cache hash table entries: 512 (order 0, 4096 bytes)
Jun 29 11:43:49 ip-172-31-27-8 kernel: pnp: PnP ACPI init
Jun 29 11:43:49 ip-172-31-27-8 kernel: pnp: PnP ACPI: found 5 devices
Jun 29 11:43:49 ip-172-31-27-8 kernel: clocksource: acpi_pm: mask: 0xffffff max_cycles: 0xffffff, max_idle_ns: ********** ns
Jun 29 11:43:49 ip-172-31-27-8 kernel: NET: Registered PF_INET protocol family
Jun 29 11:43:49 ip-172-31-27-8 kernel: IP idents hash table entries: 16384 (order: 5, 131072 bytes, linear)
Jun 29 11:43:49 ip-172-31-27-8 kernel: tcp_listen_portaddr_hash hash table entries: 512 (order: 1, 8192 bytes, linear)
Jun 29 11:43:49 ip-172-31-27-8 kernel: Table-perturb hash table entries: 65536 (order: 6, 262144 bytes, linear)
Jun 29 11:43:49 ip-172-31-27-8 kernel: TCP established hash table entries: 8192 (order: 4, 65536 bytes, linear)
Jun 29 11:43:49 ip-172-31-27-8 kernel: TCP bind hash table entries: 8192 (order: 6, 262144 bytes, linear)
Jun 29 11:43:49 ip-172-31-27-8 kernel: TCP: Hash tables configured (established 8192 bind 8192)
Jun 29 11:43:49 ip-172-31-27-8 kernel: MPTCP token hash table entries: 1024 (order: 2, 24576 bytes, linear)
Jun 29 11:43:49 ip-172-31-27-8 kernel: UDP hash table entries: 512 (order: 2, 16384 bytes, linear)
Jun 29 11:43:49 ip-172-31-27-8 kernel: UDP-Lite hash table entries: 512 (order: 2, 16384 bytes, linear)
Jun 29 11:43:49 ip-172-31-27-8 kernel: NET: Registered PF_UNIX/PF_LOCAL protocol family
Jun 29 11:43:49 ip-172-31-27-8 kernel: NET: Registered PF_XDP protocol family
Jun 29 11:43:49 ip-172-31-27-8 kernel: pci_bus 0000:00: resource 4 [io  0x0000-0x0cf7 window]
Jun 29 11:43:49 ip-172-31-27-8 kernel: pci_bus 0000:00: resource 5 [io  0x0d00-0xffff window]
Jun 29 11:43:49 ip-172-31-27-8 kernel: pci_bus 0000:00: resource 6 [mem 0x000a0000-0x000bffff window]
Jun 29 11:43:49 ip-172-31-27-8 kernel: pci_bus 0000:00: resource 7 [mem 0x80000000-0xfebfffff window]
Jun 29 11:43:49 ip-172-31-27-8 kernel: pci_bus 0000:00: resource 8 [mem 0x100000000-0x2000ffffffff window]
Jun 29 11:43:49 ip-172-31-27-8 kernel: pci 0000:00:00.0: Limiting direct PCI/PCI transfers
Jun 29 11:43:49 ip-172-31-27-8 kernel: PCI: CLS 0 bytes, default 64
Jun 29 11:43:49 ip-172-31-27-8 kernel: clocksource: tsc: mask: 0xffffffffffffffff max_cycles: 0x24093d6e846, max_idle_ns: ************ ns
Jun 29 11:43:49 ip-172-31-27-8 kernel: Trying to unpack rootfs image as initramfs...
Jun 29 11:43:49 ip-172-31-27-8 kernel: clocksource: Switched to clocksource tsc
Jun 29 11:43:49 ip-172-31-27-8 kernel: Initialise system trusted keyrings
Jun 29 11:43:49 ip-172-31-27-8 kernel: Key type blacklist registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: workingset: timestamp_bits=36 max_order=18 bucket_order=0
Jun 29 11:43:49 ip-172-31-27-8 kernel: zbud: loaded
Jun 29 11:43:49 ip-172-31-27-8 kernel: SGI XFS with ACLs, security attributes, quota, no debug enabled
Jun 29 11:43:49 ip-172-31-27-8 kernel: integrity: Platform Keyring initialized
Jun 29 11:43:49 ip-172-31-27-8 kernel: Key type asymmetric registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: Asymmetric key parser 'x509' registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: Block layer SCSI generic (bsg) driver version 0.4 loaded (major 248)
Jun 29 11:43:49 ip-172-31-27-8 kernel: io scheduler mq-deadline registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: io scheduler kyber registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: io scheduler bfq registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: shpchp: Standard Hot Plug PCI Controller Driver version: 0.4
Jun 29 11:43:49 ip-172-31-27-8 kernel: Serial: 8250/16550 driver, 4 ports, IRQ sharing disabled
Jun 29 11:43:49 ip-172-31-27-8 kernel: 00:04: ttyS0 at I/O 0x3f8 (irq = 4, base_baud = 115200) is a 16550A
Jun 29 11:43:49 ip-172-31-27-8 kernel: AMD-Vi: AMD IOMMUv2 functionality not available on this system - This is not a bug.
Jun 29 11:43:49 ip-172-31-27-8 kernel: nvme nvme0: pci function 0000:00:04.0
Jun 29 11:43:49 ip-172-31-27-8 kernel: rtc_cmos 00:00: RTC can wake from S4
Jun 29 11:43:49 ip-172-31-27-8 kernel: ACPI: \_SB_.LNKD: Enabled at IRQ 11
Jun 29 11:43:49 ip-172-31-27-8 kernel: rtc_cmos 00:00: registered as rtc0
Jun 29 11:43:49 ip-172-31-27-8 kernel: rtc_cmos 00:00: setting system clock to 2025-06-29T11:43:49 UTC (1751197429)
Jun 29 11:43:49 ip-172-31-27-8 kernel: rtc_cmos 00:00: alarms up to one day, 114 bytes nvram
Jun 29 11:43:49 ip-172-31-27-8 kernel: intel_pstate: CPU model not supported
Jun 29 11:43:49 ip-172-31-27-8 kernel: pstore: Registered efi as persistent store backend
Jun 29 11:43:49 ip-172-31-27-8 kernel: hid: raw HID events driver (C) Jiri Kosina
Jun 29 11:43:49 ip-172-31-27-8 kernel: NET: Registered PF_INET6 protocol family
Jun 29 11:43:49 ip-172-31-27-8 kernel: nvme nvme0: 2/0/0 default/read/poll queues
Jun 29 11:43:49 ip-172-31-27-8 kernel: nvme0n1: p1 p127 p128
Jun 29 11:43:49 ip-172-31-27-8 kernel: Freeing initrd memory: 16036K
Jun 29 11:43:49 ip-172-31-27-8 kernel: Segment Routing with IPv6
Jun 29 11:43:49 ip-172-31-27-8 kernel: In-situ OAM (IOAM) with IPv6
Jun 29 11:43:49 ip-172-31-27-8 kernel: NET: Registered PF_PACKET protocol family
Jun 29 11:43:49 ip-172-31-27-8 kernel: IPI shorthand broadcast: enabled
Jun 29 11:43:49 ip-172-31-27-8 kernel: AVX2 version of gcm_enc/dec engaged.
Jun 29 11:43:49 ip-172-31-27-8 kernel: AES CTR mode by8 optimization enabled
Jun 29 11:43:49 ip-172-31-27-8 kernel: sched_clock: Marking stable (*********, 2922630)->(*********, -********)
Jun 29 11:43:49 ip-172-31-27-8 kernel: registered taskstats version 1
Jun 29 11:43:49 ip-172-31-27-8 kernel: Loading compiled-in X.509 certificates
Jun 29 11:43:49 ip-172-31-27-8 kernel: Loaded X.509 cert 'Amazon.com: Amazon Linux Kernel Signing Key: e5aa2cdeac48a9379f3ce85cd99ee647397366ad'
Jun 29 11:43:49 ip-172-31-27-8 kernel: zswap: loaded using pool lzo/zbud
Jun 29 11:43:49 ip-172-31-27-8 kernel: Key type .fscrypt registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: Key type fscrypt-provisioning registered
Jun 29 11:43:49 ip-172-31-27-8 kernel: pstore: Using crash dump compression: deflate
Jun 29 11:43:49 ip-172-31-27-8 kernel: ima: No TPM chip found, activating TPM-bypass!
Jun 29 11:43:49 ip-172-31-27-8 kernel: ima: Allocated hash algorithm: sha256
Jun 29 11:43:49 ip-172-31-27-8 kernel: ima: No architecture policies found
Jun 29 11:43:49 ip-172-31-27-8 kernel: clk: Disabling unused clocks
Jun 29 11:43:49 ip-172-31-27-8 kernel: Freeing unused decrypted memory: 2036K
Jun 29 11:43:49 ip-172-31-27-8 kernel: Freeing unused kernel image (initmem) memory: 2448K
Jun 29 11:43:49 ip-172-31-27-8 kernel: Write protecting the kernel read-only data: 26624k
Jun 29 11:43:49 ip-172-31-27-8 kernel: Freeing unused kernel image (text/rodata gap) memory: 2036K
Jun 29 11:43:49 ip-172-31-27-8 kernel: Freeing unused kernel image (rodata/data gap) memory: 1348K
Jun 29 11:43:49 ip-172-31-27-8 kernel: Run /init as init process
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: systemd 252.23-3.amzn2023 running in system mode (+PAM +AUDIT +SELINUX -APPARMOR +IMA +SMACK +SECCOMP -GCRYPT -GNUTLS +OPENSSL +ACL +BLKID +CURL +ELFUTILS +FIDO2 +IDN2 -IDN -IPTC +KMOD +LIBCRYPTSETUP +LIBFDISK +PCRE2 +PWQUALITY +P11KIT +QRENCODE +TPM2 -BZIP2 -LZ4 +XZ -ZLIB -ZSTD +BPF_FRAMEWORK +XKBCOMMON +UTMP +SYSVINIT default-hierarchy=unified)
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Detected virtualization amazon.
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Detected architecture x86-64.
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Running in initrd.
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: No hostname configured, using default hostname.
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Hostname set to <localhost>.
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Initializing machine ID from VM UUID.
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Queued start job for default target initrd.target.
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Started systemd-ask-password-console.path - Dispatch Password Requests to Console Directory Watch.
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Expecting device dev-disk-by\x2duuid-8ccb215f\x2d5a99\x2d42c1\x2d8ecd\x2d1a3ec537135b.device - /dev/disk/by-uuid/8ccb215f-5a99-42c1-8ecd-1a3ec537135b...
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Reached target initrd-usr-fs.target - Initrd /usr File System.
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Reached target local-fs.target - Local File Systems.
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Reached target paths.target - Path Units.
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Reached target slices.target - Slice Units.
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Reached target swap.target - Swaps.
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Reached target timers.target - Timer Units.
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Listening on systemd-journald-audit.socket - Journal Audit Socket.
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Listening on systemd-journald-dev-log.socket - Journal Socket (/dev/log).
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Listening on systemd-journald.socket - Journal Socket.
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Listening on systemd-udevd-control.socket - udev Control Socket.
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Listening on systemd-udevd-kernel.socket - udev Kernel Socket.
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Reached target sockets.target - Socket Units.
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: kmod-static-nodes.service - Create List of Static Device Nodes was skipped because of an unmet condition check (ConditionFileNotEmpty=/lib/modules/6.1.141-155.222.amzn2023.x86_64/modules.devname).
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Starting systemd-journald.service - Journal Service...
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: systemd-modules-load.service - Load Kernel Modules was skipped because no trigger condition checks were met.
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Starting systemd-sysctl.service - Apply Kernel Variables...
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Starting systemd-sysusers.service - Create System Users...
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Starting systemd-vconsole-setup.service - Setup Virtual Console...
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Finished systemd-vconsole-setup.service - Setup Virtual Console.
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Finished systemd-sysusers.service - Create System Users.
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: dracut-cmdline-ask.service - dracut ask for additional cmdline parameters was skipped because no trigger condition checks were met.
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Starting dracut-cmdline.service - dracut cmdline hook...
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Starting systemd-tmpfiles-setup-dev.service - Create Static Device Nodes in /dev...
Jun 29 11:43:49 ip-172-31-27-8 kernel: audit: type=1130 audit(1751197429.940:2): pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=systemd-vconsole-setup comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:49 ip-172-31-27-8 kernel: audit: type=1130 audit(1751197429.940:3): pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=systemd-sysusers comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:49 ip-172-31-27-8 systemd-journald[341]: Journal started
Jun 29 11:43:49 ip-172-31-27-8 systemd-journald[341]: Runtime Journal (/run/log/journal/ec26afa817b58edf0317f158f67e8472) is 2.2M, max 18.0M, 15.8M free.
Jun 29 11:43:49 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=systemd-vconsole-setup comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:49 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=systemd-sysusers comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:49 ip-172-31-27-8 systemd-sysusers[343]: Creating group 'nobody' with GID 65534.
Jun 29 11:43:49 ip-172-31-27-8 systemd-sysusers[343]: Creating group 'users' with GID 100.
Jun 29 11:43:49 ip-172-31-27-8 systemd-sysusers[343]: Creating group 'systemd-journal' with GID 190.
Jun 29 11:43:49 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=systemd-journald comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Starting systemd-tmpfiles-setup.service - Create Volatile Files and Directories...
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Started systemd-journald.service - Journal Service.
Jun 29 11:43:49 ip-172-31-27-8 kernel: audit: type=1130 audit(1751197429.950:4): pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=systemd-journald comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:49 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=systemd-sysctl comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Finished systemd-sysctl.service - Apply Kernel Variables.
Jun 29 11:43:49 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=systemd-tmpfiles-setup-dev comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Finished systemd-tmpfiles-setup-dev.service - Create Static Device Nodes in /dev.
Jun 29 11:43:49 ip-172-31-27-8 systemd-tmpfiles[353]: /usr/lib/tmpfiles.d/var.conf:14: Duplicate line for path "/var/log", ignoring.
Jun 29 11:43:49 ip-172-31-27-8 kernel: audit: type=1130 audit(1751197429.960:5): pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=systemd-sysctl comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:49 ip-172-31-27-8 kernel: audit: type=1130 audit(1751197429.960:6): pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=systemd-tmpfiles-setup-dev comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:49 ip-172-31-27-8 systemd[1]: Finished systemd-tmpfiles-setup.service - Create Volatile Files and Directories.
Jun 29 11:43:49 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=systemd-tmpfiles-setup comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:49 ip-172-31-27-8 dracut-cmdline[348]: dracut-102-3.amzn2023.0.1
Jun 29 11:43:49 ip-172-31-27-8 dracut-cmdline[348]: Using kernel command line parameters:    BOOT_IMAGE=(hd0,gpt1)/boot/vmlinuz-6.1.141-155.222.amzn2023.x86_64 root=UUID=8ccb215f-5a99-42c1-8ecd-1a3ec537135b ro console=tty0 console=ttyS0,115200n8 nvme_core.io_timeout=********** rd.emergency=poweroff rd.shell=0 selinux=1 security=selinux quiet
Jun 29 11:43:49 ip-172-31-27-8 kernel: audit: type=1130 audit(1751197429.970:7): pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=systemd-tmpfiles-setup comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Finished dracut-cmdline.service - dracut cmdline hook.
Jun 29 11:43:50 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=dracut-cmdline comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: dracut-pre-udev.service - dracut pre-udev hook was skipped because no trigger condition checks were met.
Jun 29 11:43:50 ip-172-31-27-8 audit: BPF prog-id=6 op=LOAD
Jun 29 11:43:50 ip-172-31-27-8 audit: BPF prog-id=7 op=LOAD
Jun 29 11:43:50 ip-172-31-27-8 kernel: audit: type=1130 audit(1751197430.000:8): pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=dracut-cmdline comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:50 ip-172-31-27-8 kernel: audit: type=1334 audit(1751197430.000:9): prog-id=6 op=LOAD
Jun 29 11:43:50 ip-172-31-27-8 kernel: audit: type=1334 audit(1751197430.000:10): prog-id=7 op=LOAD
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Starting systemd-udevd.service - Rule-based Manager for Device Events and Files...
Jun 29 11:43:50 ip-172-31-27-8 systemd-udevd[405]: Using default interface naming scheme 'v252'.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Started systemd-udevd.service - Rule-based Manager for Device Events and Files.
Jun 29 11:43:50 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=systemd-udevd comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Starting dracut-pre-trigger.service - dracut pre-trigger hook...
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Finished dracut-pre-trigger.service - dracut pre-trigger hook.
Jun 29 11:43:50 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=dracut-pre-trigger comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Starting systemd-udev-trigger.service - Coldplug All udev Devices...
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Finished systemd-udev-trigger.service - Coldplug All udev Devices.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Reached target sysinit.target - System Initialization.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Reached target basic.target - Basic System.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: dracut-initqueue.service - dracut initqueue hook was skipped because no trigger condition checks were met.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Reached target remote-fs-pre.target - Preparation for Remote File Systems.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Reached target remote-fs.target - Remote File Systems.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: dracut-pre-mount.service - dracut pre-mount hook was skipped because no trigger condition checks were met.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Started rngd.service - Hardware RNG Entropy Gatherer Daemon.
Jun 29 11:43:50 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=systemd-udev-trigger comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:50 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=rngd comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:50 ip-172-31-27-8 rngd[675]: Disabling 7: PKCS11 Entropy generator (pkcs11)
Jun 29 11:43:50 ip-172-31-27-8 rngd[675]: Disabling 5: NIST Network Entropy Beacon (nist)
Jun 29 11:43:50 ip-172-31-27-8 rngd[675]: Initializing available sources
Jun 29 11:43:50 ip-172-31-27-8 rngd[675]: [hwrng ]: Initialization Failed
Jun 29 11:43:50 ip-172-31-27-8 rngd[675]: [rdrand]: Enabling RDSEED rng support
Jun 29 11:43:50 ip-172-31-27-8 rngd[675]: [rdrand]: Initialized
Jun 29 11:43:50 ip-172-31-27-8 rngd[675]: [jitter]: Initializing AES buffer
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Found device dev-disk-by\x2duuid-8ccb215f\x2d5a99\x2d42c1\x2d8ecd\x2d1a3ec537135b.device - Amazon Elastic Block Store /.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Reached target initrd-root-device.target - Initrd Root Device.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Mounting sysroot.mount - /sysroot...
Jun 29 11:43:50 ip-172-31-27-8 kernel: XFS (nvme0n1p1): Mounting V5 Filesystem
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Mounted sysroot.mount - /sysroot.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Reached target initrd-root-fs.target - Initrd Root File System.
Jun 29 11:43:50 ip-172-31-27-8 kernel: XFS (nvme0n1p1): Ending clean mount
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Starting initrd-parse-etc.service - Mountpoints Configured in the Real Root...
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: initrd-parse-etc.service: Deactivated successfully.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Finished initrd-parse-etc.service - Mountpoints Configured in the Real Root.
Jun 29 11:43:50 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=initrd-parse-etc comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:50 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=initrd-parse-etc comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Reached target initrd-fs.target - Initrd File Systems.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Reached target initrd.target - Initrd Default Target.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: dracut-mount.service - dracut mount hook was skipped because no trigger condition checks were met.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: dracut-pre-pivot.service - dracut pre-pivot and cleanup hook was skipped because no trigger condition checks were met.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Starting initrd-cleanup.service - Cleaning Up and Shutting Down Daemons...
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Stopped target initrd.target - Initrd Default Target.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Stopped target initrd-root-device.target - Initrd Root Device.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Stopped target initrd-usr-fs.target - Initrd /usr File System.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Stopped target remote-fs.target - Remote File Systems.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Stopped target remote-fs-pre.target - Preparation for Remote File Systems.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Stopped target timers.target - Timer Units.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: dracut-cmdline.service: Deactivated successfully.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Stopped dracut-cmdline.service - dracut cmdline hook.
Jun 29 11:43:50 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=dracut-cmdline comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Stopping rngd.service - Hardware RNG Entropy Gatherer Daemon...
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: systemd-vconsole-setup.service: Deactivated successfully.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Stopped systemd-vconsole-setup.service - Setup Virtual Console.
Jun 29 11:43:50 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=systemd-vconsole-setup comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: rngd.service: Deactivated successfully.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Stopped rngd.service - Hardware RNG Entropy Gatherer Daemon.
Jun 29 11:43:50 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=rngd comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Stopped target basic.target - Basic System.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Stopped target paths.target - Path Units.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: systemd-ask-password-console.path: Deactivated successfully.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Stopped systemd-ask-password-console.path - Dispatch Password Requests to Console Directory Watch.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Stopped target slices.target - Slice Units.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Stopped target sockets.target - Socket Units.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Stopped target sysinit.target - System Initialization.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Stopped target swap.target - Swaps.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: systemd-sysctl.service: Deactivated successfully.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Stopped systemd-sysctl.service - Apply Kernel Variables.
Jun 29 11:43:50 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=systemd-sysctl comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: systemd-tmpfiles-setup.service: Deactivated successfully.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Stopped systemd-tmpfiles-setup.service - Create Volatile Files and Directories.
Jun 29 11:43:50 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=systemd-tmpfiles-setup comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Stopped target local-fs.target - Local File Systems.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: systemd-udev-trigger.service: Deactivated successfully.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Stopped systemd-udev-trigger.service - Coldplug All udev Devices.
Jun 29 11:43:50 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=systemd-udev-trigger comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: dracut-pre-trigger.service: Deactivated successfully.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Stopped dracut-pre-trigger.service - dracut pre-trigger hook.
Jun 29 11:43:50 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=dracut-pre-trigger comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Stopping systemd-udevd.service - Rule-based Manager for Device Events and Files...
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: initrd-cleanup.service: Deactivated successfully.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Finished initrd-cleanup.service - Cleaning Up and Shutting Down Daemons.
Jun 29 11:43:50 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=initrd-cleanup comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:50 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=initrd-cleanup comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: systemd-udevd.service: Deactivated successfully.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Stopped systemd-udevd.service - Rule-based Manager for Device Events and Files.
Jun 29 11:43:50 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=systemd-udevd comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: systemd-udevd-control.socket: Deactivated successfully.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Closed systemd-udevd-control.socket - udev Control Socket.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: systemd-udevd-kernel.socket: Deactivated successfully.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Closed systemd-udevd-kernel.socket - udev Kernel Socket.
Jun 29 11:43:50 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=systemd-tmpfiles-setup-dev comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Starting initrd-udevadm-cleanup-db.service - Cleanup udev Database...
Jun 29 11:43:50 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=systemd-sysusers comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: systemd-tmpfiles-setup-dev.service: Deactivated successfully.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Stopped systemd-tmpfiles-setup-dev.service - Create Static Device Nodes in /dev.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: systemd-sysusers.service: Deactivated successfully.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Stopped systemd-sysusers.service - Create System Users.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: initrd-udevadm-cleanup-db.service: Deactivated successfully.
Jun 29 11:43:50 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=initrd-udevadm-cleanup-db comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:50 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=kernel msg='unit=initrd-udevadm-cleanup-db comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Finished initrd-udevadm-cleanup-db.service - Cleanup udev Database.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Reached target initrd-switch-root.target - Switch Root.
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Starting initrd-switch-root.service - Switch Root...
Jun 29 11:43:50 ip-172-31-27-8 systemd[1]: Switching root.
Jun 29 11:43:50 ip-172-31-27-8 systemd-journald[341]: Journal stopped
Jun 29 11:43:52 ip-172-31-27-8 systemd-journald[341]: Received SIGTERM from PID 1 (systemd).
Jun 29 11:43:52 ip-172-31-27-8 kernel: SELinux:  Class user_namespace not defined in policy.
Jun 29 11:43:52 ip-172-31-27-8 kernel: SELinux: the above unknown classes and permissions will be allowed
Jun 29 11:43:52 ip-172-31-27-8 kernel: SELinux:  policy capability network_peer_controls=1
Jun 29 11:43:52 ip-172-31-27-8 kernel: SELinux:  policy capability open_perms=1
Jun 29 11:43:52 ip-172-31-27-8 kernel: SELinux:  policy capability extended_socket_class=1
Jun 29 11:43:52 ip-172-31-27-8 kernel: SELinux:  policy capability always_check_network=0
Jun 29 11:43:52 ip-172-31-27-8 kernel: SELinux:  policy capability cgroup_seclabel=1
Jun 29 11:43:52 ip-172-31-27-8 kernel: SELinux:  policy capability nnp_nosuid_transition=1
Jun 29 11:43:52 ip-172-31-27-8 kernel: SELinux:  policy capability genfs_seclabel_symlinks=1
Jun 29 11:43:52 ip-172-31-27-8 kernel: SELinux:  policy capability ioctl_skip_cloexec=0
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Successfully loaded SELinux policy in 143.102ms.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Relabelled /dev, /dev/shm, /run, /sys/fs/cgroup in 20.808ms.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: systemd 252.23-3.amzn2023 running in system mode (+PAM +AUDIT +SELINUX -APPARMOR +IMA +SMACK +SECCOMP -GCRYPT -GNUTLS +OPENSSL +ACL +BLKID +CURL +ELFUTILS +FIDO2 +IDN2 -IDN -IPTC +KMOD +LIBCRYPTSETUP +LIBFDISK +PCRE2 +PWQUALITY +P11KIT +QRENCODE +TPM2 -BZIP2 -LZ4 +XZ -ZLIB -ZSTD +BPF_FRAMEWORK +XKBCOMMON +UTMP +SYSVINIT default-hierarchy=unified)
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Detected virtualization amazon.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Detected architecture x86-64.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Hostname set to <ip-172-31-27-8.us-west-2.compute.internal>.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: bpf-lsm: LSM BPF program attached
Jun 29 11:43:52 ip-172-31-27-8 zram_generator::config[807]: zram0: system has too much memory (904MB), limit is 800MB, ignoring.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: /usr/lib/systemd/system/acpid.socket:6: ListenStream= references a path below legacy directory /var/run/, updating /var/run/acpid.socket → /run/acpid.socket; please update the unit file accordingly.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: initrd-switch-root.service: Deactivated successfully.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Stopped initrd-switch-root.service - Switch Root.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: systemd-journald.service: Scheduled restart job, restart counter is at 1.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Created slice system-getty.slice - Slice /system/getty.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Created slice system-modprobe.slice - Slice /system/modprobe.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Created slice system-serial\x2dgetty.slice - Slice /system/serial-getty.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Created slice system-sshd\x2dkeygen.slice - Slice /system/sshd-keygen.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Created slice user.slice - User and Session Slice.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Started systemd-ask-password-console.path - Dispatch Password Requests to Console Directory Watch.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Started systemd-ask-password-wall.path - Forward Password Requests to Wall Directory Watch.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Set up automount proc-sys-fs-binfmt_misc.automount - Arbitrary Executable File Formats File System Automount Point.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Expecting device dev-ttyS0.device - /dev/ttyS0...
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Reached target aws-eb.target.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Reached target cryptsetup.target - Local Encrypted Volumes.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Stopped target initrd-switch-root.target - Switch Root.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Stopped target initrd-fs.target - Initrd File Systems.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Stopped target initrd-root-fs.target - Initrd Root File System.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Reached target integritysetup.target - Local Integrity Protected Volumes.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Reached target paths.target - Path Units.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Reached target slices.target - Slice Units.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Reached target swap.target - Swaps.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Reached target veritysetup.target - Local Verity Protected Volumes.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Listening on systemd-coredump.socket - Process Core Dump Socket.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Listening on systemd-initctl.socket - initctl Compatibility Named Pipe.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Listening on systemd-networkd.socket - Network Service Netlink Socket.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Listening on systemd-udevd-control.socket - udev Control Socket.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Listening on systemd-udevd-kernel.socket - udev Kernel Socket.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Listening on systemd-userdbd.socket - User Database Manager Socket.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Mounting dev-hugepages.mount - Huge Pages File System...
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Mounting dev-mqueue.mount - POSIX Message Queue File System...
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Mounting sys-kernel-debug.mount - Kernel Debug File System...
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Mounting sys-kernel-tracing.mount - Kernel Trace File System...
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Mounting tmp.mount - Temporary Directory /tmp...
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: auth-rpcgss-module.service - Kernel Module supporting RPCSEC_GSS was skipped because of an unmet condition check (ConditionPathExists=/etc/krb5.keytab).
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Starting kmod-static-nodes.service - Create List of Static Device Nodes...
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Starting <EMAIL> - Load Kernel Module configfs...
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Starting modprobe@dm_mod.service - Load Kernel Module dm_mod...
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Starting <EMAIL> - Load Kernel Module drm...
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Starting modprobe@efi_pstore.service - Load Kernel Module efi_pstore...
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Starting <EMAIL> - Load Kernel Module fuse...
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Starting <EMAIL> - Load Kernel Module loop...
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Starting nfs-convert.service - Preprocess NFS configuration convertion...
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Starting systemd-fsck-root.service - File System Check on Root Device...
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Stopped systemd-journald.service - Journal Service.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Starting systemd-journald.service - Journal Service...
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: systemd-modules-load.service - Load Kernel Modules was skipped because no trigger condition checks were met.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Starting systemd-network-generator.service - Generate network units from Kernel command line...
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Starting systemd-sysctl.service - Apply Kernel Variables...
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Starting systemd-udev-trigger.service - Coldplug All udev Devices...
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Mounted dev-hugepages.mount - Huge Pages File System.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Mounted dev-mqueue.mount - POSIX Message Queue File System.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Mounted sys-kernel-debug.mount - Kernel Debug File System.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Mounted sys-kernel-tracing.mount - Kernel Trace File System.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Mounted tmp.mount - Temporary Directory /tmp.
Jun 29 11:43:52 ip-172-31-27-8 kernel: device-mapper: core: CONFIG_IMA_DISABLE_HTABLE is disabled. Duplicate IMA measurements will not be recorded in the IMA log.
Jun 29 11:43:52 ip-172-31-27-8 kernel: device-mapper: uevent: version 1.0.3
Jun 29 11:43:52 ip-172-31-27-8 kernel: device-mapper: ioctl: 4.47.0-ioctl (2022-07-28) initialised: <EMAIL>
Jun 29 11:43:52 ip-172-31-27-8 kernel: fuse: init (API version 7.38)
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Finished kmod-static-nodes.service - Create List of Static Device Nodes.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: <EMAIL>: Deactivated successfully.
Jun 29 11:43:52 ip-172-31-27-8 systemd-journald[829]: Journal started
Jun 29 11:43:52 ip-172-31-27-8 systemd-journald[829]: Runtime Journal (/run/log/journal/ec28f7ae11495954c947725fc2940e61) is 2.2M, max 18.0M, 15.8M free.
Jun 29 11:43:52 ip-172-31-27-8 audit: MAC_POLICY_LOAD auid=********** ses=********** lsm=selinux res=1
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=8 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=8 op=UNLOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=9 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=9 op=UNLOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=10 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=10 op=UNLOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=11 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=11 op=UNLOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=12 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=12 op=UNLOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=13 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=13 op=UNLOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=14 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=15 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=14 op=UNLOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=15 op=UNLOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=16 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=16 op=UNLOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=17 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=17 op=UNLOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=18 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=18 op=UNLOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=19 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=19 op=UNLOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=20 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=21 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=21 op=UNLOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=22 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=20 op=UNLOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=22 op=UNLOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=23 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=23 op=UNLOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=24 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=24 op=UNLOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=25 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=25 op=UNLOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=26 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=26 op=UNLOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=27 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=28 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=3 op=UNLOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=29 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=30 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=4 op=UNLOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=5 op=UNLOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=31 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=28 op=UNLOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=32 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=33 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=29 op=UNLOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=30 op=UNLOAD
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=systemd-journald comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=31 op=UNLOAD
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=initrd-switch-root comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=initrd-switch-root comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=systemd-journald comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=systemd-journald comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=34 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=35 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=36 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=33 op=UNLOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=32 op=UNLOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: CONFIG_CHANGE op=set audit_enabled=1 old=1 auid=********** ses=********** subj=system_u:system_r:syslogd_t:s0 res=1
Jun 29 11:43:52 ip-172-31-27-8 audit[829]: SYSCALL arch=c000003e syscall=46 success=yes exit=60 a0=5 a1=7ffe334d0b50 a2=4000 a3=7ffe334d0bdc items=0 ppid=1 pid=829 auid=********** uid=0 gid=0 euid=0 suid=0 fsuid=0 egid=0 sgid=0 fsgid=0 tty=(none) ses=********** comm="systemd-journal" exe="/usr/lib/systemd/systemd-journald" subj=system_u:system_r:syslogd_t:s0 key=(null)
Jun 29 11:43:52 ip-172-31-27-8 audit: PROCTITLE proctitle="/usr/lib/systemd/systemd-journald"
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=kmod-static-nodes comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Queued start job for default target graphical.target.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: systemd-journald.service: Deactivated successfully.
Jun 29 11:43:52 ip-172-31-27-8 systemd-fsck[840]: /usr/sbin/fsck.xfs: XFS file system.
Jun 29 11:43:52 ip-172-31-27-8 kernel: loop: module loaded
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=modprobe@configfs comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=modprobe@configfs comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=systemd-journald comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: modprobe@dm_mod.service: Deactivated successfully.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Finished <EMAIL> - Load Kernel Module configfs.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Started systemd-journald.service - Journal Service.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Finished modprobe@dm_mod.service - Load Kernel Module dm_mod.
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=modprobe@dm_mod comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=modprobe@dm_mod comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: <EMAIL>: Deactivated successfully.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Finished <EMAIL> - Load Kernel Module drm.
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=modprobe@drm comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=modprobe@drm comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: modprobe@efi_pstore.service: Deactivated successfully.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Finished modprobe@efi_pstore.service - Load Kernel Module efi_pstore.
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=modprobe@efi_pstore comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=modprobe@efi_pstore comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: <EMAIL>: Deactivated successfully.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Finished <EMAIL> - Load Kernel Module fuse.
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=modprobe@fuse comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=modprobe@fuse comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: <EMAIL>: Deactivated successfully.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Finished <EMAIL> - Load Kernel Module loop.
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=modprobe@loop comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=modprobe@loop comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: nfs-convert.service: Deactivated successfully.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Finished nfs-convert.service - Preprocess NFS configuration convertion.
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=nfs-convert comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=nfs-convert comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Finished systemd-fsck-root.service - File System Check on Root Device.
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=systemd-fsck-root comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Finished systemd-network-generator.service - Generate network units from Kernel command line.
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=systemd-network-generator comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Finished systemd-sysctl.service - Apply Kernel Variables.
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=systemd-sysctl comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Finished systemd-udev-trigger.service - Coldplug All udev Devices.
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=systemd-udev-trigger comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Mounting sys-fs-fuse-connections.mount - FUSE Control File System...
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Mounting sys-kernel-config.mount - Kernel Configuration File System...
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Starting systemd-remount-fs.service - Remount Root and Kernel File Systems...
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: systemd-repart.service - Repartition Root Disk was skipped because no trigger condition checks were met.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Mounted sys-fs-fuse-connections.mount - FUSE Control File System.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Mounted sys-kernel-config.mount - Kernel Configuration File System.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Finished systemd-remount-fs.service - Remount Root and Kernel File Systems.
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=systemd-remount-fs comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: systemd-firstboot.service - First Boot Wizard was skipped because of an unmet condition check (ConditionFirstBoot=yes).
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: systemd-hwdb-update.service - Rebuild Hardware Database was skipped because of an unmet condition check (ConditionNeedsUpdate=/etc).
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Starting systemd-journal-flush.service - Flush Journal to Persistent Storage...
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: systemd-pstore.service - Platform Persistent Storage Archival was skipped because of an unmet condition check (ConditionDirectoryNotEmpty=/sys/fs/pstore).
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Starting systemd-random-seed.service - Load/Save Random Seed...
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: systemd-sysusers.service - Create System Users was skipped because no trigger condition checks were met.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Starting systemd-tmpfiles-setup-dev.service - Create Static Device Nodes in /dev...
Jun 29 11:43:52 ip-172-31-27-8 systemd-journald[829]: Runtime Journal (/run/log/journal/ec28f7ae11495954c947725fc2940e61) is 2.2M, max 18.0M, 15.8M free.
Jun 29 11:43:52 ip-172-31-27-8 systemd-journald[829]: Received client request to flush runtime journal.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Finished systemd-journal-flush.service - Flush Journal to Persistent Storage.
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=systemd-journal-flush comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Finished systemd-random-seed.service - Load/Save Random Seed.
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=systemd-random-seed comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Finished systemd-tmpfiles-setup-dev.service - Create Static Device Nodes in /dev.
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=systemd-tmpfiles-setup-dev comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: first-boot-complete.target - First Boot Complete was skipped because of an unmet condition check (ConditionFirstBoot=yes).
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Reached target local-fs-pre.target - Preparation for Local File Systems.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Set up automount boot-efi.automount.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Reached target local-fs.target - Local File Systems.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: import-state.service - Import network configuration from initramfs was skipped because of an unmet condition check (ConditionDirectoryNotEmpty=/run/initramfs/state).
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: ldconfig.service - Rebuild Dynamic Linker Cache was skipped because no trigger condition checks were met.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: selinux-autorelabel-mark.service - Mark the need to relabel after reboot was skipped because of an unmet condition check (ConditionSecurity=!selinux).
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: systemd-binfmt.service - Set Up Additional Binary Formats was skipped because no trigger condition checks were met.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: systemd-boot-system-token.service - Store a System Token in an EFI Variable was skipped because of an unmet condition check (ConditionPathExists=/sys/firmware/efi/efivars/LoaderFeatures-4a67b082-0a4c-41cf-b6c7-440b29bb8c4f).
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Starting systemd-boot-update.service - Automatic Boot Loader Update...
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: systemd-machine-id-commit.service - Commit a transient machine-id on disk was skipped because of an unmet condition check (ConditionPathIsMountPoint=/etc/machine-id).
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=37 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=38 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=6 op=UNLOAD
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=7 op=UNLOAD
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Starting systemd-tmpfiles-setup.service - Create Volatile Files and Directories...
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Starting systemd-udevd.service - Rule-based Manager for Device Events and Files...
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: boot-efi.automount: Got automount request for /boot/efi, triggered by 1218 (bootctl)
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Created slice system-systemd\x2dfsck.slice - Slice /system/systemd-fsck.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Expecting device dev-disk-by\x2duuid-5A01\x2dAD97.device - /dev/disk/by-uuid/5A01-AD97...
Jun 29 11:43:52 ip-172-31-27-8 systemd-udevd[1221]: Using default interface naming scheme 'v252'.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Finished systemd-tmpfiles-setup.service - Create Volatile Files and Directories.
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=systemd-tmpfiles-setup comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Started systemd-udevd.service - Rule-based Manager for Device Events and Files.
Jun 29 11:43:52 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=systemd-udevd comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:52 ip-172-31-27-8 audit: BPF prog-id=39 op=LOAD
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Starting auditd.service - Security Auditing Service...
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: systemd-journal-catalog-update.service - Rebuild Journal Catalog was skipped because of an unmet condition check (ConditionNeedsUpdate=/var).
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Starting systemd-resolved.service - Network Name Resolution...
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Condition check resulted in dev-ttyS0.device - /dev/ttyS0 being skipped.
Jun 29 11:43:52 ip-172-31-27-8 systemd[1]: Mounting var-lib-nfs-rpc_pipefs.mount - RPC Pipe File System...
Jun 29 11:43:53 ip-172-31-27-8 audit[1246]: AVC avc:  denied  { setattr } for  pid=1246 comm="auditd" name="audit" dev="nvme0n1p1" ino=2779997 scontext=system_u:system_r:auditd_t:s0 tcontext=system_u:object_r:var_log_t:s0 tclass=dir permissive=1
Jun 29 11:43:53 ip-172-31-27-8 audit[1246]: SYSCALL arch=c000003e syscall=90 success=yes exit=0 a0=55ee3c476990 a1=1c0 a2=55ee3c47699e a3=1a4 items=0 ppid=1240 pid=1246 auid=********** uid=0 gid=0 euid=0 suid=0 fsuid=0 egid=0 sgid=0 fsgid=0 tty=(none) ses=********** comm="auditd" exe="/usr/sbin/auditd" subj=system_u:system_r:auditd_t:s0 key=(null)
Jun 29 11:43:53 ip-172-31-27-8 audit: PROCTITLE proctitle="/sbin/auditd"
Jun 29 11:43:53 ip-172-31-27-8 audit[1246]: AVC avc:  denied  { create } for  pid=1246 comm="auditd" name="audit.log" scontext=system_u:system_r:auditd_t:s0 tcontext=system_u:object_r:var_log_t:s0 tclass=file permissive=1
Jun 29 11:43:53 ip-172-31-27-8 audit[1246]: AVC avc:  denied  { read open } for  pid=1246 comm="auditd" path="/var/log/audit/audit.log" dev="nvme0n1p1" ino=2780010 scontext=system_u:system_r:auditd_t:s0 tcontext=system_u:object_r:var_log_t:s0 tclass=file permissive=1
Jun 29 11:43:53 ip-172-31-27-8 audit[1246]: SYSCALL arch=c000003e syscall=257 success=yes exit=5 a0=ffffff9c a1=55ee3c476900 a2=4c0 a3=1a0 items=4 ppid=1240 pid=1246 auid=********** uid=0 gid=0 euid=0 suid=0 fsuid=0 egid=0 sgid=0 fsgid=0 tty=(none) ses=********** comm="auditd" exe="/usr/sbin/auditd" subj=system_u:system_r:auditd_t:s0 key=(null)
Jun 29 11:43:53 ip-172-31-27-8 audit: CWD cwd="/"
Jun 29 11:43:53 ip-172-31-27-8 audit: PATH item=0 name=(null) inode=2779997 dev=103:01 mode=040700 ouid=0 ogid=0 rdev=00:00 obj=system_u:object_r:var_log_t:s0 nametype=PARENT cap_fp=0 cap_fi=0 cap_fe=0 cap_fver=0 cap_frootid=0
Jun 29 11:43:53 ip-172-31-27-8 audit: PATH item=1 name=(null) nametype=CREATE cap_fp=0 cap_fi=0 cap_fe=0 cap_fver=0 cap_frootid=0
Jun 29 11:43:53 ip-172-31-27-8 audit: PATH item=2 name=(null) inode=2779997 dev=103:01 mode=040700 ouid=0 ogid=0 rdev=00:00 obj=system_u:object_r:var_log_t:s0 nametype=PARENT cap_fp=0 cap_fi=0 cap_fe=0 cap_fver=0 cap_frootid=0
Jun 29 11:43:53 ip-172-31-27-8 audit: PATH item=3 name=(null) inode=2780010 dev=103:01 mode=0100640 ouid=0 ogid=0 rdev=00:00 obj=system_u:object_r:var_log_t:s0 nametype=CREATE cap_fp=0 cap_fi=0 cap_fe=0 cap_fver=0 cap_frootid=0
Jun 29 11:43:53 ip-172-31-27-8 audit: PROCTITLE proctitle="/sbin/auditd"
Jun 29 11:43:53 ip-172-31-27-8 audit[1246]: AVC avc:  denied  { setattr } for  pid=1246 comm="auditd" name="audit.log" dev="nvme0n1p1" ino=2780010 scontext=system_u:system_r:auditd_t:s0 tcontext=system_u:object_r:var_log_t:s0 tclass=file permissive=1
Jun 29 11:43:53 ip-172-31-27-8 audit[1246]: SYSCALL arch=c000003e syscall=91 success=yes exit=0 a0=5 a1=180 a2=0 a3=0 items=0 ppid=1240 pid=1246 auid=********** uid=0 gid=0 euid=0 suid=0 fsuid=0 egid=0 sgid=0 fsgid=0 tty=(none) ses=********** comm="auditd" exe="/usr/sbin/auditd" subj=system_u:system_r:auditd_t:s0 key=(null)
Jun 29 11:43:53 ip-172-31-27-8 audit: PROCTITLE proctitle="/sbin/auditd"
Jun 29 11:43:53 ip-172-31-27-8 auditd[1246]: No plugins found, not dispatching events
Jun 29 11:43:53 ip-172-31-27-8 audit: CONFIG_CHANGE op=set audit_enabled=1 old=1 auid=********** ses=********** subj=system_u:system_r:auditd_t:s0 res=1
Jun 29 11:43:53 ip-172-31-27-8 audit[1246]: SYSCALL arch=c000003e syscall=44 success=yes exit=60 a0=3 a1=7ffc7fb79590 a2=3c a3=0 items=0 ppid=1240 pid=1246 auid=********** uid=0 gid=0 euid=0 suid=0 fsuid=0 egid=0 sgid=0 fsgid=0 tty=(none) ses=********** comm="auditd" exe="/usr/sbin/auditd" subj=system_u:system_r:auditd_t:s0 key=(null)
Jun 29 11:43:53 ip-172-31-27-8 audit: PROCTITLE proctitle="/sbin/auditd"
Jun 29 11:43:53 ip-172-31-27-8 audit: CONFIG_CHANGE op=set audit_pid=1246 old=0 auid=********** ses=********** subj=system_u:system_r:auditd_t:s0 res=1
Jun 29 11:43:53 ip-172-31-27-8 audit[1246]: SYSCALL arch=c000003e syscall=44 success=yes exit=60 a0=3 a1=7ffc7fb77240 a2=3c a3=0 items=0 ppid=1240 pid=1246 auid=********** uid=0 gid=0 euid=0 suid=0 fsuid=0 egid=0 sgid=0 fsgid=0 tty=(none) ses=********** comm="auditd" exe="/usr/sbin/auditd" subj=system_u:system_r:auditd_t:s0 key=(null)
Jun 29 11:43:53 ip-172-31-27-8 audit: PROCTITLE proctitle="/sbin/auditd"
Jun 29 11:43:53 ip-172-31-27-8 auditd[1246]: Init complete, auditd 3.0.6 listening for events (startup state enable)
Jun 29 11:43:53 ip-172-31-27-8 kernel: RPC: Registered named UNIX socket transport module.
Jun 29 11:43:53 ip-172-31-27-8 kernel: RPC: Registered udp transport module.
Jun 29 11:43:53 ip-172-31-27-8 kernel: RPC: Registered tcp transport module.
Jun 29 11:43:53 ip-172-31-27-8 kernel: RPC: Registered tcp NFSv4.1 backchannel transport module.
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: Mounted var-lib-nfs-rpc_pipefs.mount - RPC Pipe File System.
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: Reached target rpc_pipefs.target.
Jun 29 11:43:53 ip-172-31-27-8 systemd-resolved[1242]: Positive Trust Anchors:
Jun 29 11:43:53 ip-172-31-27-8 systemd-resolved[1242]: . IN DS 20326 8 2 e06d44b80b8f1d39a95c0b0d7c65d08458e880409bbc683457104237c7f8ec8d
Jun 29 11:43:53 ip-172-31-27-8 systemd-resolved[1242]: Negative trust anchors: home.arpa 10.in-addr.arpa 16.172.in-addr.arpa 17.172.in-addr.arpa 18.172.in-addr.arpa 19.172.in-addr.arpa 20.172.in-addr.arpa 21.172.in-addr.arpa 22.172.in-addr.arpa 23.172.in-addr.arpa 24.172.in-addr.arpa 25.172.in-addr.arpa 26.172.in-addr.arpa 27.172.in-addr.arpa 28.172.in-addr.arpa 29.172.in-addr.arpa 30.172.in-addr.arpa 31.172.in-addr.arpa 168.192.in-addr.arpa d.f.ip6.arpa corp home internal intranet lan local private test
Jun 29 11:43:53 ip-172-31-27-8 kernel: input: Power Button as /devices/LNXSYSTM:00/LNXPWRBN:00/input/input0
Jun 29 11:43:53 ip-172-31-27-8 kernel: ena 0000:00:05.0: Elastic Network Adapter (ENA) v2.13.2g
Jun 29 11:43:53 ip-172-31-27-8 kernel: ena 0000:00:05.0: ENA device version: 0.10
Jun 29 11:43:53 ip-172-31-27-8 kernel: ena 0000:00:05.0: ENA controller version: 0.0.1 implementation version 1
Jun 29 11:43:53 ip-172-31-27-8 kernel: i8042: PNP: PS/2 Controller [PNP0303:KBD,PNP0f13:MOU] at 0x60,0x64 irq 1,12
Jun 29 11:43:53 ip-172-31-27-8 kernel: i8042: Warning: Keylock active
Jun 29 11:43:53 ip-172-31-27-8 kernel: ACPI: button: Power Button [PWRF]
Jun 29 11:43:53 ip-172-31-27-8 kernel: input: Sleep Button as /devices/LNXSYSTM:00/LNXSLPBN:00/input/input1
Jun 29 11:43:53 ip-172-31-27-8 kernel: ACPI: button: Sleep Button [SLPF]
Jun 29 11:43:53 ip-172-31-27-8 kernel: serio: i8042 KBD port at 0x60,0x64 irq 1
Jun 29 11:43:53 ip-172-31-27-8 kernel: serio: i8042 AUX port at 0x60,0x64 irq 12
Jun 29 11:43:53 ip-172-31-27-8 kernel: ena 0000:00:05.0: LLQ is not supported Fallback to host mode policy.
Jun 29 11:43:53 ip-172-31-27-8 kernel: ena 0000:00:05.0: Elastic Network Adapter (ENA) found at mem 80400000, mac addr 0a:9e:23:4b:c9:1d
Jun 29 11:43:53 ip-172-31-27-8 audit: CONFIG_CHANGE auid=********** ses=********** subj=system_u:system_r:unconfined_service_t:s0 op=add_rule key=(null) list=1 res=1
Jun 29 11:43:53 ip-172-31-27-8 audit[1274]: SYSCALL arch=c000003e syscall=44 success=yes exit=1056 a0=3 a1=7ffe48214000 a2=420 a3=0 items=0 ppid=1249 pid=1274 auid=********** uid=0 gid=0 euid=0 suid=0 fsuid=0 egid=0 sgid=0 fsgid=0 tty=(none) ses=********** comm="auditctl" exe="/usr/sbin/auditctl" subj=system_u:system_r:unconfined_service_t:s0 key=(null)
Jun 29 11:43:53 ip-172-31-27-8 audit: PROCTITLE proctitle=2F7362696E2F617564697463746C002D52002F6574632F61756469742F61756469742E72756C6573
Jun 29 11:43:53 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=auditd comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:53 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=systemd-resolved comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:53 ip-172-31-27-8 audit[1291]: SYSTEM_BOOT pid=1291 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg=' comm="systemd-update-utmp" exe="/usr/lib/systemd/systemd-update-utmp" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: Started auditd.service - Security Auditing Service.
Jun 29 11:43:53 ip-172-31-27-8 augenrules[1249]: /sbin/augenrules: No change
Jun 29 11:43:53 ip-172-31-27-8 systemd-resolved[1242]: Using system hostname 'ip-172-31-27-8.us-west-2.compute.internal'.
Jun 29 11:43:53 ip-172-31-27-8 augenrules[1274]: No rules
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: Starting systemd-update-utmp.service - Record System Boot/Shutdown in UTMP...
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: Started systemd-resolved.service - Network Name Resolution.
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: Reached target nss-lookup.target - Host and Network Name Lookups.
Jun 29 11:43:53 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=systemd-update-utmp comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: Finished systemd-update-utmp.service - Record System Boot/Shutdown in UTMP.
Jun 29 11:43:53 ip-172-31-27-8 kernel: ena 0000:00:05.0 ens5: renamed from eth0
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: Reloading.
Jun 29 11:43:53 ip-172-31-27-8 zram_generator::config[1343]: zram0: system has too much memory (904MB), limit is 800MB, ignoring.
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: /usr/lib/systemd/system/acpid.socket:6: ListenStream= references a path below legacy directory /var/run/, updating /var/run/acpid.socket → /run/acpid.socket; please update the unit file accordingly.
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: Condition check resulted in dev-disk-by\x2duuid-5A01\x2dAD97.device - Amazon Elastic Block Store EFI\x20System\x20Partition being skipped.
Jun 29 11:43:53 ip-172-31-27-8 audit: BPF prog-id=40 op=LOAD
Jun 29 11:43:53 ip-172-31-27-8 audit: BPF prog-id=39 op=UNLOAD
Jun 29 11:43:53 ip-172-31-27-8 audit: BPF prog-id=41 op=LOAD
Jun 29 11:43:53 ip-172-31-27-8 audit: BPF prog-id=42 op=LOAD
Jun 29 11:43:53 ip-172-31-27-8 audit: BPF prog-id=37 op=UNLOAD
Jun 29 11:43:53 ip-172-31-27-8 audit: BPF prog-id=38 op=UNLOAD
Jun 29 11:43:53 ip-172-31-27-8 audit: BPF prog-id=43 op=LOAD
Jun 29 11:43:53 ip-172-31-27-8 audit: BPF prog-id=34 op=UNLOAD
Jun 29 11:43:53 ip-172-31-27-8 audit: BPF prog-id=44 op=LOAD
Jun 29 11:43:53 ip-172-31-27-8 audit: BPF prog-id=45 op=LOAD
Jun 29 11:43:53 ip-172-31-27-8 audit: BPF prog-id=35 op=UNLOAD
Jun 29 11:43:53 ip-172-31-27-8 audit: BPF prog-id=36 op=UNLOAD
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: Created slice system-policy\x2droutes.slice - Slice /system/policy-routes.
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: import-state.service - Import network configuration from initramfs was skipped because of an unmet condition check (ConditionDirectoryNotEmpty=/run/initramfs/state).
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: ldconfig.service - Rebuild Dynamic Linker Cache was skipped because no trigger condition checks were met.
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: Starting modprobe@dm_mod.service - Load Kernel Module dm_mod...
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: Starting modprobe@efi_pstore.service - Load Kernel Module efi_pstore...
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: Starting <EMAIL> - Load Kernel Module loop...
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: selinux-autorelabel-mark.service - Mark the need to relabel after reboot was skipped because of an unmet condition check (ConditionSecurity=!selinux).
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: systemd-binfmt.service - Set Up Additional Binary Formats was skipped because no trigger condition checks were met.
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: systemd-boot-system-token.service - Store a System Token in an EFI Variable was skipped because of an unmet condition check (ConditionPathExists=/sys/firmware/efi/efivars/LoaderFeatures-4a67b082-0a4c-41cf-b6c7-440b29bb8c4f).
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: systemd-firstboot.service - First Boot Wizard was skipped because of an unmet condition check (ConditionFirstBoot=yes).
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: first-boot-complete.target - First Boot Complete was skipped because of an unmet condition check (ConditionFirstBoot=yes).
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: Starting systemd-fsck@dev-disk-by\x2duuid-5A01\x2dAD97.service - File System Check on /dev/disk/by-uuid/5A01-AD97...
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: systemd-hwdb-update.service - Rebuild Hardware Database was skipped because of an unmet condition check (ConditionNeedsUpdate=/etc).
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: systemd-journal-catalog-update.service - Rebuild Journal Catalog was skipped because of an unmet condition check (ConditionNeedsUpdate=/var).
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: systemd-machine-id-commit.service - Commit a transient machine-id on disk was skipped because of an unmet condition check (ConditionPathIsMountPoint=/etc/machine-id).
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: systemd-modules-load.service - Load Kernel Modules was skipped because no trigger condition checks were met.
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: systemd-sysusers.service - Create System Users was skipped because no trigger condition checks were met.
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: modprobe@dm_mod.service: Deactivated successfully.
Jun 29 11:43:53 ip-172-31-27-8 systemd-fsck[1353]: fsck.fat 4.2 (2021-01-31)
Jun 29 11:43:53 ip-172-31-27-8 systemd-fsck[1353]: /dev/nvme0n1p128: 5 files, 657/5101 clusters
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: Finished modprobe@dm_mod.service - Load Kernel Module dm_mod.
Jun 29 11:43:53 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=modprobe@dm_mod comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:53 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=modprobe@dm_mod comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: modprobe@efi_pstore.service: Deactivated successfully.
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: Finished modprobe@efi_pstore.service - Load Kernel Module efi_pstore.
Jun 29 11:43:53 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=modprobe@efi_pstore comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:53 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=modprobe@efi_pstore comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: <EMAIL>: Deactivated successfully.
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: Finished <EMAIL> - Load Kernel Module loop.
Jun 29 11:43:53 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=modprobe@loop comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:53 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=modprobe@loop comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: Finished systemd-fsck@dev-disk-by\x2duuid-5A01\x2dAD97.service - File System Check on /dev/disk/by-uuid/5A01-AD97.
Jun 29 11:43:53 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=systemd-fsck@dev-disk-by\x2duuid-5A01\x2dAD97 comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: Mounting boot-efi.mount - /boot/efi...
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: systemd-pstore.service - Platform Persistent Storage Archival was skipped because of an unmet condition check (ConditionDirectoryNotEmpty=/sys/fs/pstore).
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: systemd-repart.service - Repartition Root Disk was skipped because no trigger condition checks were met.
Jun 29 11:43:53 ip-172-31-27-8 systemd[1]: Mounted boot-efi.mount - /boot/efi.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Finished systemd-boot-update.service - Automatic Boot Loader Update.
Jun 29 11:43:54 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=systemd-boot-update comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: systemd-update-done.service - Update is Completed was skipped because no trigger condition checks were met.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Reached target sysinit.target - System Initialization.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Started fstrim.timer - Discard unused blocks once a week.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Started logrotate.timer - Daily rotation of log files.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Started <EMAIL>.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Started sysstat-collect.timer - Run system activity accounting tool every 10 minutes.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Started sysstat-summary.timer - Generate summary of yesterday's process accounting.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Started systemd-tmpfiles-clean.timer - Daily Cleanup of Temporary Directories.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Started update-motd.timer - Timer for Dynamically Generate Message Of The Day.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Reached target timers.target - Timer Units.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Listening on acpid.socket - ACPID Listen Socket.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Listening on dbus.socket - D-Bus System Message Bus Socket.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Listening on sssd-kcm.socket - SSSD Kerberos Cache Manager responder socket.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Reached target sockets.target - Socket Units.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: rpmdb-rebuild.service - RPM database rebuild was skipped because of an unmet condition check (ConditionPathExists=/var/lib/rpm/.rebuilddb).
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: systemd-pcrphase-sysinit.service - TPM2 PCR Barrier (Initialization) was skipped because of an unmet condition check (ConditionPathExists=/sys/firmware/efi/efivars/StubPcrKernelImage-4a67b082-0a4c-41cf-b6c7-440b29bb8c4f).
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Reached target basic.target - Basic System.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Started acpid.service - ACPI Event Daemon.
Jun 29 11:43:54 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=acpid comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Starting cloud-init-local.service - Initial cloud-init job (pre-networking)...
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Starting dracut-shutdown.service - Restore /run/initramfs on shutdown...
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Started irqbalance.service - irqbalance daemon.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Started libstoragemgmt.service - libstoragemgmt plug-in server daemon.
Jun 29 11:43:54 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=irqbalance comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:54 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=libstoragemgmt comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Starting <EMAIL> - Set up policy routes for ens5...
Jun 29 11:43:54 ip-172-31-27-8 audit: BPF prog-id=46 op=LOAD
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Starting rsyslog.service - System Logging Service...
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: <EMAIL> - OpenSSH ecdsa Server Key Generation was skipped because of an unmet condition check (ConditionPathExists=!/run/systemd/generator.early/multi-user.target.wants/cloud-init.target).
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: <EMAIL> - OpenSSH ed25519 Server Key Generation was skipped because of an unmet condition check (ConditionPathExists=!/run/systemd/generator.early/multi-user.target.wants/cloud-init.target).
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: <EMAIL> - OpenSSH rsa Server Key Generation was skipped because of an unmet condition check (ConditionPathExists=!/run/systemd/generator.early/multi-user.target.wants/cloud-init.target).
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Reached target sshd-keygen.target.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: sssd.service - System Security Services Daemon was skipped because no trigger condition checks were met.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Reached target nss-user-lookup.target - User and Group Name Lookups.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Starting sysstat.service - Resets System Activity Logs...
Jun 29 11:43:54 ip-172-31-27-8 audit: BPF prog-id=47 op=LOAD
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Starting systemd-homed.service - Home Area Manager...
Jun 29 11:43:54 ip-172-31-27-8 audit: BPF prog-id=48 op=LOAD
Jun 29 11:43:54 ip-172-31-27-8 audit: BPF prog-id=49 op=LOAD
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Starting systemd-logind.service - User Login Management...
Jun 29 11:43:54 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=cloud-init-local comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Finished cloud-init-local.service - Initial cloud-init job (pre-networking).
Jun 29 11:43:54 ip-172-31-27-8 ec2net[1370]: Starting configuration for ens5
Jun 29 11:43:54 ip-172-31-27-8 rsyslogd[1371]: [origin software="rsyslogd" swVersion="8.2204.0-3.amzn2023.0.4" x-pid="1371" x-info="https://www.rsyslog.com"] start
Jun 29 11:43:54 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=dracut-shutdown comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:54 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=rsyslog comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Finished dracut-shutdown.service - Restore /run/initramfs on shutdown.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Started rsyslog.service - System Logging Service.
Jun 29 11:43:54 ip-172-31-27-8 systemd-homed[1373]: Watching /home.
Jun 29 11:43:54 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=sysstat comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:54 ip-172-31-27-8 audit: BPF prog-id=50 op=LOAD
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Finished sysstat.service - Resets System Activity Logs.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Reached target network-pre.target - Preparation for Network.
Jun 29 11:43:54 ip-172-31-27-8 audit: BPF prog-id=51 op=LOAD
Jun 29 11:43:54 ip-172-31-27-8 audit: BPF prog-id=52 op=LOAD
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Starting dbus-broker.service - D-Bus System Message Bus...
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Starting systemd-networkd.service - Network Configuration...
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Starting logrotate.service - Rotate log files...
Jun 29 11:43:54 ip-172-31-27-8 rsyslogd[1371]: imjournal: journal files changed, reloading...  [v8.2204.0-3.amzn2023.0.4 try https://www.rsyslog.com/e/0 ]
Jun 29 11:43:54 ip-172-31-27-8 systemd-logind[1374]: Watching system buttons on /dev/input/event0 (Power Button)
Jun 29 11:43:54 ip-172-31-27-8 systemd-logind[1374]: Watching system buttons on /dev/input/event1 (Sleep Button)
Jun 29 11:43:54 ip-172-31-27-8 systemd-logind[1374]: New seat seat0.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Started dbus-broker.service - D-Bus System Message Bus.
Jun 29 11:43:54 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=dbus-broker comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:54 ip-172-31-27-8 systemd-networkd[1386]: lo: Link UP
Jun 29 11:43:54 ip-172-31-27-8 systemd-networkd[1386]: lo: Gained carrier
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: logrotate.service: Deactivated successfully.
Jun 29 11:43:54 ip-172-31-27-8 systemd-networkd[1386]: Enumeration completed
Jun 29 11:43:54 ip-172-31-27-8 systemd-networkd[1386]: ens5: Configuring with /usr/lib/systemd/network/80-ec2.network.
Jun 29 11:43:54 ip-172-31-27-8 journal[1384]: Ready
Jun 29 11:43:54 ip-172-31-27-8 systemd-networkd[1386]: ens5: Link UP
Jun 29 11:43:54 ip-172-31-27-8 systemd-networkd[1386]: ens5: Gained carrier
Jun 29 11:43:54 ip-172-31-27-8 kernel: ena 0000:00:05.0 ens5: Local page cache is disabled for less than 16 channels
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Finished logrotate.service - Rotate log files.
Jun 29 11:43:54 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=logrotate comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:54 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=logrotate comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:54 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=systemd-networkd comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Started systemd-networkd.service - Network Configuration.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Reached target network.target - Network.
Jun 29 11:43:54 ip-172-31-27-8 audit: BPF prog-id=52 op=UNLOAD
Jun 29 11:43:54 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=systemd-logind comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:54 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=systemd-homed comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Starting gssproxy.service - GSSAPI Proxy Daemon...
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Starting systemd-networkd-wait-online.service - Wait for Network to be Configured...
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Started systemd-logind.service - User Login Management.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Started systemd-homed.service - Home Area Manager.
Jun 29 11:43:54 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=systemd-homed-activate comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:54 ip-172-31-27-8 systemd-networkd[1386]: ens5: DHCPv4 address ************/20, gateway ********** acquired from **********
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Starting <EMAIL> - Load Kernel Module drm...
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Finished systemd-homed-activate.service - Home Area Activation.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: <EMAIL>: Deactivated successfully.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Finished <EMAIL> - Load Kernel Module drm.
Jun 29 11:43:54 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=modprobe@drm comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:54 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=modprobe@drm comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Started gssproxy.service - GSSAPI Proxy Daemon.
Jun 29 11:43:54 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=gssproxy comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: rpc-gssd.service - RPC security service for NFS client and server was skipped because of an unmet condition check (ConditionPathExists=/etc/krb5.keytab).
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Reached target nfs-client.target - NFS client services.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Reached target remote-fs-pre.target - Preparation for Remote File Systems.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Reached target remote-cryptsetup.target - Remote Encrypted Volumes.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: Reached target remote-fs.target - Remote File Systems.
Jun 29 11:43:54 ip-172-31-27-8 systemd[1]: systemd-pcrphase.service - TPM2 PCR Barrier (User) was skipped because of an unmet condition check (ConditionPathExists=/sys/firmware/efi/efivars/StubPcrKernelImage-4a67b082-0a4c-41cf-b6c7-440b29bb8c4f).
Jun 29 11:43:55 ip-172-31-27-8 systemd-networkd[1386]: ens5: Gained IPv6LL
Jun 29 11:43:55 ip-172-31-27-8 systemd[1]: Finished systemd-networkd-wait-online.service - Wait for Network to be Configured.
Jun 29 11:43:55 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=systemd-networkd-wait-online comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:55 ip-172-31-27-8 systemd[1]: Starting cloud-init.service - Initial cloud-init job (metadata service crawler)...
Jun 29 11:43:55 ip-172-31-27-8 ec2net[1370]: No addresses found for ens5
Jun 29 11:43:55 ip-172-31-27-8 systemd-networkd[1386]: ens5: Reconfiguring with /run/systemd/network/70-ens5.network.
Jun 29 11:43:55 ip-172-31-27-8 systemd-networkd[1386]: ens5: DHCP lease lost
Jun 29 11:43:55 ip-172-31-27-8 ec2net[1370]: Reloaded networkd
Jun 29 11:43:55 ip-172-31-27-8 systemd-networkd[1386]: ens5: DHCPv6 lease lost
Jun 29 11:43:55 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=policy-routes@ens5 comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:55 ip-172-31-27-8 systemd[1]: Finished <EMAIL> - Set up policy routes for ens5.
Jun 29 11:43:55 ip-172-31-27-8 systemd-networkd[1386]: ens5: DHCPv4 address ************/20, gateway ********** acquired from **********
Jun 29 11:43:55 ip-172-31-27-8 cloud-init[1511]: Cloud-init v. 22.2.2 running 'init' at Sun, 29 Jun 2025 11:43:55 +0000. Up 6.49 seconds.
Jun 29 11:43:55 ip-172-31-27-8 cloud-init[1511]: ci-info: ++++++++++++++++++++++++++++++++++++++Net device info+++++++++++++++++++++++++++++++++++++++
Jun 29 11:43:55 ip-172-31-27-8 cloud-init[1511]: ci-info: +--------+------+-----------------------------+---------------+--------+-------------------+
Jun 29 11:43:55 ip-172-31-27-8 cloud-init[1511]: ci-info: | Device |  Up  |           Address           |      Mask     | Scope  |     Hw-Address    |
Jun 29 11:43:55 ip-172-31-27-8 cloud-init[1511]: ci-info: +--------+------+-----------------------------+---------------+--------+-------------------+
Jun 29 11:43:55 ip-172-31-27-8 cloud-init[1511]: ci-info: |  ens5  | True |         ************        | ************* | global | 0a:9e:23:4b:c9:1d |
Jun 29 11:43:55 ip-172-31-27-8 cloud-init[1511]: ci-info: |  ens5  | True | fe80::89e:23ff:fe4b:c91d/64 |       .       |  link  | 0a:9e:23:4b:c9:1d |
Jun 29 11:43:55 ip-172-31-27-8 cloud-init[1511]: ci-info: |   lo   | True |          127.0.0.1          |   *********   |  host  |         .         |
Jun 29 11:43:55 ip-172-31-27-8 cloud-init[1511]: ci-info: |   lo   | True |           ::1/128           |       .       |  host  |         .         |
Jun 29 11:43:55 ip-172-31-27-8 cloud-init[1511]: ci-info: +--------+------+-----------------------------+---------------+--------+-------------------+
Jun 29 11:43:55 ip-172-31-27-8 cloud-init[1511]: ci-info: +++++++++++++++++++++++++++++Route IPv4 info++++++++++++++++++++++++++++++
Jun 29 11:43:55 ip-172-31-27-8 cloud-init[1511]: ci-info: +-------+-------------+------------+-----------------+-----------+-------+
Jun 29 11:43:55 ip-172-31-27-8 cloud-init[1511]: ci-info: | Route | Destination |  Gateway   |     Genmask     | Interface | Flags |
Jun 29 11:43:55 ip-172-31-27-8 cloud-init[1511]: ci-info: +-------+-------------+------------+-----------------+-----------+-------+
Jun 29 11:43:55 ip-172-31-27-8 cloud-init[1511]: ci-info: |   0   |   0.0.0.0   | ********** |     0.0.0.0     |    ens5   |   UG  |
Jun 29 11:43:55 ip-172-31-27-8 cloud-init[1511]: ci-info: |   1   |  ********** |  0.0.0.0   |  *************  |    ens5   |   U   |
Jun 29 11:43:55 ip-172-31-27-8 cloud-init[1511]: ci-info: |   2   |  ********** |  0.0.0.0   | *************** |    ens5   |   UH  |
Jun 29 11:43:55 ip-172-31-27-8 cloud-init[1511]: ci-info: |   3   |  ********** |  0.0.0.0   | *************** |    ens5   |   UH  |
Jun 29 11:43:55 ip-172-31-27-8 cloud-init[1511]: ci-info: +-------+-------------+------------+-----------------+-----------+-------+
Jun 29 11:43:55 ip-172-31-27-8 cloud-init[1511]: ci-info: +++++++++++++++++++Route IPv6 info+++++++++++++++++++
Jun 29 11:43:55 ip-172-31-27-8 cloud-init[1511]: ci-info: +-------+-------------+---------+-----------+-------+
Jun 29 11:43:55 ip-172-31-27-8 cloud-init[1511]: ci-info: | Route | Destination | Gateway | Interface | Flags |
Jun 29 11:43:55 ip-172-31-27-8 cloud-init[1511]: ci-info: +-------+-------------+---------+-----------+-------+
Jun 29 11:43:55 ip-172-31-27-8 cloud-init[1511]: ci-info: |   0   |  fe80::/64  |    ::   |    ens5   |   U   |
Jun 29 11:43:55 ip-172-31-27-8 cloud-init[1511]: ci-info: |   2   |    local    |    ::   |    ens5   |   U   |
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: ci-info: |   3   |  multicast  |    ::   |    ens5   |   U   |
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: ci-info: +-------+-------------+---------+-----------+-------+
Jun 29 11:43:56 ip-172-31-27-8 audit: BPF prog-id=53 op=LOAD
Jun 29 11:43:56 ip-172-31-27-8 audit: BPF prog-id=54 op=LOAD
Jun 29 11:43:56 ip-172-31-27-8 audit: BPF prog-id=55 op=LOAD
Jun 29 11:43:56 ip-172-31-27-8 systemd[1]: Starting systemd-hostnamed.service - Hostname Service...
Jun 29 11:43:56 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=systemd-hostnamed comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:56 ip-172-31-27-8 systemd[1]: Started systemd-hostnamed.service - Hostname Service.
Jun 29 11:43:56 ip-172-31-27-8 systemd-hostnamed[1518]: Hostname set to <ip-172-31-4-198.us-west-2.compute.internal> (static)
Jun 29 11:43:56 ip-172-31-27-8 systemd-resolved[1242]: System hostname changed to 'ip-172-31-4-198.us-west-2.compute.internal'.
Jun 29 11:43:56 ip-172-31-27-8 audit[1550]: ACCT_LOCK pid=1550 uid=0 auid=********** ses=********** subj=system_u:system_r:passwd_t:s0 msg='op=locked-password id=1000 exe="/usr/bin/passwd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: Generating public/private ed25519 key pair.
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: Your identification has been saved in /etc/ssh/ssh_host_ed25519_key
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: Your public key has been saved in /etc/ssh/ssh_host_ed25519_key.pub
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: The key fingerprint is:
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: SHA256:yYnjH1ovDK0D4hwLkknd6pHFCL2CrIl/0sJ3iArSuzs <EMAIL>
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: The key's randomart image is:
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: +--[ED25519 256]--+
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: |  .              |
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: | . .             |
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: |o o =            |
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: |.+ + + o o       |
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: |++. + o.S        |
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: |O.o+.....        |
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: |+*o*.o.+o        |
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: |o Eo= ++oo       |
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: |..+O ......      |
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: +----[SHA256]-----+
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: Generating public/private ecdsa key pair.
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: Your identification has been saved in /etc/ssh/ssh_host_ecdsa_key
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: Your public key has been saved in /etc/ssh/ssh_host_ecdsa_key.pub
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: The key fingerprint is:
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: SHA256:IduZRIoAo6LrKXxFJRMKTyCceSj2bXBXwk5UtGQI9V8 <EMAIL>
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: The key's randomart image is:
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: +---[ECDSA 256]---+
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: |=+*. o*+**       |
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: |+B++.= O= .      |
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: |= oo= X oo   E   |
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: |o  . + * +. .    |
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: |.   o . S  .     |
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: | .   .           |
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: |o   .            |
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: |o...             |
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: |.o.              |
Jun 29 11:43:56 ip-172-31-27-8 cloud-init[1511]: +----[SHA256]-----+
Jun 29 11:43:56 ip-172-31-27-8 systemd[1]: Finished cloud-init.service - Initial cloud-init job (metadata service crawler).
Jun 29 11:43:56 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=cloud-init comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:56 ip-172-31-27-8 systemd[1]: Reached target cloud-config.target - Cloud-config availability.
Jun 29 11:43:56 ip-172-31-27-8 systemd[1]: Reached target network-online.target - Network is Online.
Jun 29 11:43:56 ip-172-31-27-8 systemd[1]: Started amazon-ssm-agent.service - amazon-ssm-agent.
Jun 29 11:43:56 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=amazon-ssm-agent comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:56 ip-172-31-27-8 systemd[1]: Starting chrony-config.service - Configures optimal endpoints for the chrony NTP client in AWS...
Jun 29 11:43:56 ip-172-31-27-8 systemd[1]: Starting cloud-config.service - Apply the settings specified in cloud-config...
Jun 29 11:43:56 ip-172-31-27-8 systemd[1]: Starting rpc-statd-notify.service - Notify NFS peers of a restart...
Jun 29 11:43:56 ip-172-31-27-8 systemd[1]: Starting sshd.service - OpenSSH server daemon...
Jun 29 11:43:56 ip-172-31-27-8 systemd[1]: Starting systemd-user-sessions.service - Permit User Sessions...
Jun 29 11:43:56 ip-172-31-27-8 sm-notify[1556]: Version 2.5.4 starting
Jun 29 11:43:56 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=systemd-user-sessions comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:56 ip-172-31-27-8 systemd[1]: Finished systemd-user-sessions.service - Permit User Sessions.
Jun 29 11:43:57 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=rpc-statd-notify comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:57 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=sshd comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:57 ip-172-31-27-8 systemd[1]: Started rpc-statd-notify.service - Notify NFS peers of a restart.
Jun 29 11:43:57 ip-172-31-27-8 systemd[1]: Started sshd.service - OpenSSH server daemon.
Jun 29 11:43:57 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=atd comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:57 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=crond comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:57 ip-172-31-27-8 systemd[1]: Started atd.service - Deferred execution scheduler.
Jun 29 11:43:57 ip-172-31-27-8 systemd[1]: Started crond.service - Command Scheduler.
Jun 29 11:43:57 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=getty@tty1 comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:57 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=serial-getty@ttyS0 comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:57 ip-172-31-27-8 systemd[1]: Started <EMAIL> - Getty on tty1.
Jun 29 11:43:57 ip-172-31-27-8 systemd[1]: Started <EMAIL> - Serial Getty on ttyS0.
Jun 29 11:43:57 ip-172-31-27-8 systemd[1]: Reached target getty.target - Login Prompts.
Jun 29 11:43:57 ip-172-31-27-8 systemd[1]: chrony-config.service: Deactivated successfully.
Jun 29 11:43:57 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=chrony-config comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:57 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=chrony-config comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:57 ip-172-31-27-8 audit: BPF prog-id=56 op=LOAD
Jun 29 11:43:57 ip-172-31-27-8 systemd[1]: Finished chrony-config.service - Configures optimal endpoints for the chrony NTP client in AWS.
Jun 29 11:43:57 ip-172-31-27-8 systemd[1]: Starting chronyd.service - NTP client/server...
Jun 29 11:43:57 ip-172-31-27-8 amazon-ssm-agent[1553]: Error occurred fetching the seelog config file path:  open /etc/amazon/ssm/seelog.xml: no such file or directory
Jun 29 11:43:57 ip-172-31-27-8 amazon-ssm-agent[1553]: Initializing new seelog logger
Jun 29 11:43:57 ip-172-31-27-8 amazon-ssm-agent[1553]: New Seelog Logger Creation Complete
Jun 29 11:43:57 ip-172-31-27-8 amazon-ssm-agent[1553]: 2025-06-29 11:43:57.2056 INFO Proxy environment variables:
Jun 29 11:43:57 ip-172-31-27-8 chronyd[1587]: chronyd version 4.3 starting (+CMDMON +NTP +REFCLOCK +RTC +PRIVDROP +SCFILTER +SIGND +ASYNCDNS +NTS +SECHASH +IPV6 +DEBUG)
Jun 29 11:43:57 ip-172-31-27-8 chronyd[1587]: Frequency -4.195 +/- 0.109 ppm read from /var/lib/chrony/drift
Jun 29 11:43:57 ip-172-31-27-8 chronyd[1587]: Loaded seccomp filter (level 2)
Jun 29 11:43:57 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=chronyd comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:57 ip-172-31-27-8 systemd[1]: Started chronyd.service - NTP client/server.
Jun 29 11:43:57 ip-172-31-27-8 amazon-ssm-agent[1553]: 2025-06-29 11:43:57.2060 INFO https_proxy:
Jun 29 11:43:57 ip-172-31-27-8 cloud-init[1593]: Cloud-init v. 22.2.2 running 'modules:config' at Sun, 29 Jun 2025 11:43:57 +0000. Up 7.93 seconds.
Jun 29 11:43:57 ip-172-31-27-8 amazon-ssm-agent[1553]: 2025-06-29 11:43:57.2060 INFO http_proxy:
Jun 29 11:43:57 ip-172-31-27-8 amazon-ssm-agent[1553]: 2025-06-29 11:43:57.2060 INFO no_proxy:
Jun 29 11:43:57 ip-172-31-27-8 systemd[1]: Finished cloud-config.service - Apply the settings specified in cloud-config.
Jun 29 11:43:57 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=cloud-config comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:57 ip-172-31-27-8 amazon-ssm-agent[1553]: 2025-06-29 11:43:57.2060 INFO Checking if agent identity type OnPrem can be assumed
Jun 29 11:43:57 ip-172-31-27-8 systemd[1]: Starting cloud-final.service - Execute cloud user/final scripts...
Jun 29 11:43:57 ip-172-31-27-8 systemd[1]: Starting hibinit-agent.service - Initial hibernation setup job...
Jun 29 11:43:57 ip-172-31-27-8 amazon-ssm-agent[1553]: 2025-06-29 11:43:57.2061 INFO Checking if agent identity type EC2 can be assumed
Jun 29 11:43:57 ip-172-31-27-8 amazon-ssm-agent[1553]: 2025-06-29 11:43:57.3687 INFO Agent will take identity from EC2
Jun 29 11:43:57 ip-172-31-27-8 hibinit-agent[1598]: Effective config: {'log_to_syslog': True, 'mkswap': 'mkswap {swapfile}', 'swapon': 'swapon {swapfile}', 'swapoff': 'swapoff {swapfile}', 'touch_swap': False, 'btrfs_enabled': False, 'grub_update': True, 'swap_percentage': 100, 'swap_mb': 4000, 'state_dir': '/var/lib/hibinit-agent'}
Jun 29 11:43:57 ip-172-31-27-8 hibinit-agent[1598]: Requesting new IMDSv2 token.
Jun 29 11:43:57 ip-172-31-27-8 hibinit-agent[1598]: Instance Launch has not enabled Hibernation Configured Flag. hibinit-agent exiting!!
Jun 29 11:43:57 ip-172-31-27-8 systemd[1]: hibinit-agent.service: Deactivated successfully.
Jun 29 11:43:57 ip-172-31-27-8 amazon-ssm-agent[1553]: 2025-06-29 11:43:57.3704 INFO [amazon-ssm-agent] amazon-ssm-agent - v3.3.2299.0
Jun 29 11:43:57 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=hibinit-agent comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:57 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=hibinit-agent comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:43:57 ip-172-31-27-8 systemd[1]: Started hibinit-agent.service - Initial hibernation setup job.
Jun 29 11:43:57 ip-172-31-27-8 cloud-init[1603]: Cloud-init v. 22.2.2 running 'modules:final' at Sun, 29 Jun 2025 11:43:57 +0000. Up 8.54 seconds.
Jun 29 11:43:57 ip-172-31-27-8 [eb-cfn-init]: [2025-06-29T11:43:57Z] Started EB User Data
Jun 29 11:43:57 ip-172-31-27-8 [eb-cfn-init]: + SLEEP_TIME=2
Jun 29 11:43:57 ip-172-31-27-8 amazon-ssm-agent[1553]: 2025-06-29 11:43:57.3706 INFO [amazon-ssm-agent] OS: linux, Arch: amd64
Jun 29 11:43:57 ip-172-31-27-8 [eb-cfn-init]: + SLEEP_TIME_MAX=3600
Jun 29 11:43:57 ip-172-31-27-8 [eb-cfn-init]: + true
Jun 29 11:43:57 ip-172-31-27-8 [eb-cfn-init]: + curl https://elasticbeanstalk-platform-assets-us-west-2.s3.us-west-2.amazonaws.com/stalks/eb_python311_amazon_linux_2023_1.0.797.0_20250625232415/lib/UserDataScript.sh
Jun 29 11:43:58 ip-172-31-27-8 [eb-cfn-init]:  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
Jun 29 11:43:58 ip-172-31-27-8 [eb-cfn-init]:                                 Dload  Upload   Total   Spent    Left  Speed
Jun 29 11:43:58 ip-172-31-27-8 [eb-cfn-init]: #015  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0#015100  4838  100  4838    0     0   111k      0 --:--:-- --:--:-- --:--:--  112k
Jun 29 11:43:58 ip-172-31-27-8 [eb-cfn-init]: + RESULT=0
Jun 29 11:43:58 ip-172-31-27-8 [eb-cfn-init]: + [[ 0 -ne 0 ]]
Jun 29 11:43:58 ip-172-31-27-8 [eb-cfn-init]: + /bin/bash /tmp/ebbootstrap.sh 'https://cloudformation-waitcondition-us-west-2.s3-us-west-2.amazonaws.com/arn%3Aaws%3Acloudformation%3Aus-west-2%3A************%3Astack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773/3b3f6a50-54de-11f0-a15a-0292084a4773/AWSEBInstanceLaunchWaitHandle?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250629T114314Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86399&X-Amz-Credential=AKIAJBJSWSW6NLR67N6A%2F20250629%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Signature=6ec93324db47b3ed71be0d8d1a3f676bd02fbc2a1d0b55d20a29f59c2411b18b' arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 7abb8661-da8a-452c-96c4-5a03457a2874 https://elasticbeanstalk-health.us-west-2.amazonaws.com '' https://elasticbeanstalk-platform-assets-us-west-2.s3.us-west-2.amazonaws.com/stalks/eb_python311_amazon_linux_2023_1.0.797.0_20250625232415 us-west-2
Jun 29 11:43:58 ip-172-31-27-8 [eb-cfn-init]: [2025-06-29T11:43:58.047Z] Started EB Bootstrapping Script.
Jun 29 11:43:58 ip-172-31-27-8 [eb-cfn-init]: [2025-06-29T11:43:58.050Z] Received parameters:
Jun 29 11:43:58 ip-172-31-27-8 [eb-cfn-init]: TARBALLS =
Jun 29 11:43:58 ip-172-31-27-8 [eb-cfn-init]: EB_GEMS =
Jun 29 11:43:58 ip-172-31-27-8 [eb-cfn-init]: SIGNAL_URL = https://cloudformation-waitcondition-us-west-2.s3-us-west-2.amazonaws.com/arn%3Aaws%3Acloudformation%3Aus-west-2%3A************%3Astack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773/3b3f6a50-54de-11f0-a15a-0292084a4773/AWSEBInstanceLaunchWaitHandle?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250629T114314Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86399&X-Amz-Credential=AKIAJBJSWSW6NLR67N6A%2F20250629%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Signature=6ec93324db47b3ed71be0d8d1a3f676bd02fbc2a1d0b55d20a29f59c2411b18b
Jun 29 11:43:58 ip-172-31-27-8 [eb-cfn-init]: STACK_ID = arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773
Jun 29 11:43:58 ip-172-31-27-8 [eb-cfn-init]: REGION = us-west-2
Jun 29 11:43:58 ip-172-31-27-8 [eb-cfn-init]: GUID =
Jun 29 11:43:58 ip-172-31-27-8 [eb-cfn-init]: HEALTHD_GROUP_ID = 7abb8661-da8a-452c-96c4-5a03457a2874
Jun 29 11:43:58 ip-172-31-27-8 [eb-cfn-init]: HEALTHD_ENDPOINT = https://elasticbeanstalk-health.us-west-2.amazonaws.com
Jun 29 11:43:58 ip-172-31-27-8 [eb-cfn-init]: PROXY_SERVER =
Jun 29 11:43:58 ip-172-31-27-8 [eb-cfn-init]: HEALTHD_PROXY_LOG_LOCATION =
Jun 29 11:43:58 ip-172-31-27-8 [eb-cfn-init]: PLATFORM_ASSETS_URL = https://elasticbeanstalk-platform-assets-us-west-2.s3.us-west-2.amazonaws.com/stalks/eb_python311_amazon_linux_2023_1.0.797.0_20250625232415
Jun 29 11:43:58 ip-172-31-27-8 [eb-cfn-init]: [2025-06-29T11:43:58.054Z] engine url is set to https://elasticbeanstalk-platform-assets-us-west-2.s3.us-west-2.amazonaws.com/stalks/eb_python311_amazon_linux_2023_1.0.797.0_20250625232415/lib/platform-engine.zip
Jun 29 11:43:58 ip-172-31-27-8 [eb-cfn-init]: [2025-06-29T11:43:58.056Z] first init of instance.
Jun 29 11:43:58 ip-172-31-27-8 [eb-cfn-init]: [2025-06-29T11:43:58.059Z] Started executing cfn_init _OnInstanceBoot.
Jun 29 11:43:58 ip-172-31-27-8 [eb-cfn-init]: [2025-06-29T11:43:58.061Z] Running cfn-init ConfigSet: _OnInstanceBoot.
Jun 29 11:43:58 ip-172-31-27-8 [eb-cfn-init]: [2025-06-29T11:43:58.063Z] Using stack-id from userdata
Jun 29 11:43:58 ip-172-31-27-8 amazon-ssm-agent[1553]: 2025-06-29 11:43:57.3706 INFO [amazon-ssm-agent] Starting Core Agent
Jun 29 11:43:58 ip-172-31-27-8 amazon-ssm-agent[1553]: 2025-06-29 11:43:57.3706 INFO [amazon-ssm-agent] Registrar detected. Attempting registration
Jun 29 11:43:58 ip-172-31-27-8 amazon-ssm-agent[1553]: 2025-06-29 11:43:57.3706 INFO [Registrar] Starting registrar module
Jun 29 11:43:58 ip-172-31-27-8 amazon-ssm-agent[1553]: 2025-06-29 11:43:57.3718 INFO [EC2Identity] Checking disk for registration info
Jun 29 11:43:58 ip-172-31-27-8 amazon-ssm-agent[1553]: 2025-06-29 11:43:57.3737 INFO [EC2Identity] No registration info found for ec2 instance, attempting registration
Jun 29 11:43:58 ip-172-31-27-8 amazon-ssm-agent[1553]: 2025-06-29 11:43:57.3737 INFO [EC2Identity] Found registration keys
Jun 29 11:43:58 ip-172-31-27-8 amazon-ssm-agent[1553]: 2025-06-29 11:43:57.3737 INFO [EC2Identity] Checking write access before registering
Jun 29 11:43:58 ip-172-31-27-8 amazon-ssm-agent[1553]: 2025-06-29 11:43:57.3740 INFO [EC2Identity] Registering EC2 instance with Systems Manager
Jun 29 11:43:58 ip-172-31-27-8 [eb-cfn-init]: [2025-06-29T11:43:58.816Z] Command Returned:
Jun 29 11:43:58 ip-172-31-27-8 [eb-cfn-init]: [2025-06-29T11:43:58.819Z] Completed executing cfn_init.
Jun 29 11:43:58 ip-172-31-27-8 [eb-cfn-init]: [2025-06-29T11:43:58.823Z] finished _OnInstanceBoot
Jun 29 11:43:58 ip-172-31-27-8 [eb-cfn-init]: [2025-06-29T11:43:58.827Z] installing platform engine
Jun 29 11:43:58 ip-172-31-27-8 amazon-ssm-agent[1553]: 2025-06-29 11:43:57.4155 INFO [EC2Identity] EC2 registration was successful.
Jun 29 11:43:58 ip-172-31-27-8 amazon-ssm-agent[1553]: 2025-06-29 11:43:57.4155 INFO [amazon-ssm-agent] Registration attempted. Resuming core agent startup.
Jun 29 11:43:59 ip-172-31-27-8 amazon-ssm-agent[1553]: 2025-06-29 11:43:57.4157 INFO [CredentialRefresher] credentialRefresher has started
Jun 29 11:43:59 ip-172-31-27-8 amazon-ssm-agent[1553]: 2025-06-29 11:43:57.4157 INFO [CredentialRefresher] Starting credentials refresher loop
Jun 29 11:43:59 ip-172-31-27-8 amazon-ssm-agent[1553]: 2025-06-29 11:43:57.4462 WARN EC2RoleProvider Failed to connect to Systems Manager with instance profile role credentials. Err: retrieved credentials failed to report to ssm. Error: AccessDeniedException: User: arn:aws:sts::************:assumed-role/aws-elasticbeanstalk-ec2-role/i-06cc7bc2ee1b17d55 is not authorized to perform: ssm:UpdateInstanceInformation on resource: arn:aws:ec2:us-west-2:************:instance/i-06cc7bc2ee1b17d55 because no identity-based policy allows the ssm:UpdateInstanceInformation action
Jun 29 11:43:59 ip-172-31-27-8 amazon-ssm-agent[1553]: 2025-06-29 11:43:57.4738 ERROR EC2RoleProvider Failed to connect to Systems Manager with SSM role credentials. error calling RequestManagedInstanceRoleToken: AccessDeniedException: Systems Manager's instance management role is not configured for account: ************
Jun 29 11:43:59 ip-172-31-27-8 amazon-ssm-agent[1553]: #011status code: 400, request id: e92e8942-244c-48cc-b887-fdfdae54fe5e
Jun 29 11:43:59 ip-172-31-27-8 amazon-ssm-agent[1553]: 2025-06-29 11:43:57.4738 ERROR [CredentialRefresher] Retrieve credentials produced error: no valid credentials could be retrieved for ec2 identity. Default Host Management Err: error calling RequestManagedInstanceRoleToken: AccessDeniedException: Systems Manager's instance management role is not configured for account: ************
Jun 29 11:43:59 ip-172-31-27-8 amazon-ssm-agent[1553]: #011status code: 400, request id: e92e8942-244c-48cc-b887-fdfdae54fe5e
Jun 29 11:43:59 ip-172-31-27-8 [eb-cfn-init]: 2025-06-29 11:43:59 URL:https://elasticbeanstalk-platform-assets-us-west-2.s3.us-west-2.amazonaws.com/stalks/eb_python311_amazon_linux_2023_1.0.797.0_20250625232415/lib/platform-engine.zip [********/********] -> "/tmp/platform-engine.zip" [1]
Jun 29 11:43:59 ip-172-31-27-8 amazon-ssm-agent[1553]: 2025-06-29 11:43:57.4739 INFO [CredentialRefresher] Sleeping for 29m6s before retrying retrieve credentials
Jun 29 11:44:00 ip-172-31-27-8 [eb-cfn-init]: [2025-06-29T11:44:00.176Z] executing platform engine. see /var/log/eb-engine.log.
Jun 29 11:44:00 ip-172-31-27-8 chronyd[1587]: System clock was stepped by 0.000000 seconds
Jun 29 11:44:00 ip-172-31-27-8 audit[1668]: ADD_GROUP pid=1668 uid=0 auid=********** ses=********** subj=system_u:system_r:cloud_init_t:s0 msg='op=add-group acct="healthd" exe="/usr/sbin/useradd" hostname=? addr=? terminal=? res=success'
Jun 29 11:44:00 ip-172-31-27-8 audit[1668]: ADD_USER pid=1668 uid=0 auid=********** ses=********** subj=system_u:system_r:cloud_init_t:s0 msg='op=add-user acct="healthd" exe="/usr/sbin/useradd" hostname=? addr=? terminal=? res=success'
Jun 29 11:44:01 ip-172-31-27-8 audit[1668]: USER_MGMT pid=1668 uid=0 auid=********** ses=********** subj=system_u:system_r:cloud_init_t:s0 msg='op=add-home-dir id=1001 exe="/usr/sbin/useradd" hostname=? addr=? terminal=? res=success'
Jun 29 11:44:01 ip-172-31-27-8 systemd[1]: /usr/lib/systemd/system/cfn-hup.service:6: PIDFile= references a path below legacy directory /var/run/, updating /var/run/cfn-hup.pid → /run/cfn-hup.pid; please update the unit file accordingly.
Jun 29 11:44:01 ip-172-31-27-8 systemd[1]: /usr/lib/systemd/system/cfn-hup.service:6: PIDFile= references a path below legacy directory /var/run/, updating /var/run/cfn-hup.pid → /run/cfn-hup.pid; please update the unit file accordingly.
Jun 29 11:44:01 ip-172-31-27-8 systemd[1]: Reloading.
Jun 29 11:44:01 ip-172-31-27-8 zram_generator::config[1700]: zram0: system has too much memory (904MB), limit is 800MB, ignoring.
Jun 29 11:44:01 ip-172-31-27-8 systemd[1]: /usr/lib/systemd/system/acpid.socket:6: ListenStream= references a path below legacy directory /var/run/, updating /var/run/acpid.socket → /run/acpid.socket; please update the unit file accordingly.
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=57 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=58 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=59 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=60 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=61 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=62 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=63 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=56 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=64 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=65 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=66 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=67 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=68 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=69 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=70 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=71 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=72 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 systemd[1]: Reloading.
Jun 29 11:44:01 ip-172-31-27-8 zram_generator::config[1727]: zram0: system has too much memory (904MB), limit is 800MB, ignoring.
Jun 29 11:44:01 ip-172-31-27-8 systemd[1]: /usr/lib/systemd/system/acpid.socket:6: ListenStream= references a path below legacy directory /var/run/, updating /var/run/acpid.socket → /run/acpid.socket; please update the unit file accordingly.
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=73 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=57 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=74 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=58 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=75 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=59 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=76 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=60 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=77 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=78 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=61 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=62 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=79 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=63 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=80 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=64 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=81 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=82 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=65 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=66 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=83 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=67 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=84 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=85 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=68 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=69 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=86 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=70 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=87 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=88 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=71 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=72 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 systemd[1]: Reloading.
Jun 29 11:44:01 ip-172-31-27-8 zram_generator::config[1753]: zram0: system has too much memory (904MB), limit is 800MB, ignoring.
Jun 29 11:44:01 ip-172-31-27-8 systemd[1]: /usr/lib/systemd/system/acpid.socket:6: ListenStream= references a path below legacy directory /var/run/, updating /var/run/acpid.socket → /run/acpid.socket; please update the unit file accordingly.
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=89 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=73 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=90 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=74 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=91 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=75 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=92 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=76 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=93 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=94 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=77 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=78 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=95 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=79 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=96 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=80 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=97 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=98 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=81 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=82 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=99 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=83 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=100 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=101 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=84 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=85 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=102 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=86 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=103 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=104 op=LOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=87 op=UNLOAD
Jun 29 11:44:01 ip-172-31-27-8 audit: BPF prog-id=88 op=UNLOAD
Jun 29 11:44:02 ip-172-31-27-8 systemd[1]: Starting cfn-hup.service - This is cfn-hup daemon...
Jun 29 11:44:02 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=cfn-hup comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:44:02 ip-172-31-27-8 systemd[1]: Started cfn-hup.service - This is cfn-hup daemon.
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: [2025-06-29T11:44:02.064Z] Successfully bootstrapped instance.
Jun 29 11:44:02 ip-172-31-27-8 audit: BPF prog-id=53 op=UNLOAD
Jun 29 11:44:02 ip-172-31-27-8 audit: BPF prog-id=51 op=UNLOAD
Jun 29 11:44:02 ip-172-31-27-8 audit: BPF prog-id=50 op=UNLOAD
Jun 29 11:44:02 ip-172-31-27-8 audit: BPF prog-id=49 op=UNLOAD
Jun 29 11:44:02 ip-172-31-27-8 audit: BPF prog-id=48 op=UNLOAD
Jun 29 11:44:02 ip-172-31-27-8 audit: BPF prog-id=47 op=UNLOAD
Jun 29 11:44:02 ip-172-31-27-8 audit: BPF prog-id=46 op=UNLOAD
Jun 29 11:44:02 ip-172-31-27-8 audit: BPF prog-id=45 op=UNLOAD
Jun 29 11:44:02 ip-172-31-27-8 audit: BPF prog-id=44 op=UNLOAD
Jun 29 11:44:02 ip-172-31-27-8 audit: BPF prog-id=43 op=UNLOAD
Jun 29 11:44:02 ip-172-31-27-8 audit: BPF prog-id=42 op=UNLOAD
Jun 29 11:44:02 ip-172-31-27-8 audit: BPF prog-id=41 op=UNLOAD
Jun 29 11:44:02 ip-172-31-27-8 audit: BPF prog-id=40 op=UNLOAD
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: [2025-06-29T11:44:02.072Z] Sending signal 0 to CFN wait condition https://cloudformation-waitcondition-us-west-2.s3-us-west-2.amazonaws.com/arn%3Aaws%3Acloudformation%3Aus-west-2%3A************%3Astack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773/3b3f6a50-54de-11f0-a15a-0292084a4773/AWSEBInstanceLaunchWaitHandle?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250629T114314Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86399&X-Amz-Credential=AKIAJBJSWSW6NLR67N6A%2F20250629%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Signature=6ec93324db47b3ed71be0d8d1a3f676bd02fbc2a1d0b55d20a29f59c2411b18b
Jun 29 11:44:02 ip-172-31-27-8 audit: BPF prog-id=55 op=UNLOAD
Jun 29 11:44:02 ip-172-31-27-8 audit: BPF prog-id=54 op=UNLOAD
Jun 29 11:44:02 ip-172-31-27-8 chronyd[1587]: Selected source ***************
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: CloudFormation signaled successfully with status SUCCESS
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: [2025-06-29T11:44:02.512Z] Tailing /var/log/eb-engine.log
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: ******************* eb-engine taillog *******************
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.062665 [INFO] 1001
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.063007 [INFO] configure bundle log for healthd...
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.063077 [INFO] Executing instruction: GetSetupProxyLog
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.063151 [INFO] Skipping Install yum packages
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.063157 [INFO] Skipping Install Python Bundle
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.063163 [INFO] Skipping Configure Python site-packages
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.063167 [INFO] Skipping Install Python Modules
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.063171 [INFO] Skipping MarkBaked
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.063175 [INFO] Instance has NOT been bootstrapped
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.063177 [INFO] Executing instruction: TuneSystemSettings
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.063179 [INFO] Starting TuneSystemSettings
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.063182 [INFO] Instance has NOT been bootstrapped
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.064497 [INFO] Executing instruction: GetSetupLogRotate
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.064503 [INFO] Initialize LogRotate files and directories
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: + RESULT=0
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.084253 [INFO] Instance has NOT been bootstrapped
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: + [[ 0 -ne 0 ]]
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.084273 [INFO] Executing instruction: BootstrapCFNHup
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: + exit 0
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.084277 [INFO] Bootstrap cfn-hup
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.088721 [INFO] Copying file /opt/elasticbeanstalk/config/private/aws-eb-command-handler.conf to /etc/cfn/hooks.d/aws-eb-command-handler.conf
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.091187 [INFO] Executing instruction: StartCFNHup
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.091201 [INFO] Start cfn-hup
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.091468 [INFO] Running command: systemctl show -p PartOf cfn-hup.service
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.121662 [INFO] cfn-hup is not registered with EB yet, registering it now
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.121700 [INFO] Running command: systemctl show -p PartOf cfn-hup.service
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.140132 [INFO] Running command: systemctl daemon-reload
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.389020 [INFO] Running command: systemctl reset-failed
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.402026 [INFO] Running command: systemctl is-enabled aws-eb.target
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.412223 [INFO] Running command: systemctl enable aws-eb.target
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.706147 [INFO] Running command: systemctl start aws-eb.target
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.715709 [INFO] Running command: systemctl enable cfn-hup.service
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.964592 [INFO] Created symlink /etc/systemd/system/multi-user.target.wants/cfn-hup.service → /etc/systemd/system/cfn-hup.service.
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.964627 [INFO] Running command: systemctl is-active cfn-hup.service
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.973690 [INFO] cfn-hup process is not running, starting it now
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.973721 [INFO] Running command: systemctl show -p PartOf cfn-hup.service
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.984228 [INFO] Running command: systemctl is-active cfn-hup.service
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:01.992600 [INFO] Running command: systemctl start cfn-hup.service
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:02.058000 [INFO] Instance has NOT been bootstrapped
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:02.058024 [INFO] Executing instruction: SetupPublishLogCronjob
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:02.058028 [INFO] Setup publish logs cron job...
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:02.058032 [INFO] Copying file /opt/elasticbeanstalk/config/private/logtasks/cron/publishlogs to /etc/cron.d/publishlogs
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:02.060479 [INFO] Instance has NOT been bootstrapped
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:02.060503 [INFO] Executing instruction: MarkBootstrapped
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:02.060507 [INFO] Starting MarkBootstrapped
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:02.060513 [INFO] Instance has NOT been bootstrapped
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:02.060605 [INFO] Marked instance as Bootstrapped
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:02.060610 [INFO] Executing instruction: Save CFN Stack Info
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:02.060676 [INFO] Executing cleanup logic
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025/06/29 11:44:02.060687 [INFO] Platform Engine finished execution on command: env-launch
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: ******************* End of taillog *******************
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: [2025-06-29T11:44:02.521Z] Tailing /var/log/eb-tools.log
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: ******************* eb-tools taillog *******************
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: ***eb-tools is not available yet.***
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: ******************* End of taillog *******************
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: [2025-06-29T11:44:02.524Z] Tailing /var/log/eb-hooks.log
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: ******************* eb-hooks taillog *******************
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: ***eb-hooks is not available yet.***
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: ******************* End of taillog *******************
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: [2025-06-29T11:44:02.526Z] Tailing /var/log/cfn-init.log
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: ******************* cfn-init taillog *******************
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025-06-29 11:43:58,529 [INFO] -----------------------Starting build-----------------------
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025-06-29 11:43:58,534 [INFO] Running configSets: _OnInstanceBoot
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025-06-29 11:43:58,537 [INFO] Running configSet _OnInstanceBoot
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025-06-29 11:43:58,539 [INFO] Running config AWSEBBaseConfig
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025-06-29 11:43:58,773 [INFO] Command clearbackupfiles succeeded
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025-06-29 11:43:58,776 [INFO] Running config AWSEBCfnHupEndpointOverride
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025-06-29 11:43:58,781 [INFO] Command clearbackupfiles succeeded
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025-06-29 11:43:58,782 [INFO] ConfigSets completed
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025-06-29 11:43:58,782 [INFO] -----------------------Build complete-----------------------
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: ******************* End of taillog *******************
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: [2025-06-29T11:44:02.529Z] Tailing /var/log/cfn-hup.log
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: ******************* cfn-hup taillog *******************
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025-06-29 11:44:02,364 [DEBUG] CloudFormation client initialized with endpoint https://cloudformation.us-west-2.amazonaws.com
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025-06-29 11:44:02,365 [DEBUG] SQS client initialized with endpoint https://sqs.us-west-2.amazonaws.com
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025-06-29 11:44:02,365 [DEBUG] CloudFormation client initialized with endpoint https://cloudformation.us-west-2.amazonaws.com
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025-06-29 11:44:02,365 [DEBUG] Enabled single threading mode.
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025-06-29 11:44:02,365 [DEBUG] Creating /var/lib/cfn-hup/data
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025-06-29 11:44:02,383 [INFO] No umask value specified in config file. Using the default one: 0o22
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 2025-06-29 11:44:02,452 [INFO] Pid: 1762
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: ******************* End of taillog *******************
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: 
Jun 29 11:44:02 ip-172-31-27-8 [eb-cfn-init]: [2025-06-29T11:44:02.532Z] Completed EB Bootstrapping Script.
Jun 29 11:44:02 ip-172-31-27-8 systemd[1]: Finished cloud-final.service - Execute cloud user/final scripts.
Jun 29 11:44:02 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=cloud-final comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:44:02 ip-172-31-27-8 audit: BPF prog-id=105 op=LOAD
Jun 29 11:44:02 ip-172-31-27-8 systemd[1]: Starting update-motd.service - Dynamically Generate Message Of The Day...
Jun 29 11:44:02 ip-172-31-27-8 systemd[1]: update-motd.service: Deactivated successfully.
Jun 29 11:44:02 ip-172-31-27-8 systemd[1]: Finished update-motd.service - Dynamically Generate Message Of The Day.
Jun 29 11:44:02 ip-172-31-27-8 systemd[1]: Reached target multi-user.target - Multi-User System.
Jun 29 11:44:02 ip-172-31-27-8 systemd[1]: Reached target cloud-init.target - Cloud-init target.
Jun 29 11:44:02 ip-172-31-27-8 systemd[1]: Reached target graphical.target - Graphical Interface.
Jun 29 11:44:02 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=update-motd comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:44:02 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=update-motd comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:44:02 ip-172-31-27-8 audit: BPF prog-id=105 op=UNLOAD
Jun 29 11:44:02 ip-172-31-27-8 systemd[1]: Starting systemd-update-utmp-runlevel.service - Record Runlevel Change in UTMP...
Jun 29 11:44:02 ip-172-31-27-8 audit[1807]: SYSTEM_RUNLEVEL pid=1807 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='old-level=N new-level=5 comm="systemd-update-utmp" exe="/usr/lib/systemd/systemd-update-utmp" hostname=? addr=? terminal=? res=success'
Jun 29 11:44:02 ip-172-31-27-8 systemd[1]: systemd-update-utmp-runlevel.service: Deactivated successfully.
Jun 29 11:44:03 ip-172-31-27-8 systemd[1]: Finished systemd-update-utmp-runlevel.service - Record Runlevel Change in UTMP.
Jun 29 11:44:03 ip-172-31-27-8 systemd[1]: Startup finished in 554ms (firmware) + 759ms (loader) + 362ms (kernel) + 1.096s (initrd) + 12.221s (userspace) = 14.993s.
Jun 29 11:44:03 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=systemd-update-utmp-runlevel comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:44:03 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=systemd-update-utmp-runlevel comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:44:26 ip-172-31-27-8 systemd[1]: systemd-hostnamed.service: Deactivated successfully.
Jun 29 11:44:26 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=systemd-hostnamed comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:44:26 ip-172-31-27-8 audit: BPF prog-id=98 op=UNLOAD
Jun 29 11:44:26 ip-172-31-27-8 audit: BPF prog-id=97 op=UNLOAD
Jun 29 11:44:26 ip-172-31-27-8 audit: BPF prog-id=96 op=UNLOAD
Jun 29 11:45:05 ip-172-31-27-8 systemd[1]: Created slice system-refresh\x2dpolicy\x2droutes.slice - Slice /system/refresh-policy-routes.
Jun 29 11:45:05 ip-172-31-27-8 systemd[1]: Starting <EMAIL> - Refresh policy routes for ens5...
Jun 29 11:45:05 ip-172-31-27-8 ec2net[1816]: Starting configuration refresh for ens5
Jun 29 11:45:05 ip-172-31-27-8 ec2net[1816]: No addresses found for ens5
Jun 29 11:45:05 ip-172-31-27-8 systemd[1]: <EMAIL>: Deactivated successfully.
Jun 29 11:45:05 ip-172-31-27-8 systemd[1]: Finished <EMAIL> - Refresh policy routes for ens5.
Jun 29 11:45:05 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=refresh-policy-routes@ens5 comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:45:05 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=refresh-policy-routes@ens5 comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:46:22 ip-172-31-27-8 systemd[1]: Starting <EMAIL> - Refresh policy routes for ens5...
Jun 29 11:46:22 ip-172-31-27-8 ec2net[1944]: Starting configuration refresh for ens5
Jun 29 11:46:22 ip-172-31-27-8 ec2net[1944]: No addresses found for ens5
Jun 29 11:46:22 ip-172-31-27-8 systemd[1]: <EMAIL>: Deactivated successfully.
Jun 29 11:46:22 ip-172-31-27-8 systemd[1]: Finished <EMAIL> - Refresh policy routes for ens5.
Jun 29 11:46:22 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=refresh-policy-routes@ens5 comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:46:22 ip-172-31-27-8 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=refresh-policy-routes@ens5 comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:46:30 ip-172-31-27-8 systemd[1]: Reloading.
Jun 29 11:46:30 ip-172-31-27-8 zram_generator::config[2033]: zram0: system has too much memory (904MB), limit is 800MB, ignoring.
Jun 29 11:46:30 ip-172-31-27-8 systemd[1]: /usr/lib/systemd/system/acpid.socket:6: ListenStream= references a path below legacy directory /var/run/, updating /var/run/acpid.socket → /run/acpid.socket; please update the unit file accordingly.
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=106 op=LOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=89 op=UNLOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=107 op=LOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=90 op=UNLOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=108 op=LOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=91 op=UNLOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=109 op=LOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=92 op=UNLOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=110 op=LOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=111 op=LOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=93 op=UNLOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=94 op=UNLOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=112 op=LOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=95 op=UNLOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=113 op=LOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=99 op=UNLOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=114 op=LOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=115 op=LOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=100 op=UNLOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=101 op=UNLOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=116 op=LOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=102 op=UNLOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=117 op=LOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=118 op=LOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=103 op=UNLOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=104 op=UNLOAD
Jun 29 11:46:30 ip-172-31-27-8 systemd[1]: Reloading.
Jun 29 11:46:30 ip-172-31-27-8 zram_generator::config[2060]: zram0: system has too much memory (904MB), limit is 800MB, ignoring.
Jun 29 11:46:30 ip-172-31-27-8 systemd[1]: /usr/lib/systemd/system/acpid.socket:6: ListenStream= references a path below legacy directory /var/run/, updating /var/run/acpid.socket → /run/acpid.socket; please update the unit file accordingly.
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=119 op=LOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=106 op=UNLOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=120 op=LOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=107 op=UNLOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=121 op=LOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=108 op=UNLOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=122 op=LOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=109 op=UNLOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=123 op=LOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=124 op=LOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=110 op=UNLOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=111 op=UNLOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=125 op=LOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=112 op=UNLOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=126 op=LOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=113 op=UNLOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=127 op=LOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=128 op=LOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=114 op=UNLOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=115 op=UNLOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=129 op=LOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=116 op=UNLOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=130 op=LOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=131 op=LOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=117 op=UNLOAD
Jun 29 11:46:30 ip-172-31-27-8 audit: BPF prog-id=118 op=UNLOAD
Jun 29 11:46:30 ip-172-31-27-8 systemd[1]: Reloading.
Jun 29 11:46:30 ip-172-31-27-8 zram_generator::config[2086]: zram0: system has too much memory (904MB), limit is 800MB, ignoring.
Jun 29 11:46:30 ip-172-31-27-8 systemd[1]: /usr/lib/systemd/system/acpid.socket:6: ListenStream= references a path below legacy directory /var/run/, updating /var/run/acpid.socket → /run/acpid.socket; please update the unit file accordingly.
Jun 29 11:46:31 ip-172-31-27-8 audit: BPF prog-id=132 op=LOAD
Jun 29 11:46:31 ip-172-31-27-8 audit: BPF prog-id=119 op=UNLOAD
Jun 29 11:46:31 ip-172-31-27-8 audit: BPF prog-id=133 op=LOAD
Jun 29 11:46:31 ip-172-31-27-8 audit: BPF prog-id=120 op=UNLOAD
Jun 29 11:46:31 ip-172-31-27-8 audit: BPF prog-id=134 op=LOAD
Jun 29 11:46:31 ip-172-31-27-8 audit: BPF prog-id=121 op=UNLOAD
Jun 29 11:46:31 ip-172-31-27-8 audit: BPF prog-id=135 op=LOAD
Jun 29 11:46:31 ip-172-31-27-8 audit: BPF prog-id=122 op=UNLOAD
Jun 29 11:46:31 ip-172-31-27-8 audit: BPF prog-id=136 op=LOAD
Jun 29 11:46:31 ip-172-31-27-8 audit: BPF prog-id=137 op=LOAD
Jun 29 11:46:31 ip-172-31-27-8 audit: BPF prog-id=123 op=UNLOAD
Jun 29 11:46:31 ip-172-31-27-8 audit: BPF prog-id=124 op=UNLOAD
Jun 29 11:46:31 ip-172-31-27-8 audit: BPF prog-id=138 op=LOAD
Jun 29 11:46:31 ip-172-31-27-8 audit: BPF prog-id=125 op=UNLOAD
Jun 29 11:46:31 ip-172-31-27-8 audit: BPF prog-id=139 op=LOAD
Jun 29 11:46:31 ip-172-31-27-8 audit: BPF prog-id=126 op=UNLOAD
Jun 29 11:46:31 ip-172-31-27-8 audit: BPF prog-id=140 op=LOAD
Jun 29 11:46:31 ip-172-31-27-8 audit: BPF prog-id=141 op=LOAD
Jun 29 11:46:31 ip-172-31-27-8 audit: BPF prog-id=127 op=UNLOAD
Jun 29 11:46:31 ip-172-31-27-8 audit: BPF prog-id=128 op=UNLOAD
Jun 29 11:46:31 ip-172-31-27-8 audit: BPF prog-id=142 op=LOAD
Jun 29 11:46:31 ip-172-31-27-8 audit: BPF prog-id=129 op=UNLOAD
Jun 29 11:46:31 ip-172-31-27-8 audit: BPF prog-id=143 op=LOAD
Jun 29 11:46:31 ip-172-31-27-8 audit: BPF prog-id=144 op=LOAD
Jun 29 11:46:31 ip-172-31-27-8 audit: BPF prog-id=130 op=UNLOAD
Jun 29 11:46:31 ip-172-31-27-8 audit: BPF prog-id=131 op=UNLOAD
Jun 29 11:46:31 ip-172-31-27-8 systemd[1]: Starting healthd.service - This is healthd daemon...
Jun 29 11:46:31 ip-172-31-27-8 systemd[1]: Started healthd.service - This is healthd daemon.
Jun 29 11:46:31 ip-172-31-27-8 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=healthd comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:46:31 ip-172-31-27-8 rsyslogd[1371]: imjournal: journal files changed, reloading...  [v8.2204.0-3.amzn2023.0.4 try https://www.rsyslog.com/e/0 ]
Jun 29 11:46:31 ip-172-31-27-8 systemd-journald[829]: Data hash table of /run/log/journal/ec28f7ae11495954c947725fc2940e61/system.journal has a fill level at 75.1 (3097 of 4124 items, 2375680 file size, 767 bytes per hash table item), suggesting rotation.
Jun 29 11:46:31 ip-172-31-27-8 systemd-journald[829]: /run/log/journal/ec28f7ae11495954c947725fc2940e61/system.journal: Journal header limits reached or header out-of-date, rotating.
Jun 29 11:46:31 ip-172-31-4-198 systemd[1]: Stopping rsyslog.service - System Logging Service...
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/puma-6.6.0/lib/puma/launcher.rb:92: warning: conflicting chdir during another chdir block
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/bin/healthd:19: note: previous chdir was here
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: * Listening on http://127.0.0.1:22221
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: NOTE: Gem::Platform.match is deprecated; use Gem::Platform.match_spec? or match_gem? instead. It will be removed in Rubygems 4
Jun 29 11:46:32 ip-172-31-4-198 healthd[2094]: Gem::Platform.match called from /opt/elasticbeanstalk/lib/ruby/lib/ruby/gems/3.4.0/gems/healthd-1.0.7/lib/healthd/daemon/plugins/manager.rb:72.
Jun 29 11:46:32 ip-172-31-4-198 rsyslogd[1371]: [origin software="rsyslogd" swVersion="8.2204.0-3.amzn2023.0.4" x-pid="1371" x-info="https://www.rsyslog.com"] exiting on signal 15.
Jun 29 11:46:32 ip-172-31-4-198 systemd[1]: rsyslog.service: Deactivated successfully.
Jun 29 11:46:32 ip-172-31-4-198 systemd[1]: Stopped rsyslog.service - System Logging Service.
Jun 29 11:46:32 ip-172-31-4-198 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=rsyslog comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:46:32 ip-172-31-4-198 systemd[1]: Starting rsyslog.service - System Logging Service...
Jun 29 11:46:32 ip-172-31-4-198 systemd[1]: Started rsyslog.service - System Logging Service.
Jun 29 11:46:32 ip-172-31-4-198 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=rsyslog comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:46:32 ip-172-31-4-198 rsyslogd[2112]: [origin software="rsyslogd" swVersion="8.2204.0-3.amzn2023.0.4" x-pid="2112" x-info="https://www.rsyslog.com"] start
Jun 29 11:46:32 ip-172-31-4-198 rsyslogd[2112]: imjournal: journal files changed, reloading...  [v8.2204.0-3.amzn2023.0.4 try https://www.rsyslog.com/e/0 ]
Jun 29 11:46:32 ip-172-31-4-198 systemd[1]: Reloading.
Jun 29 11:46:32 ip-172-31-4-198 zram_generator::config[2152]: zram0: system has too much memory (904MB), limit is 800MB, ignoring.
Jun 29 11:46:32 ip-172-31-4-198 systemd[1]: /usr/lib/systemd/system/acpid.socket:6: ListenStream= references a path below legacy directory /var/run/, updating /var/run/acpid.socket → /run/acpid.socket; please update the unit file accordingly.
Jun 29 11:46:32 ip-172-31-4-198 audit: BPF prog-id=145 op=LOAD
Jun 29 11:46:32 ip-172-31-4-198 audit: BPF prog-id=132 op=UNLOAD
Jun 29 11:46:32 ip-172-31-4-198 audit: BPF prog-id=146 op=LOAD
Jun 29 11:46:32 ip-172-31-4-198 audit: BPF prog-id=133 op=UNLOAD
Jun 29 11:46:32 ip-172-31-4-198 audit: BPF prog-id=147 op=LOAD
Jun 29 11:46:32 ip-172-31-4-198 audit: BPF prog-id=134 op=UNLOAD
Jun 29 11:46:32 ip-172-31-4-198 audit: BPF prog-id=148 op=LOAD
Jun 29 11:46:32 ip-172-31-4-198 audit: BPF prog-id=135 op=UNLOAD
Jun 29 11:46:32 ip-172-31-4-198 audit: BPF prog-id=149 op=LOAD
Jun 29 11:46:32 ip-172-31-4-198 audit: BPF prog-id=150 op=LOAD
Jun 29 11:46:32 ip-172-31-4-198 audit: BPF prog-id=136 op=UNLOAD
Jun 29 11:46:32 ip-172-31-4-198 audit: BPF prog-id=137 op=UNLOAD
Jun 29 11:46:32 ip-172-31-4-198 audit: BPF prog-id=151 op=LOAD
Jun 29 11:46:32 ip-172-31-4-198 audit: BPF prog-id=138 op=UNLOAD
Jun 29 11:46:32 ip-172-31-4-198 audit: BPF prog-id=152 op=LOAD
Jun 29 11:46:32 ip-172-31-4-198 audit: BPF prog-id=139 op=UNLOAD
Jun 29 11:46:32 ip-172-31-4-198 audit: BPF prog-id=153 op=LOAD
Jun 29 11:46:32 ip-172-31-4-198 audit: BPF prog-id=154 op=LOAD
Jun 29 11:46:32 ip-172-31-4-198 audit: BPF prog-id=140 op=UNLOAD
Jun 29 11:46:32 ip-172-31-4-198 audit: BPF prog-id=141 op=UNLOAD
Jun 29 11:46:32 ip-172-31-4-198 audit: BPF prog-id=155 op=LOAD
Jun 29 11:46:32 ip-172-31-4-198 audit: BPF prog-id=142 op=UNLOAD
Jun 29 11:46:32 ip-172-31-4-198 audit: BPF prog-id=156 op=LOAD
Jun 29 11:46:32 ip-172-31-4-198 audit: BPF prog-id=157 op=LOAD
Jun 29 11:46:32 ip-172-31-4-198 audit: BPF prog-id=143 op=UNLOAD
Jun 29 11:46:32 ip-172-31-4-198 audit: BPF prog-id=144 op=UNLOAD
Jun 29 11:46:32 ip-172-31-4-198 systemd[1]: Reloading.
Jun 29 11:46:33 ip-172-31-4-198 zram_generator::config[2179]: zram0: system has too much memory (904MB), limit is 800MB, ignoring.
Jun 29 11:46:33 ip-172-31-4-198 systemd[1]: /usr/lib/systemd/system/acpid.socket:6: ListenStream= references a path below legacy directory /var/run/, updating /var/run/acpid.socket → /run/acpid.socket; please update the unit file accordingly.
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=158 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=145 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=159 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=146 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=160 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=147 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=161 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=148 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=162 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=163 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=149 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=150 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=164 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=151 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=165 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=152 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=166 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=167 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=153 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=154 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=168 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=155 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=169 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=170 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=156 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=157 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 systemd[1]: Reached target eb-app.target.
Jun 29 11:46:33 ip-172-31-4-198 systemd[1]: Reloading.
Jun 29 11:46:33 ip-172-31-4-198 zram_generator::config[2205]: zram0: system has too much memory (904MB), limit is 800MB, ignoring.
Jun 29 11:46:33 ip-172-31-4-198 systemd[1]: /usr/lib/systemd/system/acpid.socket:6: ListenStream= references a path below legacy directory /var/run/, updating /var/run/acpid.socket → /run/acpid.socket; please update the unit file accordingly.
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=171 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=158 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=172 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=159 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=173 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=160 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=174 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=161 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=175 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=176 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=162 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=163 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=177 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=164 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=178 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=165 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=179 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=180 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=166 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=167 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=181 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=168 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=182 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=183 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=169 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=170 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 systemd[1]: Starting web.service - This is web daemon...
Jun 29 11:46:33 ip-172-31-4-198 systemd[1]: Started web.service - This is web daemon.
Jun 29 11:46:33 ip-172-31-4-198 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=web comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:46:33 ip-172-31-4-198 audit[2217]: AVC avc:  denied  { read } for  pid=2217 comm="nginx" name="nginx.conf" dev="nvme0n1p1" ino=11791838 scontext=system_u:system_r:httpd_t:s0 tcontext=system_u:object_r:var_t:s0 tclass=file permissive=1
Jun 29 11:46:33 ip-172-31-4-198 audit[2217]: AVC avc:  denied  { open } for  pid=2217 comm="nginx" path="/var/proxy/staging/nginx/nginx.conf" dev="nvme0n1p1" ino=11791838 scontext=system_u:system_r:httpd_t:s0 tcontext=system_u:object_r:var_t:s0 tclass=file permissive=1
Jun 29 11:46:33 ip-172-31-4-198 audit[2217]: AVC avc:  denied  { getattr } for  pid=2217 comm="nginx" path="/var/proxy/staging/nginx/nginx.conf" dev="nvme0n1p1" ino=11791838 scontext=system_u:system_r:httpd_t:s0 tcontext=system_u:object_r:var_t:s0 tclass=file permissive=1
Jun 29 11:46:33 ip-172-31-4-198 systemd[1]: Reloading.
Jun 29 11:46:33 ip-172-31-4-198 zram_generator::config[2240]: zram0: system has too much memory (904MB), limit is 800MB, ignoring.
Jun 29 11:46:33 ip-172-31-4-198 web[2213]: [2025-06-29 11:46:33 +0000] [2213] [INFO] Starting gunicorn 23.0.0
Jun 29 11:46:33 ip-172-31-4-198 web[2213]: [2025-06-29 11:46:33 +0000] [2213] [INFO] Listening at: http://127.0.0.1:8000 (2213)
Jun 29 11:46:33 ip-172-31-4-198 web[2213]: [2025-06-29 11:46:33 +0000] [2213] [INFO] Using worker: sync
Jun 29 11:46:33 ip-172-31-4-198 web[2245]: [2025-06-29 11:46:33 +0000] [2245] [INFO] Booting worker with pid: 2245
Jun 29 11:46:33 ip-172-31-4-198 systemd[1]: /usr/lib/systemd/system/acpid.socket:6: ListenStream= references a path below legacy directory /var/run/, updating /var/run/acpid.socket → /run/acpid.socket; please update the unit file accordingly.
Jun 29 11:46:33 ip-172-31-4-198 web[2245]: [2025-06-29 11:46:33 +0000] [2245] [ERROR] Exception in worker process
Jun 29 11:46:33 ip-172-31-4-198 web[2245]: Traceback (most recent call last):
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:    worker.init_process()
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 135, in init_process
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:    self.load_wsgi()
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:    self.wsgi = self.app.wsgi()
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:                ^^^^^^^^^^^^^^^
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/base.py", line 66, in wsgi
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:    self.callable = self.load()
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:                    ^^^^^^^^^^^
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:    return self.load_wsgiapp()
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:           ^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:    return util.import_app(self.app_uri)
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/util.py", line 370, in import_app
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:    mod = importlib.import_module(module)
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:  File "/usr/lib64/python3.11/importlib/__init__.py", line 126, in import_module
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:    return _bootstrap._gcd_import(name[level:], package, level)
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
Jun 29 11:46:33 ip-172-31-4-198 web[2245]: ModuleNotFoundError: No module named 'aws_processing_api'
Jun 29 11:46:33 ip-172-31-4-198 web[2245]: [2025-06-29 11:46:33 +0000] [2245] [INFO] Worker exiting (pid: 2245)
Jun 29 11:46:33 ip-172-31-4-198 web[2213]: [2025-06-29 11:46:33 +0000] [2213] [ERROR] Worker (pid:2245) exited with code 3
Jun 29 11:46:33 ip-172-31-4-198 web[2213]: [2025-06-29 11:46:33 +0000] [2213] [ERROR] Shutting down: Master
Jun 29 11:46:33 ip-172-31-4-198 web[2213]: [2025-06-29 11:46:33 +0000] [2213] [ERROR] Reason: Worker failed to boot.
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=184 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=171 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=185 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=172 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=186 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=173 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=187 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=174 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=188 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=189 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=175 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=176 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=190 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=177 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=191 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=178 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=192 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=193 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=179 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=180 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=194 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=181 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=195 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=196 op=LOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=182 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 audit: BPF prog-id=183 op=UNLOAD
Jun 29 11:46:33 ip-172-31-4-198 systemd[1]: web.service: Main process exited, code=exited, status=3/NOTIMPLEMENTED
Jun 29 11:46:33 ip-172-31-4-198 systemd[1]: web.service: Failed with result 'exit-code'.
Jun 29 11:46:34 ip-172-31-4-198 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=web comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=failed'
Jun 29 11:46:34 ip-172-31-4-198 systemd[1]: Starting nginx.service - The nginx HTTP and reverse proxy server...
Jun 29 11:46:34 ip-172-31-4-198 nginx[2252]: nginx: [warn] could not build optimal types_hash, you should increase either types_hash_max_size: 1024 or types_hash_bucket_size: 64; ignoring types_hash_bucket_size
Jun 29 11:46:34 ip-172-31-4-198 nginx[2252]: nginx: the configuration file /etc/nginx/nginx.conf syntax is ok
Jun 29 11:46:34 ip-172-31-4-198 nginx[2252]: nginx: configuration file /etc/nginx/nginx.conf test is successful
Jun 29 11:46:34 ip-172-31-4-198 systemd[1]: web.service: Scheduled restart job, restart counter is at 1.
Jun 29 11:46:34 ip-172-31-4-198 systemd[1]: Stopped web.service - This is web daemon.
Jun 29 11:46:34 ip-172-31-4-198 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=web comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:46:34 ip-172-31-4-198 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=web comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:46:34 ip-172-31-4-198 systemd[1]: Starting web.service - This is web daemon...
Jun 29 11:46:34 ip-172-31-4-198 nginx[2253]: nginx: [warn] could not build optimal types_hash, you should increase either types_hash_max_size: 1024 or types_hash_bucket_size: 64; ignoring types_hash_bucket_size
Jun 29 11:46:34 ip-172-31-4-198 audit[2259]: AVC avc:  denied  { sys_resource } for  pid=2259 comm="nginx" capability=24  scontext=system_u:system_r:httpd_t:s0 tcontext=system_u:system_r:httpd_t:s0 tclass=capability permissive=1
Jun 29 11:46:34 ip-172-31-4-198 systemd[1]: Started web.service - This is web daemon.
Jun 29 11:46:34 ip-172-31-4-198 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=web comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:46:34 ip-172-31-4-198 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=nginx comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:46:34 ip-172-31-4-198 systemd[1]: Started nginx.service - The nginx HTTP and reverse proxy server.
Jun 29 11:46:34 ip-172-31-4-198 web[2254]: [2025-06-29 11:46:34 +0000] [2254] [INFO] Starting gunicorn 23.0.0
Jun 29 11:46:34 ip-172-31-4-198 web[2254]: [2025-06-29 11:46:34 +0000] [2254] [INFO] Listening at: http://127.0.0.1:8000 (2254)
Jun 29 11:46:34 ip-172-31-4-198 web[2254]: [2025-06-29 11:46:34 +0000] [2254] [INFO] Using worker: sync
Jun 29 11:46:34 ip-172-31-4-198 web[2272]: [2025-06-29 11:46:34 +0000] [2272] [INFO] Booting worker with pid: 2272
Jun 29 11:46:34 ip-172-31-4-198 web[2272]: [2025-06-29 11:46:34 +0000] [2272] [ERROR] Exception in worker process
Jun 29 11:46:34 ip-172-31-4-198 web[2272]: Traceback (most recent call last):
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:    worker.init_process()
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 135, in init_process
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:    self.load_wsgi()
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:    self.wsgi = self.app.wsgi()
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:                ^^^^^^^^^^^^^^^
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/base.py", line 66, in wsgi
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:    self.callable = self.load()
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:                    ^^^^^^^^^^^
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:    return self.load_wsgiapp()
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:           ^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:    return util.import_app(self.app_uri)
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/util.py", line 370, in import_app
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:    mod = importlib.import_module(module)
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:  File "/usr/lib64/python3.11/importlib/__init__.py", line 126, in import_module
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:    return _bootstrap._gcd_import(name[level:], package, level)
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
Jun 29 11:46:34 ip-172-31-4-198 web[2272]: ModuleNotFoundError: No module named 'aws_processing_api'
Jun 29 11:46:34 ip-172-31-4-198 web[2272]: [2025-06-29 11:46:34 +0000] [2272] [INFO] Worker exiting (pid: 2272)
Jun 29 11:46:34 ip-172-31-4-198 web[2254]: [2025-06-29 11:46:34 +0000] [2254] [ERROR] Worker (pid:2272) exited with code 3
Jun 29 11:46:34 ip-172-31-4-198 web[2254]: [2025-06-29 11:46:34 +0000] [2254] [ERROR] Shutting down: Master
Jun 29 11:46:34 ip-172-31-4-198 web[2254]: [2025-06-29 11:46:34 +0000] [2254] [ERROR] Reason: Worker failed to boot.
Jun 29 11:46:34 ip-172-31-4-198 systemd[1]: web.service: Main process exited, code=exited, status=3/NOTIMPLEMENTED
Jun 29 11:46:34 ip-172-31-4-198 systemd[1]: web.service: Failed with result 'exit-code'.
Jun 29 11:46:34 ip-172-31-4-198 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=web comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=failed'
Jun 29 11:46:34 ip-172-31-4-198 systemd[1]: web.service: Scheduled restart job, restart counter is at 2.
Jun 29 11:46:34 ip-172-31-4-198 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=web comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:46:34 ip-172-31-4-198 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=web comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:46:34 ip-172-31-4-198 systemd[1]: Stopped web.service - This is web daemon.
Jun 29 11:46:34 ip-172-31-4-198 systemd[1]: Starting web.service - This is web daemon...
Jun 29 11:46:34 ip-172-31-4-198 systemd[1]: Started web.service - This is web daemon.
Jun 29 11:46:34 ip-172-31-4-198 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=web comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:46:34 ip-172-31-4-198 web[2276]: [2025-06-29 11:46:34 +0000] [2276] [INFO] Starting gunicorn 23.0.0
Jun 29 11:46:34 ip-172-31-4-198 web[2276]: [2025-06-29 11:46:34 +0000] [2276] [INFO] Listening at: http://127.0.0.1:8000 (2276)
Jun 29 11:46:34 ip-172-31-4-198 web[2276]: [2025-06-29 11:46:34 +0000] [2276] [INFO] Using worker: sync
Jun 29 11:46:34 ip-172-31-4-198 web[2280]: [2025-06-29 11:46:34 +0000] [2280] [INFO] Booting worker with pid: 2280
Jun 29 11:46:34 ip-172-31-4-198 web[2280]: [2025-06-29 11:46:34 +0000] [2280] [ERROR] Exception in worker process
Jun 29 11:46:34 ip-172-31-4-198 web[2280]: Traceback (most recent call last):
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:    worker.init_process()
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 135, in init_process
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:    self.load_wsgi()
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:    self.wsgi = self.app.wsgi()
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:                ^^^^^^^^^^^^^^^
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/base.py", line 66, in wsgi
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:    self.callable = self.load()
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:                    ^^^^^^^^^^^
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:    return self.load_wsgiapp()
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:           ^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:    return util.import_app(self.app_uri)
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/util.py", line 370, in import_app
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:    mod = importlib.import_module(module)
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:  File "/usr/lib64/python3.11/importlib/__init__.py", line 126, in import_module
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:    return _bootstrap._gcd_import(name[level:], package, level)
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
Jun 29 11:46:34 ip-172-31-4-198 web[2280]: ModuleNotFoundError: No module named 'aws_processing_api'
Jun 29 11:46:34 ip-172-31-4-198 web[2280]: [2025-06-29 11:46:34 +0000] [2280] [INFO] Worker exiting (pid: 2280)
Jun 29 11:46:34 ip-172-31-4-198 web[2276]: [2025-06-29 11:46:34 +0000] [2276] [ERROR] Worker (pid:2280) exited with code 3
Jun 29 11:46:34 ip-172-31-4-198 web[2276]: [2025-06-29 11:46:34 +0000] [2276] [ERROR] Shutting down: Master
Jun 29 11:46:34 ip-172-31-4-198 web[2276]: [2025-06-29 11:46:34 +0000] [2276] [ERROR] Reason: Worker failed to boot.
Jun 29 11:46:34 ip-172-31-4-198 systemd[1]: web.service: Main process exited, code=exited, status=3/NOTIMPLEMENTED
Jun 29 11:46:34 ip-172-31-4-198 systemd[1]: web.service: Failed with result 'exit-code'.
Jun 29 11:46:34 ip-172-31-4-198 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=web comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=failed'
Jun 29 11:46:35 ip-172-31-4-198 systemd[1]: web.service: Scheduled restart job, restart counter is at 3.
Jun 29 11:46:35 ip-172-31-4-198 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=web comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:46:35 ip-172-31-4-198 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=web comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:46:35 ip-172-31-4-198 systemd[1]: Stopped web.service - This is web daemon.
Jun 29 11:46:35 ip-172-31-4-198 systemd[1]: Starting web.service - This is web daemon...
Jun 29 11:46:35 ip-172-31-4-198 systemd[1]: Started web.service - This is web daemon.
Jun 29 11:46:35 ip-172-31-4-198 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=web comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:46:35 ip-172-31-4-198 web[2282]: [2025-06-29 11:46:35 +0000] [2282] [INFO] Starting gunicorn 23.0.0
Jun 29 11:46:35 ip-172-31-4-198 web[2282]: [2025-06-29 11:46:35 +0000] [2282] [INFO] Listening at: http://127.0.0.1:8000 (2282)
Jun 29 11:46:35 ip-172-31-4-198 web[2282]: [2025-06-29 11:46:35 +0000] [2282] [INFO] Using worker: sync
Jun 29 11:46:35 ip-172-31-4-198 web[2286]: [2025-06-29 11:46:35 +0000] [2286] [INFO] Booting worker with pid: 2286
Jun 29 11:46:35 ip-172-31-4-198 web[2286]: [2025-06-29 11:46:35 +0000] [2286] [ERROR] Exception in worker process
Jun 29 11:46:35 ip-172-31-4-198 web[2286]: Traceback (most recent call last):
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:    worker.init_process()
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 135, in init_process
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:    self.load_wsgi()
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:    self.wsgi = self.app.wsgi()
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:                ^^^^^^^^^^^^^^^
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/base.py", line 66, in wsgi
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:    self.callable = self.load()
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:                    ^^^^^^^^^^^
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:    return self.load_wsgiapp()
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:           ^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:    return util.import_app(self.app_uri)
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/util.py", line 370, in import_app
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:    mod = importlib.import_module(module)
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:  File "/usr/lib64/python3.11/importlib/__init__.py", line 126, in import_module
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:    return _bootstrap._gcd_import(name[level:], package, level)
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
Jun 29 11:46:35 ip-172-31-4-198 web[2286]: ModuleNotFoundError: No module named 'aws_processing_api'
Jun 29 11:46:35 ip-172-31-4-198 web[2286]: [2025-06-29 11:46:35 +0000] [2286] [INFO] Worker exiting (pid: 2286)
Jun 29 11:46:35 ip-172-31-4-198 web[2282]: [2025-06-29 11:46:35 +0000] [2282] [ERROR] Worker (pid:2286) exited with code 3
Jun 29 11:46:35 ip-172-31-4-198 web[2282]: [2025-06-29 11:46:35 +0000] [2282] [ERROR] Shutting down: Master
Jun 29 11:46:35 ip-172-31-4-198 web[2282]: [2025-06-29 11:46:35 +0000] [2282] [ERROR] Reason: Worker failed to boot.
Jun 29 11:46:35 ip-172-31-4-198 systemd[1]: web.service: Main process exited, code=exited, status=3/NOTIMPLEMENTED
Jun 29 11:46:35 ip-172-31-4-198 systemd[1]: web.service: Failed with result 'exit-code'.
Jun 29 11:46:35 ip-172-31-4-198 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=web comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=failed'
Jun 29 11:46:35 ip-172-31-4-198 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=web comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:46:35 ip-172-31-4-198 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=web comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:46:35 ip-172-31-4-198 systemd[1]: web.service: Scheduled restart job, restart counter is at 4.
Jun 29 11:46:35 ip-172-31-4-198 systemd[1]: Stopped web.service - This is web daemon.
Jun 29 11:46:35 ip-172-31-4-198 systemd[1]: Starting web.service - This is web daemon...
Jun 29 11:46:35 ip-172-31-4-198 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=web comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:46:35 ip-172-31-4-198 systemd[1]: Started web.service - This is web daemon.
Jun 29 11:46:35 ip-172-31-4-198 web[2288]: [2025-06-29 11:46:35 +0000] [2288] [INFO] Starting gunicorn 23.0.0
Jun 29 11:46:35 ip-172-31-4-198 web[2288]: [2025-06-29 11:46:35 +0000] [2288] [INFO] Listening at: http://127.0.0.1:8000 (2288)
Jun 29 11:46:35 ip-172-31-4-198 web[2288]: [2025-06-29 11:46:35 +0000] [2288] [INFO] Using worker: sync
Jun 29 11:46:35 ip-172-31-4-198 web[2292]: [2025-06-29 11:46:35 +0000] [2292] [INFO] Booting worker with pid: 2292
Jun 29 11:46:35 ip-172-31-4-198 web[2292]: [2025-06-29 11:46:35 +0000] [2292] [ERROR] Exception in worker process
Jun 29 11:46:35 ip-172-31-4-198 web[2292]: Traceback (most recent call last):
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:    worker.init_process()
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 135, in init_process
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:    self.load_wsgi()
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:    self.wsgi = self.app.wsgi()
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:                ^^^^^^^^^^^^^^^
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/base.py", line 66, in wsgi
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:    self.callable = self.load()
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:                    ^^^^^^^^^^^
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:    return self.load_wsgiapp()
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:           ^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:    return util.import_app(self.app_uri)
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/util.py", line 370, in import_app
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:    mod = importlib.import_module(module)
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:  File "/usr/lib64/python3.11/importlib/__init__.py", line 126, in import_module
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:    return _bootstrap._gcd_import(name[level:], package, level)
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
Jun 29 11:46:35 ip-172-31-4-198 web[2292]: ModuleNotFoundError: No module named 'aws_processing_api'
Jun 29 11:46:35 ip-172-31-4-198 web[2292]: [2025-06-29 11:46:35 +0000] [2292] [INFO] Worker exiting (pid: 2292)
Jun 29 11:46:35 ip-172-31-4-198 web[2288]: [2025-06-29 11:46:35 +0000] [2288] [ERROR] Worker (pid:2292) exited with code 3
Jun 29 11:46:35 ip-172-31-4-198 web[2288]: [2025-06-29 11:46:35 +0000] [2288] [ERROR] Shutting down: Master
Jun 29 11:46:35 ip-172-31-4-198 web[2288]: [2025-06-29 11:46:35 +0000] [2288] [ERROR] Reason: Worker failed to boot.
Jun 29 11:46:35 ip-172-31-4-198 systemd[1]: web.service: Main process exited, code=exited, status=3/NOTIMPLEMENTED
Jun 29 11:46:35 ip-172-31-4-198 systemd[1]: web.service: Failed with result 'exit-code'.
Jun 29 11:46:36 ip-172-31-4-198 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=web comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=failed'
Jun 29 11:46:36 ip-172-31-4-198 systemd[1]: web.service: Scheduled restart job, restart counter is at 5.
Jun 29 11:46:36 ip-172-31-4-198 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=web comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:46:36 ip-172-31-4-198 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=web comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:46:36 ip-172-31-4-198 systemd[1]: Stopped web.service - This is web daemon.
Jun 29 11:46:36 ip-172-31-4-198 systemd[1]: Starting web.service - This is web daemon...
Jun 29 11:46:36 ip-172-31-4-198 systemd[1]: Started web.service - This is web daemon.
Jun 29 11:46:36 ip-172-31-4-198 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=web comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:46:36 ip-172-31-4-198 web[2294]: [2025-06-29 11:46:36 +0000] [2294] [INFO] Starting gunicorn 23.0.0
Jun 29 11:46:36 ip-172-31-4-198 web[2294]: [2025-06-29 11:46:36 +0000] [2294] [INFO] Listening at: http://127.0.0.1:8000 (2294)
Jun 29 11:46:36 ip-172-31-4-198 web[2294]: [2025-06-29 11:46:36 +0000] [2294] [INFO] Using worker: sync
Jun 29 11:46:36 ip-172-31-4-198 web[2298]: [2025-06-29 11:46:36 +0000] [2298] [INFO] Booting worker with pid: 2298
Jun 29 11:46:36 ip-172-31-4-198 web[2298]: [2025-06-29 11:46:36 +0000] [2298] [ERROR] Exception in worker process
Jun 29 11:46:36 ip-172-31-4-198 web[2298]: Traceback (most recent call last):
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:    worker.init_process()
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 135, in init_process
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:    self.load_wsgi()
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:    self.wsgi = self.app.wsgi()
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:                ^^^^^^^^^^^^^^^
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/base.py", line 66, in wsgi
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:    self.callable = self.load()
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:                    ^^^^^^^^^^^
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:    return self.load_wsgiapp()
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:           ^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:    return util.import_app(self.app_uri)
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/util.py", line 370, in import_app
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:    mod = importlib.import_module(module)
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:  File "/usr/lib64/python3.11/importlib/__init__.py", line 126, in import_module
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:    return _bootstrap._gcd_import(name[level:], package, level)
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
Jun 29 11:46:36 ip-172-31-4-198 web[2298]: ModuleNotFoundError: No module named 'aws_processing_api'
Jun 29 11:46:36 ip-172-31-4-198 web[2298]: [2025-06-29 11:46:36 +0000] [2298] [INFO] Worker exiting (pid: 2298)
Jun 29 11:46:36 ip-172-31-4-198 web[2294]: [2025-06-29 11:46:36 +0000] [2294] [ERROR] Worker (pid:2298) exited with code 3
Jun 29 11:46:36 ip-172-31-4-198 web[2294]: [2025-06-29 11:46:36 +0000] [2294] [ERROR] Shutting down: Master
Jun 29 11:46:36 ip-172-31-4-198 web[2294]: [2025-06-29 11:46:36 +0000] [2294] [ERROR] Reason: Worker failed to boot.
Jun 29 11:46:36 ip-172-31-4-198 systemd[1]: web.service: Main process exited, code=exited, status=3/NOTIMPLEMENTED
Jun 29 11:46:36 ip-172-31-4-198 systemd[1]: web.service: Failed with result 'exit-code'.
Jun 29 11:46:36 ip-172-31-4-198 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=web comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=failed'
Jun 29 11:46:36 ip-172-31-4-198 systemd[1]: web.service: Scheduled restart job, restart counter is at 6.
Jun 29 11:46:36 ip-172-31-4-198 audit[1]: SERVICE_START pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=web comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:46:36 ip-172-31-4-198 audit[1]: SERVICE_STOP pid=1 uid=0 auid=********** ses=********** subj=system_u:system_r:init_t:s0 msg='unit=web comm="systemd" exe="/usr/lib/systemd/systemd" hostname=? addr=? terminal=? res=success'
Jun 29 11:46:36 ip-172-31-4-198 systemd[1]: Stopped web.service - This is web daemon.
Jun 29 11:46:36 ip-172-31-4-198 systemd[1]: web.service: Start request repeated too quickly.
Jun 29 11:46:36 ip-172-31-4-198 systemd[1]: web.service: Failed with result 'exit-code'.
Jun 29 11:46:36 ip-172-31-4-198 systemd[1]: Failed to start web.service - This is web daemon.
Jun 29 11:46:42 ip-172-31-4-198 healthd[2094]: Version 2 of the Ruby SDK will enter maintenance mode as of November 20, 2020. To continue receiving service updates and new features, please upgrade to Version 3. More information can be found here: https://aws.amazon.com/blogs/developer/deprecation-schedule-for-aws-sdk-for-ruby-v2/
Jun 29 11:46:42 ip-172-31-4-198 audit[2260]: AVC avc:  denied  { name_connect } for  pid=2260 comm="nginx" dest=8000 scontext=system_u:system_r:httpd_t:s0 tcontext=system_u:object_r:soundd_port_t:s0 tclass=tcp_socket permissive=1
