Jun 29 11:46:33 ip-172-31-4-198 web[2213]: [2025-06-29 11:46:33 +0000] [2213] [INFO] Starting gunicorn 23.0.0
Jun 29 11:46:33 ip-172-31-4-198 web[2213]: [2025-06-29 11:46:33 +0000] [2213] [INFO] Listening at: http://127.0.0.1:8000 (2213)
Jun 29 11:46:33 ip-172-31-4-198 web[2213]: [2025-06-29 11:46:33 +0000] [2213] [INFO] Using worker: sync
Jun 29 11:46:33 ip-172-31-4-198 web[2245]: [2025-06-29 11:46:33 +0000] [2245] [INFO] Booting worker with pid: 2245
Jun 29 11:46:33 ip-172-31-4-198 web[2245]: [2025-06-29 11:46:33 +0000] [2245] [ERROR] Exception in worker process
Jun 29 11:46:33 ip-172-31-4-198 web[2245]: Traceback (most recent call last):
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:    worker.init_process()
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 135, in init_process
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:    self.load_wsgi()
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:    self.wsgi = self.app.wsgi()
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:                ^^^^^^^^^^^^^^^
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/base.py", line 66, in wsgi
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:    self.callable = self.load()
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:                    ^^^^^^^^^^^
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:    return self.load_wsgiapp()
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:           ^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:    return util.import_app(self.app_uri)
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/util.py", line 370, in import_app
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:    mod = importlib.import_module(module)
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:  File "/usr/lib64/python3.11/importlib/__init__.py", line 126, in import_module
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:    return _bootstrap._gcd_import(name[level:], package, level)
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:46:33 ip-172-31-4-198 web[2245]:  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
Jun 29 11:46:33 ip-172-31-4-198 web[2245]: ModuleNotFoundError: No module named 'aws_processing_api'
Jun 29 11:46:33 ip-172-31-4-198 web[2245]: [2025-06-29 11:46:33 +0000] [2245] [INFO] Worker exiting (pid: 2245)
Jun 29 11:46:33 ip-172-31-4-198 web[2213]: [2025-06-29 11:46:33 +0000] [2213] [ERROR] Worker (pid:2245) exited with code 3
Jun 29 11:46:33 ip-172-31-4-198 web[2213]: [2025-06-29 11:46:33 +0000] [2213] [ERROR] Shutting down: Master
Jun 29 11:46:33 ip-172-31-4-198 web[2213]: [2025-06-29 11:46:33 +0000] [2213] [ERROR] Reason: Worker failed to boot.
Jun 29 11:46:34 ip-172-31-4-198 web[2254]: [2025-06-29 11:46:34 +0000] [2254] [INFO] Starting gunicorn 23.0.0
Jun 29 11:46:34 ip-172-31-4-198 web[2254]: [2025-06-29 11:46:34 +0000] [2254] [INFO] Listening at: http://127.0.0.1:8000 (2254)
Jun 29 11:46:34 ip-172-31-4-198 web[2254]: [2025-06-29 11:46:34 +0000] [2254] [INFO] Using worker: sync
Jun 29 11:46:34 ip-172-31-4-198 web[2272]: [2025-06-29 11:46:34 +0000] [2272] [INFO] Booting worker with pid: 2272
Jun 29 11:46:34 ip-172-31-4-198 web[2272]: [2025-06-29 11:46:34 +0000] [2272] [ERROR] Exception in worker process
Jun 29 11:46:34 ip-172-31-4-198 web[2272]: Traceback (most recent call last):
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:    worker.init_process()
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 135, in init_process
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:    self.load_wsgi()
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:    self.wsgi = self.app.wsgi()
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:                ^^^^^^^^^^^^^^^
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/base.py", line 66, in wsgi
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:    self.callable = self.load()
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:                    ^^^^^^^^^^^
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:    return self.load_wsgiapp()
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:           ^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:    return util.import_app(self.app_uri)
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/util.py", line 370, in import_app
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:    mod = importlib.import_module(module)
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:  File "/usr/lib64/python3.11/importlib/__init__.py", line 126, in import_module
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:    return _bootstrap._gcd_import(name[level:], package, level)
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:46:34 ip-172-31-4-198 web[2272]:  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
Jun 29 11:46:34 ip-172-31-4-198 web[2272]: ModuleNotFoundError: No module named 'aws_processing_api'
Jun 29 11:46:34 ip-172-31-4-198 web[2272]: [2025-06-29 11:46:34 +0000] [2272] [INFO] Worker exiting (pid: 2272)
Jun 29 11:46:34 ip-172-31-4-198 web[2254]: [2025-06-29 11:46:34 +0000] [2254] [ERROR] Worker (pid:2272) exited with code 3
Jun 29 11:46:34 ip-172-31-4-198 web[2254]: [2025-06-29 11:46:34 +0000] [2254] [ERROR] Shutting down: Master
Jun 29 11:46:34 ip-172-31-4-198 web[2254]: [2025-06-29 11:46:34 +0000] [2254] [ERROR] Reason: Worker failed to boot.
Jun 29 11:46:34 ip-172-31-4-198 web[2276]: [2025-06-29 11:46:34 +0000] [2276] [INFO] Starting gunicorn 23.0.0
Jun 29 11:46:34 ip-172-31-4-198 web[2276]: [2025-06-29 11:46:34 +0000] [2276] [INFO] Listening at: http://127.0.0.1:8000 (2276)
Jun 29 11:46:34 ip-172-31-4-198 web[2276]: [2025-06-29 11:46:34 +0000] [2276] [INFO] Using worker: sync
Jun 29 11:46:34 ip-172-31-4-198 web[2280]: [2025-06-29 11:46:34 +0000] [2280] [INFO] Booting worker with pid: 2280
Jun 29 11:46:34 ip-172-31-4-198 web[2280]: [2025-06-29 11:46:34 +0000] [2280] [ERROR] Exception in worker process
Jun 29 11:46:34 ip-172-31-4-198 web[2280]: Traceback (most recent call last):
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:    worker.init_process()
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 135, in init_process
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:    self.load_wsgi()
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:    self.wsgi = self.app.wsgi()
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:                ^^^^^^^^^^^^^^^
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/base.py", line 66, in wsgi
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:    self.callable = self.load()
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:                    ^^^^^^^^^^^
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:    return self.load_wsgiapp()
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:           ^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:    return util.import_app(self.app_uri)
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/util.py", line 370, in import_app
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:    mod = importlib.import_module(module)
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:  File "/usr/lib64/python3.11/importlib/__init__.py", line 126, in import_module
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:    return _bootstrap._gcd_import(name[level:], package, level)
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:46:34 ip-172-31-4-198 web[2280]:  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
Jun 29 11:46:34 ip-172-31-4-198 web[2280]: ModuleNotFoundError: No module named 'aws_processing_api'
Jun 29 11:46:34 ip-172-31-4-198 web[2280]: [2025-06-29 11:46:34 +0000] [2280] [INFO] Worker exiting (pid: 2280)
Jun 29 11:46:34 ip-172-31-4-198 web[2276]: [2025-06-29 11:46:34 +0000] [2276] [ERROR] Worker (pid:2280) exited with code 3
Jun 29 11:46:34 ip-172-31-4-198 web[2276]: [2025-06-29 11:46:34 +0000] [2276] [ERROR] Shutting down: Master
Jun 29 11:46:34 ip-172-31-4-198 web[2276]: [2025-06-29 11:46:34 +0000] [2276] [ERROR] Reason: Worker failed to boot.
Jun 29 11:46:35 ip-172-31-4-198 web[2282]: [2025-06-29 11:46:35 +0000] [2282] [INFO] Starting gunicorn 23.0.0
Jun 29 11:46:35 ip-172-31-4-198 web[2282]: [2025-06-29 11:46:35 +0000] [2282] [INFO] Listening at: http://127.0.0.1:8000 (2282)
Jun 29 11:46:35 ip-172-31-4-198 web[2282]: [2025-06-29 11:46:35 +0000] [2282] [INFO] Using worker: sync
Jun 29 11:46:35 ip-172-31-4-198 web[2286]: [2025-06-29 11:46:35 +0000] [2286] [INFO] Booting worker with pid: 2286
Jun 29 11:46:35 ip-172-31-4-198 web[2286]: [2025-06-29 11:46:35 +0000] [2286] [ERROR] Exception in worker process
Jun 29 11:46:35 ip-172-31-4-198 web[2286]: Traceback (most recent call last):
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:    worker.init_process()
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 135, in init_process
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:    self.load_wsgi()
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:    self.wsgi = self.app.wsgi()
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:                ^^^^^^^^^^^^^^^
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/base.py", line 66, in wsgi
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:    self.callable = self.load()
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:                    ^^^^^^^^^^^
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:    return self.load_wsgiapp()
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:           ^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:    return util.import_app(self.app_uri)
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/util.py", line 370, in import_app
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:    mod = importlib.import_module(module)
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:  File "/usr/lib64/python3.11/importlib/__init__.py", line 126, in import_module
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:    return _bootstrap._gcd_import(name[level:], package, level)
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:46:35 ip-172-31-4-198 web[2286]:  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
Jun 29 11:46:35 ip-172-31-4-198 web[2286]: ModuleNotFoundError: No module named 'aws_processing_api'
Jun 29 11:46:35 ip-172-31-4-198 web[2286]: [2025-06-29 11:46:35 +0000] [2286] [INFO] Worker exiting (pid: 2286)
Jun 29 11:46:35 ip-172-31-4-198 web[2282]: [2025-06-29 11:46:35 +0000] [2282] [ERROR] Worker (pid:2286) exited with code 3
Jun 29 11:46:35 ip-172-31-4-198 web[2282]: [2025-06-29 11:46:35 +0000] [2282] [ERROR] Shutting down: Master
Jun 29 11:46:35 ip-172-31-4-198 web[2282]: [2025-06-29 11:46:35 +0000] [2282] [ERROR] Reason: Worker failed to boot.
Jun 29 11:46:35 ip-172-31-4-198 web[2288]: [2025-06-29 11:46:35 +0000] [2288] [INFO] Starting gunicorn 23.0.0
Jun 29 11:46:35 ip-172-31-4-198 web[2288]: [2025-06-29 11:46:35 +0000] [2288] [INFO] Listening at: http://127.0.0.1:8000 (2288)
Jun 29 11:46:35 ip-172-31-4-198 web[2288]: [2025-06-29 11:46:35 +0000] [2288] [INFO] Using worker: sync
Jun 29 11:46:35 ip-172-31-4-198 web[2292]: [2025-06-29 11:46:35 +0000] [2292] [INFO] Booting worker with pid: 2292
Jun 29 11:46:35 ip-172-31-4-198 web[2292]: [2025-06-29 11:46:35 +0000] [2292] [ERROR] Exception in worker process
Jun 29 11:46:35 ip-172-31-4-198 web[2292]: Traceback (most recent call last):
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:    worker.init_process()
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 135, in init_process
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:    self.load_wsgi()
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:    self.wsgi = self.app.wsgi()
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:                ^^^^^^^^^^^^^^^
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/base.py", line 66, in wsgi
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:    self.callable = self.load()
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:                    ^^^^^^^^^^^
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:    return self.load_wsgiapp()
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:           ^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:    return util.import_app(self.app_uri)
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/util.py", line 370, in import_app
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:    mod = importlib.import_module(module)
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:  File "/usr/lib64/python3.11/importlib/__init__.py", line 126, in import_module
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:    return _bootstrap._gcd_import(name[level:], package, level)
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:46:35 ip-172-31-4-198 web[2292]:  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
Jun 29 11:46:35 ip-172-31-4-198 web[2292]: ModuleNotFoundError: No module named 'aws_processing_api'
Jun 29 11:46:35 ip-172-31-4-198 web[2292]: [2025-06-29 11:46:35 +0000] [2292] [INFO] Worker exiting (pid: 2292)
Jun 29 11:46:35 ip-172-31-4-198 web[2288]: [2025-06-29 11:46:35 +0000] [2288] [ERROR] Worker (pid:2292) exited with code 3
Jun 29 11:46:35 ip-172-31-4-198 web[2288]: [2025-06-29 11:46:35 +0000] [2288] [ERROR] Shutting down: Master
Jun 29 11:46:35 ip-172-31-4-198 web[2288]: [2025-06-29 11:46:35 +0000] [2288] [ERROR] Reason: Worker failed to boot.
Jun 29 11:46:36 ip-172-31-4-198 web[2294]: [2025-06-29 11:46:36 +0000] [2294] [INFO] Starting gunicorn 23.0.0
Jun 29 11:46:36 ip-172-31-4-198 web[2294]: [2025-06-29 11:46:36 +0000] [2294] [INFO] Listening at: http://127.0.0.1:8000 (2294)
Jun 29 11:46:36 ip-172-31-4-198 web[2294]: [2025-06-29 11:46:36 +0000] [2294] [INFO] Using worker: sync
Jun 29 11:46:36 ip-172-31-4-198 web[2298]: [2025-06-29 11:46:36 +0000] [2298] [INFO] Booting worker with pid: 2298
Jun 29 11:46:36 ip-172-31-4-198 web[2298]: [2025-06-29 11:46:36 +0000] [2298] [ERROR] Exception in worker process
Jun 29 11:46:36 ip-172-31-4-198 web[2298]: Traceback (most recent call last):
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:    worker.init_process()
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 135, in init_process
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:    self.load_wsgi()
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:    self.wsgi = self.app.wsgi()
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:                ^^^^^^^^^^^^^^^
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/base.py", line 66, in wsgi
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:    self.callable = self.load()
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:                    ^^^^^^^^^^^
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:    return self.load_wsgiapp()
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:           ^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:    return util.import_app(self.app_uri)
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/util.py", line 370, in import_app
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:    mod = importlib.import_module(module)
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:  File "/usr/lib64/python3.11/importlib/__init__.py", line 126, in import_module
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:    return _bootstrap._gcd_import(name[level:], package, level)
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:46:36 ip-172-31-4-198 web[2298]:  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
Jun 29 11:46:36 ip-172-31-4-198 web[2298]: ModuleNotFoundError: No module named 'aws_processing_api'
Jun 29 11:46:36 ip-172-31-4-198 web[2298]: [2025-06-29 11:46:36 +0000] [2298] [INFO] Worker exiting (pid: 2298)
Jun 29 11:46:36 ip-172-31-4-198 web[2294]: [2025-06-29 11:46:36 +0000] [2294] [ERROR] Worker (pid:2298) exited with code 3
Jun 29 11:46:36 ip-172-31-4-198 web[2294]: [2025-06-29 11:46:36 +0000] [2294] [ERROR] Shutting down: Master
Jun 29 11:46:36 ip-172-31-4-198 web[2294]: [2025-06-29 11:46:36 +0000] [2294] [ERROR] Reason: Worker failed to boot.
Jun 29 11:49:13 ip-172-31-4-198 web[2857]: [2025-06-29 11:49:13 +0000] [2857] [INFO] Starting gunicorn 23.0.0
Jun 29 11:49:13 ip-172-31-4-198 web[2857]: [2025-06-29 11:49:13 +0000] [2857] [INFO] Listening at: http://127.0.0.1:8000 (2857)
Jun 29 11:49:13 ip-172-31-4-198 web[2857]: [2025-06-29 11:49:13 +0000] [2857] [INFO] Using worker: sync
Jun 29 11:49:13 ip-172-31-4-198 web[2865]: [2025-06-29 11:49:13 +0000] [2865] [INFO] Booting worker with pid: 2865
Jun 29 12:29:58 ip-172-31-4-198 web[2857]: [2025-06-29 12:29:58 +0000] [2857] [INFO] Handling signal: term
Jun 29 12:29:58 ip-172-31-4-198 web[2865]: [2025-06-29 12:29:58 +0000] [2865] [INFO] Worker exiting (pid: 2865)
Jun 29 12:29:58 ip-172-31-4-198 web[2857]: [2025-06-29 12:29:58 +0000] [2857] [INFO] Shutting down: Master
Jun 29 12:30:00 ip-172-31-4-198 web[4966]: [2025-06-29 12:30:00 +0000] [4966] [INFO] Starting gunicorn 23.0.0
Jun 29 12:30:00 ip-172-31-4-198 web[4966]: [2025-06-29 12:30:00 +0000] [4966] [INFO] Listening at: http://127.0.0.1:8000 (4966)
Jun 29 12:30:00 ip-172-31-4-198 web[4966]: [2025-06-29 12:30:00 +0000] [4966] [INFO] Using worker: sync
Jun 29 12:30:00 ip-172-31-4-198 web[4996]: [2025-06-29 12:30:00 +0000] [4996] [INFO] Booting worker with pid: 4996
