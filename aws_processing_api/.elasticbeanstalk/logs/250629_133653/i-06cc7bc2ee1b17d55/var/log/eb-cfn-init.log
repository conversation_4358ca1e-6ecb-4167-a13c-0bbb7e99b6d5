[2025-06-29T11:43:57Z] Started EB User Data
+ SLEEP_TIME=2
+ SLEEP_TIME_MAX=3600
+ true
+ curl https://elasticbeanstalk-platform-assets-us-west-2.s3.us-west-2.amazonaws.com/stalks/eb_python311_amazon_linux_2023_1.0.797.0_20250625232415/lib/UserDataScript.sh
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100  4838  100  4838    0     0   111k      0 --:--:-- --:--:-- --:--:--  112k
+ RESULT=0
+ [[ 0 -ne 0 ]]
+ /bin/bash /tmp/ebbootstrap.sh 'https://cloudformation-waitcondition-us-west-2.s3-us-west-2.amazonaws.com/arn%3Aaws%3Acloudformation%3Aus-west-2%3A029530100737%3Astack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773/3b3f6a50-54de-11f0-a15a-0292084a4773/AWSEBInstanceLaunchWaitHandle?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250629T114314Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86399&X-Amz-Credential=AKIAJBJSWSW6NLR67N6A%2F20250629%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Signature=6ec93324db47b3ed71be0d8d1a3f676bd02fbc2a1d0b55d20a29f59c2411b18b' arn:aws:cloudformation:us-west-2:029530100737:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 7abb8661-da8a-452c-96c4-5a03457a2874 https://elasticbeanstalk-health.us-west-2.amazonaws.com '' https://elasticbeanstalk-platform-assets-us-west-2.s3.us-west-2.amazonaws.com/stalks/eb_python311_amazon_linux_2023_1.0.797.0_20250625232415 us-west-2
[2025-06-29T11:43:58.047Z] Started EB Bootstrapping Script.
[2025-06-29T11:43:58.050Z] Received parameters:
 TARBALLS = 
 EB_GEMS = 
 SIGNAL_URL = https://cloudformation-waitcondition-us-west-2.s3-us-west-2.amazonaws.com/arn%3Aaws%3Acloudformation%3Aus-west-2%3A029530100737%3Astack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773/3b3f6a50-54de-11f0-a15a-0292084a4773/AWSEBInstanceLaunchWaitHandle?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250629T114314Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86399&X-Amz-Credential=AKIAJBJSWSW6NLR67N6A%2F20250629%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Signature=6ec93324db47b3ed71be0d8d1a3f676bd02fbc2a1d0b55d20a29f59c2411b18b
 STACK_ID = arn:aws:cloudformation:us-west-2:029530100737:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773
 REGION = us-west-2
 GUID = 
 HEALTHD_GROUP_ID = 7abb8661-da8a-452c-96c4-5a03457a2874
 HEALTHD_ENDPOINT = https://elasticbeanstalk-health.us-west-2.amazonaws.com
 PROXY_SERVER = 
 HEALTHD_PROXY_LOG_LOCATION = 
 PLATFORM_ASSETS_URL = https://elasticbeanstalk-platform-assets-us-west-2.s3.us-west-2.amazonaws.com/stalks/eb_python311_amazon_linux_2023_1.0.797.0_20250625232415
[2025-06-29T11:43:58.054Z] engine url is set to https://elasticbeanstalk-platform-assets-us-west-2.s3.us-west-2.amazonaws.com/stalks/eb_python311_amazon_linux_2023_1.0.797.0_20250625232415/lib/platform-engine.zip
[2025-06-29T11:43:58.056Z] first init of instance.
[2025-06-29T11:43:58.059Z] Started executing cfn_init _OnInstanceBoot.
[2025-06-29T11:43:58.061Z] Running cfn-init ConfigSet: _OnInstanceBoot.
[2025-06-29T11:43:58.063Z] Using stack-id from userdata
[2025-06-29T11:43:58.816Z] Command Returned: 
[2025-06-29T11:43:58.819Z] Completed executing cfn_init.
[2025-06-29T11:43:58.823Z] finished _OnInstanceBoot
[2025-06-29T11:43:58.827Z] installing platform engine
2025-06-29 11:43:59 URL:https://elasticbeanstalk-platform-assets-us-west-2.s3.us-west-2.amazonaws.com/stalks/eb_python311_amazon_linux_2023_1.0.797.0_20250625232415/lib/platform-engine.zip [33307599/33307599] -> "/tmp/platform-engine.zip" [1]
[2025-06-29T11:44:00.176Z] executing platform engine. see /var/log/eb-engine.log.
[2025-06-29T11:44:02.064Z] Successfully bootstrapped instance.
[2025-06-29T11:44:02.072Z] Sending signal 0 to CFN wait condition https://cloudformation-waitcondition-us-west-2.s3-us-west-2.amazonaws.com/arn%3Aaws%3Acloudformation%3Aus-west-2%3A029530100737%3Astack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773/3b3f6a50-54de-11f0-a15a-0292084a4773/AWSEBInstanceLaunchWaitHandle?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250629T114314Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86399&X-Amz-Credential=AKIAJBJSWSW6NLR67N6A%2F20250629%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Signature=6ec93324db47b3ed71be0d8d1a3f676bd02fbc2a1d0b55d20a29f59c2411b18b
CloudFormation signaled successfully with status SUCCESS
[2025-06-29T11:44:02.512Z] Tailing /var/log/eb-engine.log

******************* eb-engine taillog *******************
2025/06/29 11:44:01.062665 [INFO] 1001

2025/06/29 11:44:01.063007 [INFO] configure bundle log for healthd...
2025/06/29 11:44:01.063077 [INFO] Executing instruction: GetSetupProxyLog
2025/06/29 11:44:01.063151 [INFO] Skipping Install yum packages
2025/06/29 11:44:01.063157 [INFO] Skipping Install Python Bundle
2025/06/29 11:44:01.063163 [INFO] Skipping Configure Python site-packages
2025/06/29 11:44:01.063167 [INFO] Skipping Install Python Modules
2025/06/29 11:44:01.063171 [INFO] Skipping MarkBaked
2025/06/29 11:44:01.063175 [INFO] Instance has NOT been bootstrapped
2025/06/29 11:44:01.063177 [INFO] Executing instruction: TuneSystemSettings
2025/06/29 11:44:01.063179 [INFO] Starting TuneSystemSettings
2025/06/29 11:44:01.063182 [INFO] Instance has NOT been bootstrapped
2025/06/29 11:44:01.064497 [INFO] Executing instruction: GetSetupLogRotate
2025/06/29 11:44:01.064503 [INFO] Initialize LogRotate files and directories
2025/06/29 11:44:01.084253 [INFO] Instance has NOT been bootstrapped
2025/06/29 11:44:01.084273 [INFO] Executing instruction: BootstrapCFNHup
2025/06/29 11:44:01.084277 [INFO] Bootstrap cfn-hup
2025/06/29 11:44:01.088721 [INFO] Copying file /opt/elasticbeanstalk/config/private/aws-eb-command-handler.conf to /etc/cfn/hooks.d/aws-eb-command-handler.conf
2025/06/29 11:44:01.091187 [INFO] Executing instruction: StartCFNHup
2025/06/29 11:44:01.091201 [INFO] Start cfn-hup
2025/06/29 11:44:01.091468 [INFO] Running command: systemctl show -p PartOf cfn-hup.service
2025/06/29 11:44:01.121662 [INFO] cfn-hup is not registered with EB yet, registering it now
2025/06/29 11:44:01.121700 [INFO] Running command: systemctl show -p PartOf cfn-hup.service
2025/06/29 11:44:01.140132 [INFO] Running command: systemctl daemon-reload
2025/06/29 11:44:01.389020 [INFO] Running command: systemctl reset-failed
2025/06/29 11:44:01.402026 [INFO] Running command: systemctl is-enabled aws-eb.target
2025/06/29 11:44:01.412223 [INFO] Running command: systemctl enable aws-eb.target
2025/06/29 11:44:01.706147 [INFO] Running command: systemctl start aws-eb.target
2025/06/29 11:44:01.715709 [INFO] Running command: systemctl enable cfn-hup.service
2025/06/29 11:44:01.964592 [INFO] Created symlink /etc/systemd/system/multi-user.target.wants/cfn-hup.service → /etc/systemd/system/cfn-hup.service.

2025/06/29 11:44:01.964627 [INFO] Running command: systemctl is-active cfn-hup.service
2025/06/29 11:44:01.973690 [INFO] cfn-hup process is not running, starting it now
2025/06/29 11:44:01.973721 [INFO] Running command: systemctl show -p PartOf cfn-hup.service
2025/06/29 11:44:01.984228 [INFO] Running command: systemctl is-active cfn-hup.service
2025/06/29 11:44:01.992600 [INFO] Running command: systemctl start cfn-hup.service
2025/06/29 11:44:02.058000 [INFO] Instance has NOT been bootstrapped
2025/06/29 11:44:02.058024 [INFO] Executing instruction: SetupPublishLogCronjob
2025/06/29 11:44:02.058028 [INFO] Setup publish logs cron job...
2025/06/29 11:44:02.058032 [INFO] Copying file /opt/elasticbeanstalk/config/private/logtasks/cron/publishlogs to /etc/cron.d/publishlogs
2025/06/29 11:44:02.060479 [INFO] Instance has NOT been bootstrapped
2025/06/29 11:44:02.060503 [INFO] Executing instruction: MarkBootstrapped
2025/06/29 11:44:02.060507 [INFO] Starting MarkBootstrapped
2025/06/29 11:44:02.060513 [INFO] Instance has NOT been bootstrapped
2025/06/29 11:44:02.060605 [INFO] Marked instance as Bootstrapped
2025/06/29 11:44:02.060610 [INFO] Executing instruction: Save CFN Stack Info
2025/06/29 11:44:02.060676 [INFO] Executing cleanup logic
2025/06/29 11:44:02.060687 [INFO] Platform Engine finished execution on command: env-launch
******************* End of taillog *******************


[2025-06-29T11:44:02.521Z] Tailing /var/log/eb-tools.log

******************* eb-tools taillog *******************
***eb-tools is not available yet.***
******************* End of taillog *******************


[2025-06-29T11:44:02.524Z] Tailing /var/log/eb-hooks.log

******************* eb-hooks taillog *******************
***eb-hooks is not available yet.***
******************* End of taillog *******************


[2025-06-29T11:44:02.526Z] Tailing /var/log/cfn-init.log

******************* cfn-init taillog *******************
2025-06-29 11:43:58,529 [INFO] -----------------------Starting build-----------------------
2025-06-29 11:43:58,534 [INFO] Running configSets: _OnInstanceBoot
2025-06-29 11:43:58,537 [INFO] Running configSet _OnInstanceBoot
2025-06-29 11:43:58,539 [INFO] Running config AWSEBBaseConfig
2025-06-29 11:43:58,773 [INFO] Command clearbackupfiles succeeded
2025-06-29 11:43:58,776 [INFO] Running config AWSEBCfnHupEndpointOverride
2025-06-29 11:43:58,781 [INFO] Command clearbackupfiles succeeded
2025-06-29 11:43:58,782 [INFO] ConfigSets completed
2025-06-29 11:43:58,782 [INFO] -----------------------Build complete-----------------------
******************* End of taillog *******************


[2025-06-29T11:44:02.529Z] Tailing /var/log/cfn-hup.log

******************* cfn-hup taillog *******************
2025-06-29 11:44:02,364 [DEBUG] CloudFormation client initialized with endpoint https://cloudformation.us-west-2.amazonaws.com
2025-06-29 11:44:02,365 [DEBUG] SQS client initialized with endpoint https://sqs.us-west-2.amazonaws.com
2025-06-29 11:44:02,365 [DEBUG] CloudFormation client initialized with endpoint https://cloudformation.us-west-2.amazonaws.com
2025-06-29 11:44:02,365 [DEBUG] Enabled single threading mode.
2025-06-29 11:44:02,365 [DEBUG] Creating /var/lib/cfn-hup/data
2025-06-29 11:44:02,383 [INFO] No umask value specified in config file. Using the default one: 0o22
2025-06-29 11:44:02,452 [INFO] Pid: 1762
******************* End of taillog *******************


[2025-06-29T11:44:02.532Z] Completed EB Bootstrapping Script.
+ RESULT=0
+ [[ 0 -ne 0 ]]
+ exit 0
