Jun 29 11:43:57 ip-172-31-27-8 crond[1576]: (CRON) STARTUP (1.5.7)
Jun 29 11:43:57 ip-172-31-27-8 crond[1576]: (CRON) INFO (Syslog will be used instead of sendmail.)
Jun 29 11:43:57 ip-172-31-27-8 crond[1576]: (CRON) INFO (RANDOM_DELAY will be scaled with factor 8% if used.)
Jun 29 11:43:57 ip-172-31-27-8 crond[1576]: (CRON) INFO (running with inotify support)
Jun 29 11:50:01 ip-172-31-4-198 CROND[2940]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 11:50:01 ip-172-31-4-198 CROND[2939]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 12:01:01 ip-172-31-4-198 CROND[3375]: (root) CMD (run-parts /etc/cron.hourly)
Jun 29 12:01:01 ip-172-31-4-198 run-parts[3375]: (/etc/cron.hourly) starting 0anacron
Jun 29 12:01:01 ip-172-31-4-198 anacron[3386]: <PERSON>cron started on 2025-06-29
Jun 29 12:01:01 ip-172-31-4-198 anacron[3386]: Will run job `cron.daily' in 16 min.
Jun 29 12:01:01 ip-172-31-4-198 run-parts[3375]: (/etc/cron.hourly) finished 0anacron
Jun 29 12:01:01 ip-172-31-4-198 anacron[3386]: Will run job `cron.weekly' in 36 min.
Jun 29 12:01:01 ip-172-31-4-198 anacron[3386]: Will run job `cron.monthly' in 56 min.
Jun 29 12:01:01 ip-172-31-4-198 anacron[3386]: Jobs will be executed sequentially
Jun 29 12:01:01 ip-172-31-4-198 run-parts[3375]: (/etc/cron.hourly) starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 29 12:01:01 ip-172-31-4-198 run-parts[3375]: (/etc/cron.hourly) finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 29 12:01:01 ip-172-31-4-198 run-parts[3375]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 29 12:01:01 ip-172-31-4-198 run-parts[3375]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 29 12:01:01 ip-172-31-4-198 run-parts[3375]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 29 12:01:01 ip-172-31-4-198 run-parts[3375]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 29 12:01:01 ip-172-31-4-198 run-parts[3375]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 29 12:01:01 ip-172-31-4-198 run-parts[3375]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 29 12:01:01 ip-172-31-4-198 run-parts[3375]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 29 12:01:01 ip-172-31-4-198 run-parts[3375]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 29 12:01:01 ip-172-31-4-198 run-parts[3375]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 29 12:01:01 ip-172-31-4-198 run-parts[3375]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 29 12:01:01 ip-172-31-4-198 run-parts[3375]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 29 12:01:01 ip-172-31-4-198 run-parts[3375]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 29 12:01:01 ip-172-31-4-198 CROND[3374]: (root) CMDEND (run-parts /etc/cron.hourly)
Jun 29 12:10:01 ip-172-31-4-198 CROND[3727]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 12:10:01 ip-172-31-4-198 CROND[3726]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 12:17:01 ip-172-31-4-198 anacron[3386]: Job `cron.daily' started
Jun 29 12:17:01 ip-172-31-4-198 anacron[3386]: Job `cron.daily' terminated
Jun 29 12:30:01 ip-172-31-4-198 CROND[5025]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 12:30:01 ip-172-31-4-198 CROND[5024]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
