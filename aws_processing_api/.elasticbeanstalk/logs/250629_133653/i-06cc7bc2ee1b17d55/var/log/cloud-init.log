2025-06-29 11:43:55,902 - util.py[DEBUG]: Cloud-init v. 22.2.2 running 'init' at Sun, 29 Jun 2025 11:43:55 +0000. Up 6.49 seconds.
2025-06-29 11:43:55,902 - main.py[DEBUG]: No kernel command line url found.
2025-06-29 11:43:55,902 - main.py[DEBUG]: Closing stdin.
2025-06-29 11:43:55,905 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud (recursive=False)
2025-06-29 11:43:55,906 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud (recursive=True)
2025-06-29 11:43:55,910 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/scripts (recursive=False)
2025-06-29 11:43:55,911 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/scripts (recursive=True)
2025-06-29 11:43:55,914 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/scripts/per-instance (recursive=False)
2025-06-29 11:43:55,914 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/scripts (recursive=True)
2025-06-29 11:43:55,917 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/scripts/per-once (recursive=False)
2025-06-29 11:43:55,918 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/scripts (recursive=True)
2025-06-29 11:43:55,922 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/scripts/per-boot (recursive=False)
2025-06-29 11:43:55,923 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/scripts (recursive=True)
2025-06-29 11:43:55,927 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/scripts/vendor (recursive=False)
2025-06-29 11:43:55,928 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud (recursive=True)
2025-06-29 11:43:55,935 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/seed (recursive=False)
2025-06-29 11:43:55,936 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud (recursive=True)
2025-06-29 11:43:55,944 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances (recursive=False)
2025-06-29 11:43:55,945 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud (recursive=True)
2025-06-29 11:43:55,954 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/handlers (recursive=False)
2025-06-29 11:43:55,955 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud (recursive=True)
2025-06-29 11:43:55,964 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/sem (recursive=False)
2025-06-29 11:43:55,965 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/data (recursive=False)
2025-06-29 11:43:55,965 - util.py[DEBUG]: Restoring selinux mode for /run/cloud-init (recursive=True)
2025-06-29 11:43:55,966 - util.py[DEBUG]: Restoring selinux mode for /run/cloud-init/sem (recursive=False)
2025-06-29 11:43:55,966 - util.py[DEBUG]: Writing to /var/log/cloud-init.log - ab: [640] 0 bytes
2025-06-29 11:43:55,968 - util.py[DEBUG]: Restoring selinux mode for /var/log/cloud-init.log (recursive=False)
2025-06-29 11:43:55,968 - util.py[DEBUG]: Restoring selinux mode for /var/log/cloud-init.log (recursive=False)
2025-06-29 11:43:55,968 - util.py[DEBUG]: Changing the ownership of /var/log/cloud-init.log to 0:4
2025-06-29 11:43:55,969 - util.py[DEBUG]: Writing to /var/lib/cloud/data/python-version - wb: [644] 3 bytes
2025-06-29 11:43:55,969 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/data/python-version (recursive=False)
2025-06-29 11:43:55,970 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/data/python-version (recursive=False)
2025-06-29 11:43:55,970 - subp.py[DEBUG]: Running command ['ip', '--json', 'addr'] with allowed return codes [0] (shell=False, capture=True)
2025-06-29 11:43:55,975 - subp.py[DEBUG]: Running command ['ip', '-o', 'route', 'list'] with allowed return codes [0] (shell=False, capture=True)
2025-06-29 11:43:55,978 - subp.py[DEBUG]: Running command ['ip', '--oneline', '-6', 'route', 'list', 'table', 'all'] with allowed return codes [0, 1] (shell=False, capture=True)
2025-06-29 11:43:55,989 - handlers.py[DEBUG]: start: init-network/check-cache: attempting to read from cache [trust]
2025-06-29 11:43:55,993 - util.py[DEBUG]: Reading from /var/lib/cloud/instance/obj.pkl (quiet=False)
2025-06-29 11:43:55,995 - stages.py[DEBUG]: no cache found
2025-06-29 11:43:55,999 - handlers.py[DEBUG]: finish: init-network/check-cache: SUCCESS: no cache found
2025-06-29 11:43:55,999 - util.py[DEBUG]: Attempting to remove /var/lib/cloud/instance
2025-06-29 11:43:56,032 - stages.py[DEBUG]: Using distro class <class 'cloudinit.distros.amazon.Distro'>
2025-06-29 11:43:56,033 - __init__.py[DEBUG]: Looking for data source in: ['Ec2', 'None'], via packages ['', 'cloudinit.sources'] that matches dependencies ['FILESYSTEM', 'NETWORK']
2025-06-29 11:43:56,047 - __init__.py[DEBUG]: Searching for network data source in: ['DataSourceEc2', 'DataSourceNone']
2025-06-29 11:43:56,047 - handlers.py[DEBUG]: start: init-network/search-Ec2: searching for network data from DataSourceEc2
2025-06-29 11:43:56,047 - __init__.py[DEBUG]: Seeing if we can get any data from <class 'cloudinit.sources.DataSourceEc2.DataSourceEc2'>
2025-06-29 11:43:56,047 - __init__.py[DEBUG]: Update datasource metadata and network config due to events: boot-new-instance
2025-06-29 11:43:56,047 - util.py[DEBUG]: Reading from /sys/hypervisor/uuid (quiet=False)
2025-06-29 11:43:56,047 - dmi.py[DEBUG]: querying dmi data /sys/class/dmi/id/product_uuid
2025-06-29 11:43:56,048 - dmi.py[DEBUG]: querying dmi data /sys/class/dmi/id/product_serial
2025-06-29 11:43:56,048 - dmi.py[DEBUG]: querying dmi data /sys/class/dmi/id/chassis_asset_tag
2025-06-29 11:43:56,048 - dmi.py[DEBUG]: querying dmi data /sys/class/dmi/id/sys_vendor
2025-06-29 11:43:56,048 - DataSourceEc2.py[DEBUG]: strict_mode: warn, cloud_name=aws cloud_platform=ec2
2025-06-29 11:43:56,048 - util.py[DEBUG]: Skipping DNS checks of IP address ***************
2025-06-29 11:43:56,048 - util.py[DEBUG]: Resolving URL: http://***************:80 took 0.000 seconds
2025-06-29 11:43:56,048 - util.py[DEBUG]: Skipping DNS checks of IP address fd00:ec2::254
2025-06-29 11:43:56,048 - util.py[DEBUG]: Resolving URL: http://[fd00:ec2::254]:80 took 0.000 seconds
2025-06-29 11:43:56,048 - DataSourceEc2.py[DEBUG]: Fetching Ec2 IMDSv2 API Token
2025-06-29 11:43:56,049 - url_helper.py[DEBUG]: [0/1] open 'http://***************:80/latest/api/token' with {'url': 'http://***************:80/latest/api/token', 'allow_redirects': True, 'method': 'PUT', 'timeout': 50.0, 'headers': {'User-Agent': 'Cloud-Init/22.2.2', 'X-aws-ec2-metadata-token-ttl-seconds': 'REDACTED'}} configuration
2025-06-29 11:43:56,053 - url_helper.py[DEBUG]: Read from http://***************:80/latest/api/token (200, 56b) after 1 attempts
2025-06-29 11:43:56,054 - DataSourceEc2.py[DEBUG]: Using metadata source: 'http://***************:80'
2025-06-29 11:43:56,054 - url_helper.py[DEBUG]: [0/1] open 'http://***************:80/2021-03-23/meta-data/instance-id' with {'url': 'http://***************:80/2021-03-23/meta-data/instance-id', 'allow_redirects': True, 'method': 'GET', 'headers': {'User-Agent': 'Cloud-Init/22.2.2', 'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,056 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/instance-id (200, 19b) after 1 attempts
2025-06-29 11:43:56,056 - DataSourceEc2.py[DEBUG]: Found preferred metadata version 2021-03-23
2025-06-29 11:43:56,056 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/user-data' with {'url': 'http://***************:80/2021-03-23/user-data', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,058 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/user-data (200, 2478b) after 1 attempts
2025-06-29 11:43:56,058 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/' with {'url': 'http://***************:80/2021-03-23/meta-data/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,060 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/ (200, 318b) after 1 attempts
2025-06-29 11:43:56,061 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/block-device-mapping/' with {'url': 'http://***************:80/2021-03-23/meta-data/block-device-mapping/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,062 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/block-device-mapping/ (200, 8b) after 1 attempts
2025-06-29 11:43:56,063 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/block-device-mapping/ami' with {'url': 'http://***************:80/2021-03-23/meta-data/block-device-mapping/ami', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,065 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/block-device-mapping/ami (200, 4b) after 1 attempts
2025-06-29 11:43:56,065 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/block-device-mapping/root' with {'url': 'http://***************:80/2021-03-23/meta-data/block-device-mapping/root', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,066 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/block-device-mapping/root (200, 9b) after 1 attempts
2025-06-29 11:43:56,067 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/events/' with {'url': 'http://***************:80/2021-03-23/meta-data/events/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,068 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/events/ (200, 12b) after 1 attempts
2025-06-29 11:43:56,069 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/events/maintenance/' with {'url': 'http://***************:80/2021-03-23/meta-data/events/maintenance/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,070 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/events/maintenance/ (200, 17b) after 1 attempts
2025-06-29 11:43:56,070 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/events/maintenance/history' with {'url': 'http://***************:80/2021-03-23/meta-data/events/maintenance/history', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,072 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/events/maintenance/history (200, 2b) after 1 attempts
2025-06-29 11:43:56,072 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/events/maintenance/scheduled' with {'url': 'http://***************:80/2021-03-23/meta-data/events/maintenance/scheduled', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,074 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/events/maintenance/scheduled (200, 2b) after 1 attempts
2025-06-29 11:43:56,074 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/iam/' with {'url': 'http://***************:80/2021-03-23/meta-data/iam/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,076 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/iam/ (200, 26b) after 1 attempts
2025-06-29 11:43:56,076 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/iam/info' with {'url': 'http://***************:80/2021-03-23/meta-data/iam/info', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,079 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/iam/info (200, 216b) after 1 attempts
2025-06-29 11:43:56,079 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/identity-credentials/' with {'url': 'http://***************:80/2021-03-23/meta-data/identity-credentials/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,081 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/identity-credentials/ (200, 4b) after 1 attempts
2025-06-29 11:43:56,081 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/identity-credentials/ec2/' with {'url': 'http://***************:80/2021-03-23/meta-data/identity-credentials/ec2/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,083 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/identity-credentials/ec2/ (200, 26b) after 1 attempts
2025-06-29 11:43:56,083 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/identity-credentials/ec2/info' with {'url': 'http://***************:80/2021-03-23/meta-data/identity-credentials/ec2/info', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,085 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/identity-credentials/ec2/info (200, 98b) after 1 attempts
2025-06-29 11:43:56,085 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/metrics/' with {'url': 'http://***************:80/2021-03-23/meta-data/metrics/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,087 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/metrics/ (200, 7b) after 1 attempts
2025-06-29 11:43:56,087 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/metrics/vhostmd' with {'url': 'http://***************:80/2021-03-23/meta-data/metrics/vhostmd', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,089 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/metrics/vhostmd (200, 38b) after 1 attempts
2025-06-29 11:43:56,089 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/' with {'url': 'http://***************:80/2021-03-23/meta-data/network/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,091 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/ (200, 11b) after 1 attempts
2025-06-29 11:43:56,091 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,093 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/ (200, 5b) after 1 attempts
2025-06-29 11:43:56,093 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,095 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/ (200, 18b) after 1 attempts
2025-06-29 11:43:56,095 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,097 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/ (200, 230b) after 1 attempts
2025-06-29 11:43:56,097 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/ipv4-associations/' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/ipv4-associations/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,099 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/ipv4-associations/ (200, 13b) after 1 attempts
2025-06-29 11:43:56,099 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/ipv4-associations/*************' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/ipv4-associations/*************', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,101 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/ipv4-associations/************* (200, 12b) after 1 attempts
2025-06-29 11:43:56,101 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/device-number' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/device-number', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,103 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/device-number (200, 1b) after 1 attempts
2025-06-29 11:43:56,103 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/interface-id' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/interface-id', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,104 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/interface-id (200, 21b) after 1 attempts
2025-06-29 11:43:56,105 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/local-hostname' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/local-hostname', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,106 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/local-hostname (200, 42b) after 1 attempts
2025-06-29 11:43:56,106 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/local-ipv4s' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/local-ipv4s', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,108 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/local-ipv4s (200, 12b) after 1 attempts
2025-06-29 11:43:56,109 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/mac' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/mac', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,110 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/mac (200, 17b) after 1 attempts
2025-06-29 11:43:56,111 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/owner-id' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/owner-id', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,112 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/owner-id (200, 12b) after 1 attempts
2025-06-29 11:43:56,113 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/public-hostname' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/public-hostname', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,114 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/public-hostname (200, 49b) after 1 attempts
2025-06-29 11:43:56,115 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/public-ipv4s' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/public-ipv4s', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,117 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/public-ipv4s (200, 13b) after 1 attempts
2025-06-29 11:43:56,117 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/security-group-ids' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/security-group-ids', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,119 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/security-group-ids (200, 20b) after 1 attempts
2025-06-29 11:43:56,119 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/security-groups' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/security-groups', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,120 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/security-groups (200, 56b) after 1 attempts
2025-06-29 11:43:56,121 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/subnet-id' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/subnet-id', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,122 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/subnet-id (200, 24b) after 1 attempts
2025-06-29 11:43:56,122 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/subnet-ipv4-cidr-block' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/subnet-ipv4-cidr-block', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,124 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/subnet-ipv4-cidr-block (200, 13b) after 1 attempts
2025-06-29 11:43:56,124 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/vpc-id' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/vpc-id', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,126 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/vpc-id (200, 21b) after 1 attempts
2025-06-29 11:43:56,126 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/vpc-ipv4-cidr-block' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/vpc-ipv4-cidr-block', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,128 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/vpc-ipv4-cidr-block (200, 13b) after 1 attempts
2025-06-29 11:43:56,128 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/vpc-ipv4-cidr-blocks' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/vpc-ipv4-cidr-blocks', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,130 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/0a:9e:23:4b:c9:1d/vpc-ipv4-cidr-blocks (200, 13b) after 1 attempts
2025-06-29 11:43:56,130 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/placement/' with {'url': 'http://***************:80/2021-03-23/meta-data/placement/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,132 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/placement/ (200, 45b) after 1 attempts
2025-06-29 11:43:56,132 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/placement/availability-zone' with {'url': 'http://***************:80/2021-03-23/meta-data/placement/availability-zone', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,134 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/placement/availability-zone (200, 10b) after 1 attempts
2025-06-29 11:43:56,134 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/placement/availability-zone-id' with {'url': 'http://***************:80/2021-03-23/meta-data/placement/availability-zone-id', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,135 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/placement/availability-zone-id (200, 8b) after 1 attempts
2025-06-29 11:43:56,136 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/placement/region' with {'url': 'http://***************:80/2021-03-23/meta-data/placement/region', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,137 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/placement/region (200, 9b) after 1 attempts
2025-06-29 11:43:56,138 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/public-keys/' with {'url': 'http://***************:80/2021-03-23/meta-data/public-keys/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,139 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/public-keys/ (200, 8b) after 1 attempts
2025-06-29 11:43:56,139 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/public-keys/0/openssh-key' with {'url': 'http://***************:80/2021-03-23/meta-data/public-keys/0/openssh-key', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,141 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/public-keys/0/openssh-key (200, 88b) after 1 attempts
2025-06-29 11:43:56,142 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/services/' with {'url': 'http://***************:80/2021-03-23/meta-data/services/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,146 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/services/ (200, 16b) after 1 attempts
2025-06-29 11:43:56,146 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/services/domain' with {'url': 'http://***************:80/2021-03-23/meta-data/services/domain', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,148 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/services/domain (200, 13b) after 1 attempts
2025-06-29 11:43:56,148 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/services/partition' with {'url': 'http://***************:80/2021-03-23/meta-data/services/partition', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,150 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/services/partition (200, 3b) after 1 attempts
2025-06-29 11:43:56,151 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/ami-id' with {'url': 'http://***************:80/2021-03-23/meta-data/ami-id', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,153 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/ami-id (200, 21b) after 1 attempts
2025-06-29 11:43:56,153 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/ami-launch-index' with {'url': 'http://***************:80/2021-03-23/meta-data/ami-launch-index', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,155 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/ami-launch-index (200, 1b) after 1 attempts
2025-06-29 11:43:56,156 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/ami-manifest-path' with {'url': 'http://***************:80/2021-03-23/meta-data/ami-manifest-path', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,158 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/ami-manifest-path (200, 9b) after 1 attempts
2025-06-29 11:43:56,158 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/hostname' with {'url': 'http://***************:80/2021-03-23/meta-data/hostname', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,160 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/hostname (200, 42b) after 1 attempts
2025-06-29 11:43:56,160 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/instance-action' with {'url': 'http://***************:80/2021-03-23/meta-data/instance-action', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,162 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/instance-action (200, 4b) after 1 attempts
2025-06-29 11:43:56,162 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/instance-id' with {'url': 'http://***************:80/2021-03-23/meta-data/instance-id', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,164 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/instance-id (200, 19b) after 1 attempts
2025-06-29 11:43:56,164 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/instance-life-cycle' with {'url': 'http://***************:80/2021-03-23/meta-data/instance-life-cycle', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,166 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/instance-life-cycle (200, 9b) after 1 attempts
2025-06-29 11:43:56,167 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/instance-type' with {'url': 'http://***************:80/2021-03-23/meta-data/instance-type', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,169 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/instance-type (200, 8b) after 1 attempts
2025-06-29 11:43:56,169 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/local-hostname' with {'url': 'http://***************:80/2021-03-23/meta-data/local-hostname', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,171 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/local-hostname (200, 42b) after 1 attempts
2025-06-29 11:43:56,171 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/local-ipv4' with {'url': 'http://***************:80/2021-03-23/meta-data/local-ipv4', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,173 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/local-ipv4 (200, 12b) after 1 attempts
2025-06-29 11:43:56,173 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/mac' with {'url': 'http://***************:80/2021-03-23/meta-data/mac', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,176 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/mac (200, 17b) after 1 attempts
2025-06-29 11:43:56,176 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/profile' with {'url': 'http://***************:80/2021-03-23/meta-data/profile', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,178 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/profile (200, 11b) after 1 attempts
2025-06-29 11:43:56,178 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/public-hostname' with {'url': 'http://***************:80/2021-03-23/meta-data/public-hostname', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,180 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/public-hostname (200, 49b) after 1 attempts
2025-06-29 11:43:56,180 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/public-ipv4' with {'url': 'http://***************:80/2021-03-23/meta-data/public-ipv4', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,182 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/public-ipv4 (200, 13b) after 1 attempts
2025-06-29 11:43:56,183 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/reservation-id' with {'url': 'http://***************:80/2021-03-23/meta-data/reservation-id', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,185 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/reservation-id (200, 19b) after 1 attempts
2025-06-29 11:43:56,185 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/security-groups' with {'url': 'http://***************:80/2021-03-23/meta-data/security-groups', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,187 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/security-groups (200, 56b) after 1 attempts
2025-06-29 11:43:56,187 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/dynamic/instance-identity' with {'url': 'http://***************:80/2021-03-23/dynamic/instance-identity', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,189 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/dynamic/instance-identity (200, 32b) after 1 attempts
2025-06-29 11:43:56,189 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/dynamic/instance-identity/document' with {'url': 'http://***************:80/2021-03-23/dynamic/instance-identity/document', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,191 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/dynamic/instance-identity/document (200, 476b) after 1 attempts
2025-06-29 11:43:56,191 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/dynamic/instance-identity/pkcs7' with {'url': 'http://***************:80/2021-03-23/dynamic/instance-identity/pkcs7', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,193 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/dynamic/instance-identity/pkcs7 (200, 1171b) after 1 attempts
2025-06-29 11:43:56,193 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/dynamic/instance-identity/rsa2048' with {'url': 'http://***************:80/2021-03-23/dynamic/instance-identity/rsa2048', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,195 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/dynamic/instance-identity/rsa2048 (200, 1491b) after 1 attempts
2025-06-29 11:43:56,195 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/dynamic/instance-identity/signature' with {'url': 'http://***************:80/2021-03-23/dynamic/instance-identity/signature', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-29 11:43:56,197 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/dynamic/instance-identity/signature (200, 174b) after 1 attempts
2025-06-29 11:43:56,197 - util.py[DEBUG]: Crawl of metadata service took 0.149 seconds
2025-06-29 11:43:56,199 - util.py[DEBUG]: Writing to /run/cloud-init/cloud-id-aws - wb: [644] 4 bytes
2025-06-29 11:43:56,200 - util.py[DEBUG]: Restoring selinux mode for /run/cloud-init/cloud-id-aws (recursive=False)
2025-06-29 11:43:56,201 - util.py[DEBUG]: Restoring selinux mode for /run/cloud-init/cloud-id-aws (recursive=False)
2025-06-29 11:43:56,201 - util.py[DEBUG]: Creating symbolic link from '/run/cloud-init/cloud-id' => '/run/cloud-init/cloud-id-aws'
2025-06-29 11:43:56,202 - atomic_helper.py[DEBUG]: Atomically writing to file /run/cloud-init/instance-data-sensitive.json (via temporary file /run/cloud-init/tmp4j6vgfj9) - w: [600] 12263 bytes/chars
2025-06-29 11:43:56,202 - atomic_helper.py[DEBUG]: Atomically writing to file /run/cloud-init/instance-data.json (via temporary file /run/cloud-init/tmpnxyobc7j) - w: [644] 8121 bytes/chars
2025-06-29 11:43:56,202 - handlers.py[DEBUG]: finish: init-network/search-Ec2: SUCCESS: found network data from DataSourceEc2
2025-06-29 11:43:56,203 - stages.py[INFO]: Loaded datasource DataSourceEc2 - DataSourceEc2
2025-06-29 11:43:56,203 - util.py[DEBUG]: Reading from /etc/cloud/cloud.cfg (quiet=False)
2025-06-29 11:43:56,203 - util.py[DEBUG]: Read 2553 bytes from /etc/cloud/cloud.cfg
2025-06-29 11:43:56,203 - util.py[DEBUG]: Attempting to load yaml from string of length 2553 with allowed root types (<class 'dict'>,)
2025-06-29 11:43:56,209 - util.py[DEBUG]: Reading from /etc/cloud/cloud.cfg.d/40_selinux-reboot.cfg (quiet=False)
2025-06-29 11:43:56,210 - util.py[DEBUG]: Read 174 bytes from /etc/cloud/cloud.cfg.d/40_selinux-reboot.cfg
2025-06-29 11:43:56,210 - util.py[DEBUG]: Attempting to load yaml from string of length 174 with allowed root types (<class 'dict'>,)
2025-06-29 11:43:56,210 - util.py[DEBUG]: Reading from /etc/cloud/cloud.cfg.d/10_aws_dnfvars.cfg (quiet=False)
2025-06-29 11:43:56,210 - util.py[DEBUG]: Read 591 bytes from /etc/cloud/cloud.cfg.d/10_aws_dnfvars.cfg
2025-06-29 11:43:56,210 - util.py[DEBUG]: Attempting to load yaml from string of length 591 with allowed root types (<class 'dict'>,)
2025-06-29 11:43:56,212 - util.py[DEBUG]: Reading from /etc/cloud/cloud.cfg.d/05_logging.cfg (quiet=False)
2025-06-29 11:43:56,212 - util.py[DEBUG]: Read 2070 bytes from /etc/cloud/cloud.cfg.d/05_logging.cfg
2025-06-29 11:43:56,212 - util.py[DEBUG]: Attempting to load yaml from string of length 2070 with allowed root types (<class 'dict'>,)
2025-06-29 11:43:56,215 - util.py[DEBUG]: Reading from /etc/cloud/cloud.cfg.d/01_amazon-ec2.cfg (quiet=False)
2025-06-29 11:43:56,215 - util.py[DEBUG]: Read 237 bytes from /etc/cloud/cloud.cfg.d/01_amazon-ec2.cfg
2025-06-29 11:43:56,215 - util.py[DEBUG]: Attempting to load yaml from string of length 237 with allowed root types (<class 'dict'>,)
2025-06-29 11:43:56,216 - util.py[DEBUG]: Reading from /run/cloud-init/cloud.cfg (quiet=False)
2025-06-29 11:43:56,216 - util.py[DEBUG]: Attempting to load yaml from string of length 0 with allowed root types (<class 'dict'>,)
2025-06-29 11:43:56,216 - util.py[DEBUG]: loaded blob returned None, returning default.
2025-06-29 11:43:56,217 - util.py[DEBUG]: Attempting to remove /var/lib/cloud/instance
2025-06-29 11:43:56,217 - util.py[DEBUG]: Creating symbolic link from '/var/lib/cloud/instance' => '/var/lib/cloud/instances/i-06cc7bc2ee1b17d55'
2025-06-29 11:43:56,217 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55 (recursive=True)
2025-06-29 11:43:56,220 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/handlers (recursive=False)
2025-06-29 11:43:56,221 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55 (recursive=True)
2025-06-29 11:43:56,225 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/scripts (recursive=False)
2025-06-29 11:43:56,225 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55 (recursive=True)
2025-06-29 11:43:56,230 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem (recursive=False)
2025-06-29 11:43:56,230 - util.py[DEBUG]: Reading from /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/datasource (quiet=False)
2025-06-29 11:43:56,230 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/datasource - wb: [644] 29 bytes
2025-06-29 11:43:56,230 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/datasource (recursive=False)
2025-06-29 11:43:56,231 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/datasource (recursive=False)
2025-06-29 11:43:56,232 - util.py[DEBUG]: Writing to /var/lib/cloud/data/previous-datasource - wb: [644] 29 bytes
2025-06-29 11:43:56,232 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/data/previous-datasource (recursive=False)
2025-06-29 11:43:56,233 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/data/previous-datasource (recursive=False)
2025-06-29 11:43:56,233 - util.py[DEBUG]: Reading from /var/lib/cloud/data/instance-id (quiet=False)
2025-06-29 11:43:56,233 - stages.py[DEBUG]: previous iid found to be NO_PREVIOUS_INSTANCE_ID
2025-06-29 11:43:56,233 - util.py[DEBUG]: Writing to /var/lib/cloud/data/instance-id - wb: [644] 20 bytes
2025-06-29 11:43:56,233 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/data/instance-id (recursive=False)
2025-06-29 11:43:56,234 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/data/instance-id (recursive=False)
2025-06-29 11:43:56,234 - util.py[DEBUG]: Writing to /run/cloud-init/.instance-id - wb: [644] 20 bytes
2025-06-29 11:43:56,235 - util.py[DEBUG]: Restoring selinux mode for /run/cloud-init/.instance-id (recursive=False)
2025-06-29 11:43:56,235 - util.py[DEBUG]: Restoring selinux mode for /run/cloud-init/.instance-id (recursive=False)
2025-06-29 11:43:56,235 - util.py[DEBUG]: Writing to /var/lib/cloud/data/previous-instance-id - wb: [644] 24 bytes
2025-06-29 11:43:56,235 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/data/previous-instance-id (recursive=False)
2025-06-29 11:43:56,236 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/data/previous-instance-id (recursive=False)
2025-06-29 11:43:56,237 - util.py[DEBUG]: Writing to /var/lib/cloud/instance/obj.pkl - wb: [400] 12730 bytes
2025-06-29 11:43:56,237 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/obj.pkl (recursive=False)
2025-06-29 11:43:56,238 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/obj.pkl (recursive=False)
2025-06-29 11:43:56,238 - main.py[DEBUG]: [net] init will now be targeting instance id: i-06cc7bc2ee1b17d55. new=True
2025-06-29 11:43:56,238 - util.py[DEBUG]: Reading from /etc/cloud/cloud.cfg (quiet=False)
2025-06-29 11:43:56,238 - util.py[DEBUG]: Read 2553 bytes from /etc/cloud/cloud.cfg
2025-06-29 11:43:56,238 - util.py[DEBUG]: Attempting to load yaml from string of length 2553 with allowed root types (<class 'dict'>,)
2025-06-29 11:43:56,245 - util.py[DEBUG]: Reading from /etc/cloud/cloud.cfg.d/40_selinux-reboot.cfg (quiet=False)
2025-06-29 11:43:56,245 - util.py[DEBUG]: Read 174 bytes from /etc/cloud/cloud.cfg.d/40_selinux-reboot.cfg
2025-06-29 11:43:56,245 - util.py[DEBUG]: Attempting to load yaml from string of length 174 with allowed root types (<class 'dict'>,)
2025-06-29 11:43:56,246 - util.py[DEBUG]: Reading from /etc/cloud/cloud.cfg.d/10_aws_dnfvars.cfg (quiet=False)
2025-06-29 11:43:56,246 - util.py[DEBUG]: Read 591 bytes from /etc/cloud/cloud.cfg.d/10_aws_dnfvars.cfg
2025-06-29 11:43:56,246 - util.py[DEBUG]: Attempting to load yaml from string of length 591 with allowed root types (<class 'dict'>,)
2025-06-29 11:43:56,247 - util.py[DEBUG]: Reading from /etc/cloud/cloud.cfg.d/05_logging.cfg (quiet=False)
2025-06-29 11:43:56,247 - util.py[DEBUG]: Read 2070 bytes from /etc/cloud/cloud.cfg.d/05_logging.cfg
2025-06-29 11:43:56,247 - util.py[DEBUG]: Attempting to load yaml from string of length 2070 with allowed root types (<class 'dict'>,)
2025-06-29 11:43:56,250 - util.py[DEBUG]: Reading from /etc/cloud/cloud.cfg.d/01_amazon-ec2.cfg (quiet=False)
2025-06-29 11:43:56,250 - util.py[DEBUG]: Read 237 bytes from /etc/cloud/cloud.cfg.d/01_amazon-ec2.cfg
2025-06-29 11:43:56,250 - util.py[DEBUG]: Attempting to load yaml from string of length 237 with allowed root types (<class 'dict'>,)
2025-06-29 11:43:56,251 - util.py[DEBUG]: Reading from /run/cloud-init/cloud.cfg (quiet=False)
2025-06-29 11:43:56,251 - util.py[DEBUG]: Attempting to load yaml from string of length 0 with allowed root types (<class 'dict'>,)
2025-06-29 11:43:56,251 - util.py[DEBUG]: loaded blob returned None, returning default.
2025-06-29 11:43:56,252 - util.py[DEBUG]: Reading from /sys/class/net/ens5/address (quiet=False)
2025-06-29 11:43:56,252 - util.py[DEBUG]: Read 18 bytes from /sys/class/net/ens5/address
2025-06-29 11:43:56,252 - util.py[DEBUG]: Reading from /sys/class/net/lo/address (quiet=False)
2025-06-29 11:43:56,252 - util.py[DEBUG]: Read 18 bytes from /sys/class/net/lo/address
2025-06-29 11:43:56,253 - util.py[DEBUG]: Reading from /sys/class/net/ens5/name_assign_type (quiet=False)
2025-06-29 11:43:56,253 - util.py[DEBUG]: Read 2 bytes from /sys/class/net/ens5/name_assign_type
2025-06-29 11:43:56,253 - util.py[DEBUG]: Reading from /sys/class/net/ens5/address (quiet=False)
2025-06-29 11:43:56,253 - util.py[DEBUG]: Read 18 bytes from /sys/class/net/ens5/address
2025-06-29 11:43:56,253 - util.py[DEBUG]: Reading from /sys/class/net/ens5/carrier (quiet=False)
2025-06-29 11:43:56,253 - util.py[DEBUG]: Read 2 bytes from /sys/class/net/ens5/carrier
2025-06-29 11:43:56,253 - util.py[DEBUG]: Reading from /sys/class/net/ens5/addr_assign_type (quiet=False)
2025-06-29 11:43:56,253 - util.py[DEBUG]: Read 2 bytes from /sys/class/net/ens5/addr_assign_type
2025-06-29 11:43:56,253 - util.py[DEBUG]: Reading from /sys/class/net/ens5/uevent (quiet=False)
2025-06-29 11:43:56,253 - util.py[DEBUG]: Read 25 bytes from /sys/class/net/ens5/uevent
2025-06-29 11:43:56,253 - util.py[DEBUG]: Reading from /sys/class/net/ens5/address (quiet=False)
2025-06-29 11:43:56,253 - util.py[DEBUG]: Read 18 bytes from /sys/class/net/ens5/address
2025-06-29 11:43:56,253 - __init__.py[DEBUG]: ovs-vsctl not in PATH; not detecting Open vSwitch interfaces
2025-06-29 11:43:56,254 - util.py[DEBUG]: Reading from /sys/class/net/ens5/device/device (quiet=False)
2025-06-29 11:43:56,254 - util.py[DEBUG]: Read 7 bytes from /sys/class/net/ens5/device/device
2025-06-29 11:43:56,254 - util.py[DEBUG]: Reading from /sys/class/net/lo/addr_assign_type (quiet=False)
2025-06-29 11:43:56,254 - util.py[DEBUG]: Read 2 bytes from /sys/class/net/lo/addr_assign_type
2025-06-29 11:43:56,254 - util.py[DEBUG]: Reading from /sys/class/net/lo/uevent (quiet=False)
2025-06-29 11:43:56,254 - util.py[DEBUG]: Read 23 bytes from /sys/class/net/lo/uevent
2025-06-29 11:43:56,254 - util.py[DEBUG]: Reading from /sys/class/net/lo/address (quiet=False)
2025-06-29 11:43:56,254 - util.py[DEBUG]: Read 18 bytes from /sys/class/net/lo/address
2025-06-29 11:43:56,254 - util.py[DEBUG]: Reading from /sys/class/net/lo/device/device (quiet=False)
2025-06-29 11:43:56,254 - util.py[DEBUG]: Reading from /sys/class/net/ens5/type (quiet=False)
2025-06-29 11:43:56,254 - util.py[DEBUG]: Read 2 bytes from /sys/class/net/ens5/type
2025-06-29 11:43:56,254 - util.py[DEBUG]: Reading from /sys/class/net/lo/type (quiet=False)
2025-06-29 11:43:56,254 - util.py[DEBUG]: Read 4 bytes from /sys/class/net/lo/type
2025-06-29 11:43:56,254 - stages.py[DEBUG]: network config disabled by system_cfg
2025-06-29 11:43:56,254 - stages.py[INFO]: network config is disabled by system_cfg
2025-06-29 11:43:56,254 - handlers.py[DEBUG]: start: init-network/setup-datasource: setting up datasource
2025-06-29 11:43:56,254 - handlers.py[DEBUG]: finish: init-network/setup-datasource: SUCCESS: setting up datasource
2025-06-29 11:43:56,254 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/user-data.txt - wb: [600] 2478 bytes
2025-06-29 11:43:56,255 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/user-data.txt (recursive=False)
2025-06-29 11:43:56,256 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/user-data.txt (recursive=False)
2025-06-29 11:43:56,257 - util.py[DEBUG]: Attempting to load yaml from string of length 104 with allowed root types (<class 'dict'>,)
2025-06-29 11:43:56,260 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/user-data.txt.i - wb: [600] 2500 bytes
2025-06-29 11:43:56,261 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/user-data.txt.i (recursive=False)
2025-06-29 11:43:56,262 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/user-data.txt.i (recursive=False)
2025-06-29 11:43:56,262 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/vendor-data.txt - wb: [600] 0 bytes
2025-06-29 11:43:56,262 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/vendor-data.txt (recursive=False)
2025-06-29 11:43:56,263 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/vendor-data.txt (recursive=False)
2025-06-29 11:43:56,264 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/vendor-data.txt.i - wb: [600] 308 bytes
2025-06-29 11:43:56,265 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/vendor-data.txt.i (recursive=False)
2025-06-29 11:43:56,265 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/vendor-data.txt.i (recursive=False)
2025-06-29 11:43:56,266 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/vendor-data2.txt - wb: [600] 0 bytes
2025-06-29 11:43:56,266 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/vendor-data2.txt (recursive=False)
2025-06-29 11:43:56,267 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/vendor-data2.txt (recursive=False)
2025-06-29 11:43:56,268 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/vendor-data2.txt.i - wb: [600] 308 bytes
2025-06-29 11:43:56,268 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/vendor-data2.txt.i (recursive=False)
2025-06-29 11:43:56,269 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/vendor-data2.txt.i (recursive=False)
2025-06-29 11:43:56,269 - stages.py[DEBUG]: Using distro class <class 'cloudinit.distros.amazon.Distro'>
2025-06-29 11:43:56,270 - cc_set_hostname.py[DEBUG]: Setting the hostname to ip-172-31-4-198.us-west-2.compute.internal (ip-172-31-4-198)
2025-06-29 11:43:56,270 - subp.py[DEBUG]: Running command ['hostnamectl', 'set-hostname', 'ip-172-31-4-198.us-west-2.compute.internal'] with allowed return codes [0] (shell=False, capture=True)
2025-06-29 11:43:56,383 - __init__.py[DEBUG]: Non-persistently setting the system hostname to ip-172-31-4-198.us-west-2.compute.internal
2025-06-29 11:43:56,383 - subp.py[DEBUG]: Running command ['hostname', 'ip-172-31-4-198.us-west-2.compute.internal'] with allowed return codes [0] (shell=False, capture=True)
2025-06-29 11:43:56,388 - atomic_helper.py[DEBUG]: Atomically writing to file /var/lib/cloud/data/set-hostname (via temporary file /var/lib/cloud/data/tmpg51tze7r) - w: [644] 90 bytes/chars
2025-06-29 11:43:56,389 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/consume_data - wb: [644] 24 bytes
2025-06-29 11:43:56,390 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/consume_data (recursive=False)
2025-06-29 11:43:56,391 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/consume_data (recursive=False)
2025-06-29 11:43:56,392 - helpers.py[DEBUG]: Running consume_data using lock (<FileLock using file '/var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/consume_data'>)
2025-06-29 11:43:56,392 - handlers.py[DEBUG]: start: init-network/consume-user-data: reading and applying user-data
2025-06-29 11:43:56,392 - launch_index.py[DEBUG]: Discarding 0 multipart messages which do not match launch index 0
2025-06-29 11:43:56,393 - stages.py[DEBUG]: Added default handler for {'text/cloud-config', 'text/cloud-config-jsonp'} from CloudConfigPartHandler: [['text/cloud-config', 'text/cloud-config-jsonp']]
2025-06-29 11:43:56,393 - stages.py[DEBUG]: Added default handler for {'text/x-shellscript'} from ShellScriptPartHandler: [['text/x-shellscript']]
2025-06-29 11:43:56,393 - stages.py[DEBUG]: Added default handler for {'text/x-shellscript-per-boot'} from ShellScriptByFreqPartHandler: [['text/x-shellscript-per-boot']]
2025-06-29 11:43:56,393 - stages.py[DEBUG]: Added default handler for {'text/x-shellscript-per-instance'} from ShellScriptByFreqPartHandler: [['text/x-shellscript-per-instance']]
2025-06-29 11:43:56,393 - stages.py[DEBUG]: Added default handler for {'text/x-shellscript-per-once'} from ShellScriptByFreqPartHandler: [['text/x-shellscript-per-once']]
2025-06-29 11:43:56,393 - stages.py[DEBUG]: Added default handler for {'text/cloud-boothook'} from BootHookPartHandler: [['text/cloud-boothook']]
2025-06-29 11:43:56,393 - stages.py[DEBUG]: Added default handler for {'text/jinja2'} from JinjaTemplatePartHandler: [['text/jinja2']]
2025-06-29 11:43:56,393 - __init__.py[DEBUG]: Calling handler CloudConfigPartHandler: [['text/cloud-config', 'text/cloud-config-jsonp']] (__begin__, None, 3) with frequency once-per-instance
2025-06-29 11:43:56,393 - __init__.py[DEBUG]: Calling handler ShellScriptPartHandler: [['text/x-shellscript']] (__begin__, None, 2) with frequency once-per-instance
2025-06-29 11:43:56,393 - __init__.py[DEBUG]: Calling handler ShellScriptByFreqPartHandler: [['text/x-shellscript-per-boot']] (__begin__, None, 2) with frequency once-per-instance
2025-06-29 11:43:56,393 - __init__.py[DEBUG]: Calling handler ShellScriptByFreqPartHandler: [['text/x-shellscript-per-instance']] (__begin__, None, 2) with frequency once-per-instance
2025-06-29 11:43:56,393 - __init__.py[DEBUG]: Calling handler ShellScriptByFreqPartHandler: [['text/x-shellscript-per-once']] (__begin__, None, 2) with frequency once-per-instance
2025-06-29 11:43:56,393 - __init__.py[DEBUG]: Calling handler BootHookPartHandler: [['text/cloud-boothook']] (__begin__, None, 2) with frequency once-per-instance
2025-06-29 11:43:56,393 - __init__.py[DEBUG]: Calling handler JinjaTemplatePartHandler: [['text/jinja2']] (__begin__, None, 3) with frequency once-per-instance
2025-06-29 11:43:56,394 - __init__.py[DEBUG]: {'Content-Type': 'text/cloud-config; charset="us-ascii"', 'MIME-Version': '1.0', 'Content-Transfer-Encoding': '7bit', 'Content-Disposition': 'attachment; filename="cloud-config.txt"'}
2025-06-29 11:43:56,394 - __init__.py[DEBUG]: Calling handler CloudConfigPartHandler: [['text/cloud-config', 'text/cloud-config-jsonp']] (text/cloud-config, cloud-config.txt, 3) with frequency once-per-instance
2025-06-29 11:43:56,394 - util.py[DEBUG]: Attempting to load yaml from string of length 104 with allowed root types (<class 'dict'>,)
2025-06-29 11:43:56,395 - cloud_config.py[DEBUG]: Merging by applying [('dict', ['replace']), ('list', []), ('str', [])]
2025-06-29 11:43:56,395 - __init__.py[DEBUG]: {'Content-Type': 'text/x-shellscript; charset="us-ascii"', 'MIME-Version': '1.0', 'Content-Transfer-Encoding': '7bit', 'Content-Disposition': 'attachment; filename="user-data.txt"'}
2025-06-29 11:43:56,395 - __init__.py[DEBUG]: Calling handler ShellScriptPartHandler: [['text/x-shellscript']] (text/x-shellscript, user-data.txt, 2) with frequency once-per-instance
2025-06-29 11:43:56,395 - util.py[DEBUG]: Writing to /var/lib/cloud/instance/scripts/user-data.txt - wb: [700] 1829 bytes
2025-06-29 11:43:56,395 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/scripts/user-data.txt (recursive=False)
2025-06-29 11:43:56,396 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/scripts/user-data.txt (recursive=False)
2025-06-29 11:43:56,397 - __init__.py[DEBUG]: Calling handler CloudConfigPartHandler: [['text/cloud-config', 'text/cloud-config-jsonp']] (__end__, None, 3) with frequency once-per-instance
2025-06-29 11:43:56,397 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/cloud-config.txt - wb: [600] 154 bytes
2025-06-29 11:43:56,398 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/cloud-config.txt (recursive=False)
2025-06-29 11:43:56,398 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/cloud-config.txt (recursive=False)
2025-06-29 11:43:56,399 - __init__.py[DEBUG]: Calling handler ShellScriptPartHandler: [['text/x-shellscript']] (__end__, None, 2) with frequency once-per-instance
2025-06-29 11:43:56,399 - __init__.py[DEBUG]: Calling handler ShellScriptByFreqPartHandler: [['text/x-shellscript-per-boot']] (__end__, None, 2) with frequency once-per-instance
2025-06-29 11:43:56,399 - __init__.py[DEBUG]: Calling handler ShellScriptByFreqPartHandler: [['text/x-shellscript-per-instance']] (__end__, None, 2) with frequency once-per-instance
2025-06-29 11:43:56,399 - __init__.py[DEBUG]: Calling handler ShellScriptByFreqPartHandler: [['text/x-shellscript-per-once']] (__end__, None, 2) with frequency once-per-instance
2025-06-29 11:43:56,399 - __init__.py[DEBUG]: Calling handler BootHookPartHandler: [['text/cloud-boothook']] (__end__, None, 2) with frequency once-per-instance
2025-06-29 11:43:56,399 - __init__.py[DEBUG]: Calling handler JinjaTemplatePartHandler: [['text/jinja2']] (__end__, None, 3) with frequency once-per-instance
2025-06-29 11:43:56,399 - handlers.py[DEBUG]: finish: init-network/consume-user-data: SUCCESS: reading and applying user-data
2025-06-29 11:43:56,399 - handlers.py[DEBUG]: start: init-network/consume-vendor-data: reading and applying vendor-data
2025-06-29 11:43:56,399 - stages.py[DEBUG]: no vendordata from datasource
2025-06-29 11:43:56,399 - handlers.py[DEBUG]: finish: init-network/consume-vendor-data: SUCCESS: reading and applying vendor-data
2025-06-29 11:43:56,399 - handlers.py[DEBUG]: start: init-network/consume-vendor-data2: reading and applying vendor-data2
2025-06-29 11:43:56,399 - stages.py[DEBUG]: no vendordata2 from datasource
2025-06-29 11:43:56,399 - handlers.py[DEBUG]: finish: init-network/consume-vendor-data2: SUCCESS: reading and applying vendor-data2
2025-06-29 11:43:56,399 - util.py[DEBUG]: Reading from /etc/cloud/cloud.cfg (quiet=False)
2025-06-29 11:43:56,400 - util.py[DEBUG]: Read 2553 bytes from /etc/cloud/cloud.cfg
2025-06-29 11:43:56,400 - util.py[DEBUG]: Attempting to load yaml from string of length 2553 with allowed root types (<class 'dict'>,)
2025-06-29 11:43:56,406 - util.py[DEBUG]: Reading from /etc/cloud/cloud.cfg.d/40_selinux-reboot.cfg (quiet=False)
2025-06-29 11:43:56,406 - util.py[DEBUG]: Read 174 bytes from /etc/cloud/cloud.cfg.d/40_selinux-reboot.cfg
2025-06-29 11:43:56,406 - util.py[DEBUG]: Attempting to load yaml from string of length 174 with allowed root types (<class 'dict'>,)
2025-06-29 11:43:56,407 - util.py[DEBUG]: Reading from /etc/cloud/cloud.cfg.d/10_aws_dnfvars.cfg (quiet=False)
2025-06-29 11:43:56,407 - util.py[DEBUG]: Read 591 bytes from /etc/cloud/cloud.cfg.d/10_aws_dnfvars.cfg
2025-06-29 11:43:56,407 - util.py[DEBUG]: Attempting to load yaml from string of length 591 with allowed root types (<class 'dict'>,)
2025-06-29 11:43:56,409 - util.py[DEBUG]: Reading from /etc/cloud/cloud.cfg.d/05_logging.cfg (quiet=False)
2025-06-29 11:43:56,409 - util.py[DEBUG]: Read 2070 bytes from /etc/cloud/cloud.cfg.d/05_logging.cfg
2025-06-29 11:43:56,409 - util.py[DEBUG]: Attempting to load yaml from string of length 2070 with allowed root types (<class 'dict'>,)
2025-06-29 11:43:56,411 - util.py[DEBUG]: Reading from /etc/cloud/cloud.cfg.d/01_amazon-ec2.cfg (quiet=False)
2025-06-29 11:43:56,412 - util.py[DEBUG]: Read 237 bytes from /etc/cloud/cloud.cfg.d/01_amazon-ec2.cfg
2025-06-29 11:43:56,412 - util.py[DEBUG]: Attempting to load yaml from string of length 237 with allowed root types (<class 'dict'>,)
2025-06-29 11:43:56,413 - util.py[DEBUG]: Reading from /run/cloud-init/cloud.cfg (quiet=False)
2025-06-29 11:43:56,413 - util.py[DEBUG]: Attempting to load yaml from string of length 0 with allowed root types (<class 'dict'>,)
2025-06-29 11:43:56,413 - util.py[DEBUG]: loaded blob returned None, returning default.
2025-06-29 11:43:56,413 - util.py[DEBUG]: Reading from /var/lib/cloud/instance/cloud-config.txt (quiet=False)
2025-06-29 11:43:56,413 - util.py[DEBUG]: Read 154 bytes from /var/lib/cloud/instance/cloud-config.txt
2025-06-29 11:43:56,413 - util.py[DEBUG]: Attempting to load yaml from string of length 154 with allowed root types (<class 'dict'>,)
2025-06-29 11:43:56,414 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-29 11:43:56,415 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-29 11:43:56,484 - util.py[DEBUG]: Reading from /var/lib/cloud/instance/cloud-config.txt (quiet=False)
2025-06-29 11:43:56,485 - util.py[DEBUG]: Read 154 bytes from /var/lib/cloud/instance/cloud-config.txt
2025-06-29 11:43:56,485 - util.py[DEBUG]: Attempting to load yaml from string of length 154 with allowed root types (<class 'dict'>,)
2025-06-29 11:43:56,488 - handlers.py[DEBUG]: start: init-network/activate-datasource: activating datasource
2025-06-29 11:43:56,488 - util.py[DEBUG]: Writing to /var/lib/cloud/instance/obj.pkl - wb: [400] 16907 bytes
2025-06-29 11:43:56,490 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/obj.pkl (recursive=False)
2025-06-29 11:43:56,491 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/obj.pkl (recursive=False)
2025-06-29 11:43:56,491 - handlers.py[DEBUG]: finish: init-network/activate-datasource: SUCCESS: activating datasource
2025-06-29 11:43:56,491 - main.py[DEBUG]: no di_report found in config.
2025-06-29 11:43:56,493 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-29 11:43:56,493 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-29 11:43:56,496 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-29 11:43:56,496 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-29 11:43:56,498 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-29 11:43:56,498 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-29 11:43:56,501 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-29 11:43:56,501 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-29 11:43:56,503 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-29 11:43:56,503 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-29 11:43:56,507 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-29 11:43:56,507 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-29 11:43:56,509 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-29 11:43:56,510 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-29 11:43:56,514 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-29 11:43:56,514 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-29 11:43:56,516 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-29 11:43:56,516 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-29 11:43:56,519 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-29 11:43:56,519 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-29 11:43:56,521 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-29 11:43:56,521 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-29 11:43:56,523 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-29 11:43:56,523 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-29 11:43:56,525 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-29 11:43:56,526 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-29 11:43:56,528 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-29 11:43:56,528 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-29 11:43:56,531 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-29 11:43:56,531 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-29 11:43:56,534 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-29 11:43:56,534 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-29 11:43:56,536 - stages.py[DEBUG]: Using distro class <class 'cloudinit.distros.amazon.Distro'>
2025-06-29 11:43:56,536 - modules.py[DEBUG]: Running module migrator (<module 'cloudinit.config.cc_migrator' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_migrator.py'>) with frequency always
2025-06-29 11:43:56,536 - handlers.py[DEBUG]: start: init-network/config-migrator: running config-migrator with frequency always
2025-06-29 11:43:56,536 - helpers.py[DEBUG]: Running config-migrator using lock (<cloudinit.helpers.DummyLock object at 0x7f9112a63970>)
2025-06-29 11:43:56,537 - cc_migrator.py[DEBUG]: Migrated 0 semaphore files to there canonicalized names
2025-06-29 11:43:56,537 - handlers.py[DEBUG]: finish: init-network/config-migrator: SUCCESS: config-migrator ran successfully
2025-06-29 11:43:56,537 - modules.py[DEBUG]: Running module seed_random (<module 'cloudinit.config.cc_seed_random' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_seed_random.py'>) with frequency once-per-instance
2025-06-29 11:43:56,537 - handlers.py[DEBUG]: start: init-network/config-seed_random: running config-seed_random with frequency once-per-instance
2025-06-29 11:43:56,537 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_seed_random - wb: [644] 23 bytes
2025-06-29 11:43:56,538 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_seed_random (recursive=False)
2025-06-29 11:43:56,538 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_seed_random (recursive=False)
2025-06-29 11:43:56,539 - helpers.py[DEBUG]: Running config-seed_random using lock (<FileLock using file '/var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_seed_random'>)
2025-06-29 11:43:56,539 - cc_seed_random.py[DEBUG]: no command provided
2025-06-29 11:43:56,539 - handlers.py[DEBUG]: finish: init-network/config-seed_random: SUCCESS: config-seed_random ran successfully
2025-06-29 11:43:56,539 - modules.py[DEBUG]: Running module bootcmd (<module 'cloudinit.config.cc_bootcmd' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_bootcmd.py'>) with frequency always
2025-06-29 11:43:56,539 - handlers.py[DEBUG]: start: init-network/config-bootcmd: running config-bootcmd with frequency always
2025-06-29 11:43:56,539 - helpers.py[DEBUG]: Running config-bootcmd using lock (<cloudinit.helpers.DummyLock object at 0x7f9112a63970>)
2025-06-29 11:43:56,539 - cc_bootcmd.py[DEBUG]: Skipping module named bootcmd, no 'bootcmd' key in configuration
2025-06-29 11:43:56,539 - handlers.py[DEBUG]: finish: init-network/config-bootcmd: SUCCESS: config-bootcmd ran successfully
2025-06-29 11:43:56,539 - modules.py[DEBUG]: Running module write-files (<module 'cloudinit.config.cc_write_files' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_write_files.py'>) with frequency once-per-instance
2025-06-29 11:43:56,539 - handlers.py[DEBUG]: start: init-network/config-write-files: running config-write-files with frequency once-per-instance
2025-06-29 11:43:56,540 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_write_files - wb: [644] 24 bytes
2025-06-29 11:43:56,540 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_write_files (recursive=False)
2025-06-29 11:43:56,541 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_write_files (recursive=False)
2025-06-29 11:43:56,541 - helpers.py[DEBUG]: Running config-write-files using lock (<FileLock using file '/var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_write_files'>)
2025-06-29 11:43:56,541 - cc_write_files.py[DEBUG]: Skipping module named write-files, no/empty 'write_files' key in configuration
2025-06-29 11:43:56,541 - handlers.py[DEBUG]: finish: init-network/config-write-files: SUCCESS: config-write-files ran successfully
2025-06-29 11:43:56,541 - modules.py[DEBUG]: Running module write-metadata (<module 'cloudinit.config.cc_write_metadata' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_write_metadata.py'>) with frequency once-per-instance
2025-06-29 11:43:56,541 - handlers.py[DEBUG]: start: init-network/config-write-metadata: running config-write-metadata with frequency once-per-instance
2025-06-29 11:43:56,541 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_write_metadata - wb: [644] 23 bytes
2025-06-29 11:43:56,542 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_write_metadata (recursive=False)
2025-06-29 11:43:56,543 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_write_metadata (recursive=False)
2025-06-29 11:43:56,543 - helpers.py[DEBUG]: Running config-write-metadata using lock (<FileLock using file '/var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_write_metadata'>)
2025-06-29 11:43:56,543 - util.py[DEBUG]: Writing to /etc/dnf/vars/awsregion - wb: [644] 9 bytes
2025-06-29 11:43:56,545 - util.py[DEBUG]: Restoring selinux mode for /etc/dnf/vars/awsregion (recursive=False)
2025-06-29 11:43:56,546 - util.py[DEBUG]: Restoring selinux mode for /etc/dnf/vars/awsregion (recursive=False)
2025-06-29 11:43:56,546 - util.py[DEBUG]: Changing the ownership of /etc/dnf/vars/awsregion to 0:0
2025-06-29 11:43:56,546 - util.py[DEBUG]: Writing to /etc/dnf/vars/awsdomain - wb: [644] 13 bytes
2025-06-29 11:43:56,547 - util.py[DEBUG]: Restoring selinux mode for /etc/dnf/vars/awsdomain (recursive=False)
2025-06-29 11:43:56,547 - util.py[DEBUG]: Restoring selinux mode for /etc/dnf/vars/awsdomain (recursive=False)
2025-06-29 11:43:56,547 - util.py[DEBUG]: Changing the ownership of /etc/dnf/vars/awsdomain to 0:0
2025-06-29 11:43:56,547 - handlers.py[DEBUG]: finish: init-network/config-write-metadata: SUCCESS: config-write-metadata ran successfully
2025-06-29 11:43:56,547 - modules.py[DEBUG]: Running module growpart (<module 'cloudinit.config.cc_growpart' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_growpart.py'>) with frequency always
2025-06-29 11:43:56,548 - handlers.py[DEBUG]: start: init-network/config-growpart: running config-growpart with frequency always
2025-06-29 11:43:56,548 - helpers.py[DEBUG]: Running config-growpart using lock (<cloudinit.helpers.DummyLock object at 0x7f9112a63b80>)
2025-06-29 11:43:56,548 - cc_growpart.py[DEBUG]: No 'growpart' entry in cfg.  Using default: {'mode': 'auto', 'devices': ['/'], 'ignore_growroot_disabled': False}
2025-06-29 11:43:56,548 - subp.py[DEBUG]: Running command ['growpart', '--help'] with allowed return codes [0] (shell=False, capture=True)
2025-06-29 11:43:56,567 - util.py[DEBUG]: Reading from /proc/1425/mountinfo (quiet=False)
2025-06-29 11:43:56,567 - util.py[DEBUG]: Read 2940 bytes from /proc/1425/mountinfo
2025-06-29 11:43:56,567 - util.py[DEBUG]: Reading from /sys/class/block/nvme0n1p1/partition (quiet=False)
2025-06-29 11:43:56,568 - util.py[DEBUG]: Read 2 bytes from /sys/class/block/nvme0n1p1/partition
2025-06-29 11:43:56,568 - util.py[DEBUG]: Reading from /sys/devices/pci0000:00/0000:00:04.0/nvme/nvme0/nvme0n1/dev (quiet=False)
2025-06-29 11:43:56,568 - util.py[DEBUG]: Read 6 bytes from /sys/devices/pci0000:00/0000:00:04.0/nvme/nvme0/nvme0n1/dev
2025-06-29 11:43:56,568 - subp.py[DEBUG]: Running command ['growpart', '--dry-run', '/dev/nvme0n1', '1'] with allowed return codes [0] (shell=False, capture=True)
2025-06-29 11:43:56,623 - util.py[DEBUG]: resize_devices took 0.056 seconds
2025-06-29 11:43:56,623 - cc_growpart.py[DEBUG]: '/' NOCHANGE: no change necessary (/dev/nvme0n1, 1)
2025-06-29 11:43:56,623 - handlers.py[DEBUG]: finish: init-network/config-growpart: SUCCESS: config-growpart ran successfully
2025-06-29 11:43:56,623 - modules.py[DEBUG]: Running module resizefs (<module 'cloudinit.config.cc_resizefs' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_resizefs.py'>) with frequency always
2025-06-29 11:43:56,624 - handlers.py[DEBUG]: start: init-network/config-resizefs: running config-resizefs with frequency always
2025-06-29 11:43:56,624 - helpers.py[DEBUG]: Running config-resizefs using lock (<cloudinit.helpers.DummyLock object at 0x7f9112a63970>)
2025-06-29 11:43:56,624 - util.py[DEBUG]: Reading from /proc/1425/mountinfo (quiet=False)
2025-06-29 11:43:56,624 - util.py[DEBUG]: Read 2940 bytes from /proc/1425/mountinfo
2025-06-29 11:43:56,624 - cc_resizefs.py[DEBUG]: resize_info: dev=/dev/nvme0n1p1 mnt_point=/ path=/
2025-06-29 11:43:56,624 - cc_resizefs.py[DEBUG]: Resizing / (xfs) using xfs_growfs /
2025-06-29 11:43:56,625 - util.py[DEBUG]: Forked child 1546 who will run callback log_time
2025-06-29 11:43:56,626 - cc_resizefs.py[DEBUG]: Resizing (via forking) root filesystem (type=xfs, val=noblock)
2025-06-29 11:43:56,626 - subp.py[DEBUG]: Running command ('xfs_growfs', '/') with allowed return codes [0] (shell=False, capture=True)
2025-06-29 11:43:56,626 - handlers.py[DEBUG]: finish: init-network/config-resizefs: SUCCESS: config-resizefs ran successfully
2025-06-29 11:43:56,626 - modules.py[DEBUG]: Running module disk_setup (<module 'cloudinit.config.cc_disk_setup' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_disk_setup.py'>) with frequency once-per-instance
2025-06-29 11:43:56,627 - handlers.py[DEBUG]: start: init-network/config-disk_setup: running config-disk_setup with frequency once-per-instance
2025-06-29 11:43:56,627 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_disk_setup - wb: [644] 25 bytes
2025-06-29 11:43:56,628 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_disk_setup (recursive=False)
2025-06-29 11:43:56,629 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_disk_setup (recursive=False)
2025-06-29 11:43:56,630 - helpers.py[DEBUG]: Running config-disk_setup using lock (<FileLock using file '/var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_disk_setup'>)
2025-06-29 11:43:56,630 - handlers.py[DEBUG]: finish: init-network/config-disk_setup: SUCCESS: config-disk_setup ran successfully
2025-06-29 11:43:56,630 - modules.py[DEBUG]: Running module mounts (<module 'cloudinit.config.cc_mounts' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_mounts.py'>) with frequency once-per-instance
2025-06-29 11:43:56,630 - handlers.py[DEBUG]: start: init-network/config-mounts: running config-mounts with frequency once-per-instance
2025-06-29 11:43:56,630 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_mounts - wb: [644] 25 bytes
2025-06-29 11:43:56,631 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_mounts (recursive=False)
2025-06-29 11:43:56,631 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_mounts (recursive=False)
2025-06-29 11:43:56,632 - helpers.py[DEBUG]: Running config-mounts using lock (<FileLock using file '/var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_mounts'>)
2025-06-29 11:43:56,632 - cc_mounts.py[DEBUG]: mounts configuration is []
2025-06-29 11:43:56,632 - util.py[DEBUG]: Reading from /etc/fstab (quiet=False)
2025-06-29 11:43:56,632 - util.py[DEBUG]: Read 217 bytes from /etc/fstab
2025-06-29 11:43:56,632 - cc_mounts.py[DEBUG]: Attempting to determine the real name of ephemeral0
2025-06-29 11:43:56,632 - DataSourceEc2.py[DEBUG]: Unable to convert ephemeral0 to a device
2025-06-29 11:43:56,632 - cc_mounts.py[DEBUG]: changed default device ephemeral0 => None
2025-06-29 11:43:56,632 - cc_mounts.py[DEBUG]: Ignoring nonexistent default named mount ephemeral0
2025-06-29 11:43:56,633 - cc_mounts.py[DEBUG]: Attempting to determine the real name of swap
2025-06-29 11:43:56,633 - DataSourceEc2.py[DEBUG]: Unable to convert swap to a device
2025-06-29 11:43:56,633 - cc_mounts.py[DEBUG]: changed default device swap => None
2025-06-29 11:43:56,633 - cc_mounts.py[DEBUG]: Ignoring nonexistent default named mount swap
2025-06-29 11:43:56,633 - cc_mounts.py[DEBUG]: no need to setup swap
2025-06-29 11:43:56,633 - cc_mounts.py[DEBUG]: No modifications to fstab needed
2025-06-29 11:43:56,633 - handlers.py[DEBUG]: finish: init-network/config-mounts: SUCCESS: config-mounts ran successfully
2025-06-29 11:43:56,633 - modules.py[DEBUG]: Running module set_hostname (<module 'cloudinit.config.cc_set_hostname' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_set_hostname.py'>) with frequency always
2025-06-29 11:43:56,633 - handlers.py[DEBUG]: start: init-network/config-set_hostname: running config-set_hostname with frequency always
2025-06-29 11:43:56,633 - helpers.py[DEBUG]: Running config-set_hostname using lock (<cloudinit.helpers.DummyLock object at 0x7f9112a63d60>)
2025-06-29 11:43:56,633 - util.py[DEBUG]: Reading from /var/lib/cloud/data/set-hostname (quiet=False)
2025-06-29 11:43:56,633 - util.py[DEBUG]: Read 90 bytes from /var/lib/cloud/data/set-hostname
2025-06-29 11:43:56,633 - cc_set_hostname.py[DEBUG]: No hostname changes. Skipping set-hostname
2025-06-29 11:43:56,633 - handlers.py[DEBUG]: finish: init-network/config-set_hostname: SUCCESS: config-set_hostname ran successfully
2025-06-29 11:43:56,633 - modules.py[DEBUG]: Running module update_hostname (<module 'cloudinit.config.cc_update_hostname' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_update_hostname.py'>) with frequency always
2025-06-29 11:43:56,634 - handlers.py[DEBUG]: start: init-network/config-update_hostname: running config-update_hostname with frequency always
2025-06-29 11:43:56,634 - helpers.py[DEBUG]: Running config-update_hostname using lock (<cloudinit.helpers.DummyLock object at 0x7f9112a63b80>)
2025-06-29 11:43:56,634 - cc_update_hostname.py[DEBUG]: Updating hostname to ip-172-31-4-198.us-west-2.compute.internal (ip-172-31-4-198)
2025-06-29 11:43:56,634 - subp.py[DEBUG]: Running command ['hostname'] with allowed return codes [0] (shell=False, capture=True)
2025-06-29 11:43:56,637 - __init__.py[DEBUG]: Attempting to update hostname to ip-172-31-4-198.us-west-2.compute.internal in 1 files
2025-06-29 11:43:56,637 - util.py[DEBUG]: Writing to /var/lib/cloud/data/previous-hostname - wb: [644] 42 bytes
2025-06-29 11:43:56,638 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/data/previous-hostname (recursive=False)
2025-06-29 11:43:56,639 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/data/previous-hostname (recursive=False)
2025-06-29 11:43:56,639 - handlers.py[DEBUG]: finish: init-network/config-update_hostname: SUCCESS: config-update_hostname ran successfully
2025-06-29 11:43:56,639 - modules.py[DEBUG]: Running module update_etc_hosts (<module 'cloudinit.config.cc_update_etc_hosts' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_update_etc_hosts.py'>) with frequency always
2025-06-29 11:43:56,639 - handlers.py[DEBUG]: start: init-network/config-update_etc_hosts: running config-update_etc_hosts with frequency always
2025-06-29 11:43:56,639 - helpers.py[DEBUG]: Running config-update_etc_hosts using lock (<cloudinit.helpers.DummyLock object at 0x7f9112a63d60>)
2025-06-29 11:43:56,640 - cc_update_etc_hosts.py[DEBUG]: Configuration option 'manage_etc_hosts' is not set, not managing /etc/hosts in module update_etc_hosts
2025-06-29 11:43:56,640 - handlers.py[DEBUG]: finish: init-network/config-update_etc_hosts: SUCCESS: config-update_etc_hosts ran successfully
2025-06-29 11:43:56,640 - modules.py[DEBUG]: Running module ca-certs (<module 'cloudinit.config.cc_ca_certs' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_ca_certs.py'>) with frequency once-per-instance
2025-06-29 11:43:56,640 - handlers.py[DEBUG]: start: init-network/config-ca-certs: running config-ca-certs with frequency once-per-instance
2025-06-29 11:43:56,640 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_ca_certs - wb: [644] 25 bytes
2025-06-29 11:43:56,640 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_ca_certs (recursive=False)
2025-06-29 11:43:56,641 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_ca_certs (recursive=False)
2025-06-29 11:43:56,642 - helpers.py[DEBUG]: Running config-ca-certs using lock (<FileLock using file '/var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_ca_certs'>)
2025-06-29 11:43:56,642 - cc_ca_certs.py[DEBUG]: Skipping module named ca-certs, no 'ca_certs' key in configuration
2025-06-29 11:43:56,642 - handlers.py[DEBUG]: finish: init-network/config-ca-certs: SUCCESS: config-ca-certs ran successfully
2025-06-29 11:43:56,642 - modules.py[DEBUG]: Running module rsyslog (<module 'cloudinit.config.cc_rsyslog' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_rsyslog.py'>) with frequency once-per-instance
2025-06-29 11:43:56,642 - handlers.py[DEBUG]: start: init-network/config-rsyslog: running config-rsyslog with frequency once-per-instance
2025-06-29 11:43:56,642 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_rsyslog - wb: [644] 25 bytes
2025-06-29 11:43:56,643 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_rsyslog (recursive=False)
2025-06-29 11:43:56,643 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_rsyslog (recursive=False)
2025-06-29 11:43:56,643 - util.py[DEBUG]: backgrounded Resizing took 0.018 seconds
2025-06-29 11:43:56,644 - helpers.py[DEBUG]: Running config-rsyslog using lock (<FileLock using file '/var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_rsyslog'>)
2025-06-29 11:43:56,644 - cc_rsyslog.py[DEBUG]: Skipping module named rsyslog, no 'rsyslog' key in configuration
2025-06-29 11:43:56,644 - handlers.py[DEBUG]: finish: init-network/config-rsyslog: SUCCESS: config-rsyslog ran successfully
2025-06-29 11:43:56,644 - modules.py[DEBUG]: Running module selinux (<module 'cloudinit.config.cc_selinux' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_selinux.py'>) with frequency once-per-instance
2025-06-29 11:43:56,644 - handlers.py[DEBUG]: start: init-network/config-selinux: running config-selinux with frequency once-per-instance
2025-06-29 11:43:56,644 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_selinux - wb: [644] 25 bytes
2025-06-29 11:43:56,645 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_selinux (recursive=False)
2025-06-29 11:43:56,645 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_selinux (recursive=False)
2025-06-29 11:43:56,646 - helpers.py[DEBUG]: Running config-selinux using lock (<FileLock using file '/var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_selinux'>)
2025-06-29 11:43:56,652 - cc_selinux.py[DEBUG]: Current SELinux state is enabled
2025-06-29 11:43:56,652 - cc_selinux.py[DEBUG]: Current SELinux mode is permissive
2025-06-29 11:43:56,652 - cc_selinux.py[DEBUG]: Configured SELinux mode is permissive
2025-06-29 11:43:56,652 - cc_selinux.py[DEBUG]: Current SELinux policy is targeted
2025-06-29 11:43:56,652 - cc_selinux.py[DEBUG]: No SELinux configuration, skipping
2025-06-29 11:43:56,652 - handlers.py[DEBUG]: finish: init-network/config-selinux: SUCCESS: config-selinux ran successfully
2025-06-29 11:43:56,652 - modules.py[DEBUG]: Running module users-groups (<module 'cloudinit.config.cc_users_groups' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_users_groups.py'>) with frequency once-per-instance
2025-06-29 11:43:56,653 - handlers.py[DEBUG]: start: init-network/config-users-groups: running config-users-groups with frequency once-per-instance
2025-06-29 11:43:56,653 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_users_groups - wb: [644] 23 bytes
2025-06-29 11:43:56,654 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_users_groups (recursive=False)
2025-06-29 11:43:56,655 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_users_groups (recursive=False)
2025-06-29 11:43:56,655 - helpers.py[DEBUG]: Running config-users-groups using lock (<FileLock using file '/var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_users_groups'>)
2025-06-29 11:43:56,656 - __init__.py[INFO]: User ec2-user already exists, skipping.
2025-06-29 11:43:56,656 - subp.py[DEBUG]: Running command ['passwd', '-l', 'ec2-user'] with allowed return codes [0] (shell=False, capture=True)
2025-06-29 11:43:56,706 - util.py[DEBUG]: Reading from /etc/sudoers (quiet=False)
2025-06-29 11:43:56,716 - util.py[DEBUG]: Read 4375 bytes from /etc/sudoers
2025-06-29 11:43:56,717 - util.py[DEBUG]: Restoring selinux mode for /etc/sudoers.d (recursive=False)
2025-06-29 11:43:56,718 - util.py[DEBUG]: Writing to /etc/sudoers.d/90-cloud-init-users - ab: [None] 59 bytes
2025-06-29 11:43:56,719 - util.py[DEBUG]: Restoring selinux mode for /etc/sudoers.d/90-cloud-init-users (recursive=False)
2025-06-29 11:43:56,720 - handlers.py[DEBUG]: finish: init-network/config-users-groups: SUCCESS: config-users-groups ran successfully
2025-06-29 11:43:56,720 - modules.py[DEBUG]: Running module ssh (<module 'cloudinit.config.cc_ssh' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_ssh.py'>) with frequency once-per-instance
2025-06-29 11:43:56,720 - handlers.py[DEBUG]: start: init-network/config-ssh: running config-ssh with frequency once-per-instance
2025-06-29 11:43:56,720 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_ssh - wb: [644] 25 bytes
2025-06-29 11:43:56,721 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_ssh (recursive=False)
2025-06-29 11:43:56,722 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_ssh (recursive=False)
2025-06-29 11:43:56,723 - helpers.py[DEBUG]: Running config-ssh using lock (<FileLock using file '/var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_ssh'>)
2025-06-29 11:43:56,723 - subp.py[DEBUG]: Running command ['ssh-keygen', '-t', 'ed25519', '-N', '', '-f', '/etc/ssh/ssh_host_ed25519_key'] with allowed return codes [0] (shell=False, capture=True)
2025-06-29 11:43:56,736 - util.py[DEBUG]: Restoring selinux mode for /etc/ssh (recursive=True)
2025-06-29 11:43:56,743 - subp.py[DEBUG]: Running command ['ssh-keygen', '-t', 'ecdsa', '-N', '', '-f', '/etc/ssh/ssh_host_ecdsa_key'] with allowed return codes [0] (shell=False, capture=True)
2025-06-29 11:43:56,751 - util.py[DEBUG]: Restoring selinux mode for /etc/ssh (recursive=True)
2025-06-29 11:43:56,756 - util.py[DEBUG]: Reading from /etc/ssh/ssh_host_ed25519_key.pub (quiet=False)
2025-06-29 11:43:56,756 - util.py[DEBUG]: Read 129 bytes from /etc/ssh/ssh_host_ed25519_key.pub
2025-06-29 11:43:56,756 - util.py[DEBUG]: Reading from /etc/ssh/ssh_host_ecdsa_key.pub (quiet=False)
2025-06-29 11:43:56,756 - util.py[DEBUG]: Read 209 bytes from /etc/ssh/ssh_host_ecdsa_key.pub
2025-06-29 11:43:56,757 - util.py[DEBUG]: Reading from /etc/ssh/sshd_config (quiet=False)
2025-06-29 11:43:56,757 - util.py[DEBUG]: Read 3854 bytes from /etc/ssh/sshd_config
2025-06-29 11:43:56,758 - util.py[DEBUG]: Changing the ownership of /home/<USER>/.ssh to 1000:1000
2025-06-29 11:43:56,759 - util.py[DEBUG]: Restoring selinux mode for /home/<USER>/.ssh (recursive=False)
2025-06-29 11:43:56,759 - util.py[DEBUG]: Writing to /home/<USER>/.ssh/authorized_keys - wb: [600] 0 bytes
2025-06-29 11:43:56,759 - util.py[DEBUG]: Restoring selinux mode for /home/<USER>/.ssh/authorized_keys (recursive=False)
2025-06-29 11:43:56,760 - util.py[DEBUG]: Restoring selinux mode for /home/<USER>/.ssh/authorized_keys (recursive=False)
2025-06-29 11:43:56,760 - util.py[DEBUG]: Changing the ownership of /home/<USER>/.ssh/authorized_keys to 1000:1000
2025-06-29 11:43:56,760 - util.py[DEBUG]: Reading from /home/<USER>/.ssh/authorized_keys (quiet=False)
2025-06-29 11:43:56,760 - util.py[DEBUG]: Read 0 bytes from /home/<USER>/.ssh/authorized_keys
2025-06-29 11:43:56,760 - util.py[DEBUG]: Writing to /home/<USER>/.ssh/authorized_keys - wb: [600] 88 bytes
2025-06-29 11:43:56,760 - util.py[DEBUG]: Restoring selinux mode for /home/<USER>/.ssh/authorized_keys (recursive=False)
2025-06-29 11:43:56,760 - util.py[DEBUG]: Restoring selinux mode for /home/<USER>/.ssh/authorized_keys (recursive=False)
2025-06-29 11:43:56,761 - util.py[DEBUG]: Restoring selinux mode for /home/<USER>/.ssh (recursive=True)
2025-06-29 11:43:56,762 - util.py[DEBUG]: Reading from /etc/ssh/sshd_config (quiet=False)
2025-06-29 11:43:56,762 - util.py[DEBUG]: Read 3854 bytes from /etc/ssh/sshd_config
2025-06-29 11:43:56,762 - util.py[DEBUG]: Restoring selinux mode for /root/.ssh (recursive=True)
2025-06-29 11:43:56,763 - util.py[DEBUG]: Writing to /root/.ssh/authorized_keys - wb: [600] 0 bytes
2025-06-29 11:43:56,764 - util.py[DEBUG]: Restoring selinux mode for /root/.ssh/authorized_keys (recursive=False)
2025-06-29 11:43:56,764 - util.py[DEBUG]: Restoring selinux mode for /root/.ssh/authorized_keys (recursive=False)
2025-06-29 11:43:56,764 - util.py[DEBUG]: Changing the ownership of /root/.ssh/authorized_keys to 0:0
2025-06-29 11:43:56,764 - util.py[DEBUG]: Reading from /root/.ssh/authorized_keys (quiet=False)
2025-06-29 11:43:56,764 - util.py[DEBUG]: Read 0 bytes from /root/.ssh/authorized_keys
2025-06-29 11:43:56,764 - util.py[DEBUG]: Writing to /root/.ssh/authorized_keys - wb: [600] 254 bytes
2025-06-29 11:43:56,764 - util.py[DEBUG]: Restoring selinux mode for /root/.ssh/authorized_keys (recursive=False)
2025-06-29 11:43:56,765 - util.py[DEBUG]: Restoring selinux mode for /root/.ssh/authorized_keys (recursive=False)
2025-06-29 11:43:56,765 - util.py[DEBUG]: Restoring selinux mode for /root/.ssh (recursive=True)
2025-06-29 11:43:56,766 - handlers.py[DEBUG]: finish: init-network/config-ssh: SUCCESS: config-ssh ran successfully
2025-06-29 11:43:56,766 - main.py[DEBUG]: Ran 17 modules with 0 failures
2025-06-29 11:43:56,766 - atomic_helper.py[DEBUG]: Atomically writing to file /var/lib/cloud/data/status.json (via temporary file /var/lib/cloud/data/tmpdta6vind) - w: [644] 486 bytes/chars
2025-06-29 11:43:56,767 - util.py[DEBUG]: Reading from /proc/uptime (quiet=False)
2025-06-29 11:43:56,767 - util.py[DEBUG]: Read 10 bytes from /proc/uptime
2025-06-29 11:43:56,767 - util.py[DEBUG]: cloud-init mode 'init' took 0.937 seconds (0.94)
2025-06-29 11:43:56,767 - handlers.py[DEBUG]: finish: init-network: SUCCESS: searching for network datasources
2025-06-29 11:43:57,392 - util.py[DEBUG]: Cloud-init v. 22.2.2 running 'modules:config' at Sun, 29 Jun 2025 11:43:57 +0000. Up 7.93 seconds.
2025-06-29 11:43:57,396 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-29 11:43:57,396 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-29 11:43:57,398 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-29 11:43:57,398 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-29 11:43:57,401 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-29 11:43:57,401 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-29 11:43:57,403 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-29 11:43:57,403 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-29 11:43:57,406 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-29 11:43:57,406 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-29 11:43:57,408 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-29 11:43:57,408 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-29 11:43:57,412 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-29 11:43:57,412 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-29 11:43:57,414 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-29 11:43:57,415 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-29 11:43:57,417 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-29 11:43:57,417 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-29 11:43:57,419 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-29 11:43:57,420 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-29 11:43:57,421 - stages.py[DEBUG]: Using distro class <class 'cloudinit.distros.amazon.Distro'>
2025-06-29 11:43:57,422 - modules.py[INFO]: Skipping modules 'ssh-import-id' because they are not verified on distro 'amazon'.  To run anyway, add them to 'unverified_modules' in config.
2025-06-29 11:43:57,422 - modules.py[DEBUG]: Running module keyboard (<module 'cloudinit.config.cc_keyboard' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_keyboard.py'>) with frequency once-per-instance
2025-06-29 11:43:57,423 - handlers.py[DEBUG]: start: modules-config/config-keyboard: running config-keyboard with frequency once-per-instance
2025-06-29 11:43:57,423 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_keyboard - wb: [644] 24 bytes
2025-06-29 11:43:57,425 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_keyboard (recursive=False)
2025-06-29 11:43:57,427 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_keyboard (recursive=False)
2025-06-29 11:43:57,428 - helpers.py[DEBUG]: Running config-keyboard using lock (<FileLock using file '/var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_keyboard'>)
2025-06-29 11:43:57,428 - cc_keyboard.py[DEBUG]: Skipping module named keyboard, no 'keyboard' section found
2025-06-29 11:43:57,428 - handlers.py[DEBUG]: finish: modules-config/config-keyboard: SUCCESS: config-keyboard ran successfully
2025-06-29 11:43:57,428 - modules.py[DEBUG]: Running module locale (<module 'cloudinit.config.cc_locale' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_locale.py'>) with frequency once-per-instance
2025-06-29 11:43:57,428 - handlers.py[DEBUG]: start: modules-config/config-locale: running config-locale with frequency once-per-instance
2025-06-29 11:43:57,428 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_locale - wb: [644] 25 bytes
2025-06-29 11:43:57,429 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_locale (recursive=False)
2025-06-29 11:43:57,429 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_locale (recursive=False)
2025-06-29 11:43:57,430 - helpers.py[DEBUG]: Running config-locale using lock (<FileLock using file '/var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_locale'>)
2025-06-29 11:43:57,430 - util.py[DEBUG]: Reading from /etc/locale.conf (quiet=False)
2025-06-29 11:43:57,430 - util.py[DEBUG]: Read 17 bytes from /etc/locale.conf
2025-06-29 11:43:57,430 - cc_locale.py[DEBUG]: Setting locale to en_US.UTF-8
2025-06-29 11:43:57,430 - util.py[DEBUG]: Reading from /etc/locale.conf (quiet=False)
2025-06-29 11:43:57,430 - util.py[DEBUG]: Read 17 bytes from /etc/locale.conf
2025-06-29 11:43:57,430 - util.py[DEBUG]: Writing to /etc/locale.conf - wb: [644] 17 bytes
2025-06-29 11:43:57,432 - util.py[DEBUG]: Restoring selinux mode for /etc/locale.conf (recursive=False)
2025-06-29 11:43:57,434 - util.py[DEBUG]: Restoring selinux mode for /etc/locale.conf (recursive=False)
2025-06-29 11:43:57,434 - handlers.py[DEBUG]: finish: modules-config/config-locale: SUCCESS: config-locale ran successfully
2025-06-29 11:43:57,434 - modules.py[DEBUG]: Running module set-passwords (<module 'cloudinit.config.cc_set_passwords' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_set_passwords.py'>) with frequency once-per-instance
2025-06-29 11:43:57,435 - handlers.py[DEBUG]: start: modules-config/config-set-passwords: running config-set-passwords with frequency once-per-instance
2025-06-29 11:43:57,435 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_set_passwords - wb: [644] 24 bytes
2025-06-29 11:43:57,435 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_set_passwords (recursive=False)
2025-06-29 11:43:57,436 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_set_passwords (recursive=False)
2025-06-29 11:43:57,436 - helpers.py[DEBUG]: Running config-set-passwords using lock (<FileLock using file '/var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_set_passwords'>)
2025-06-29 11:43:57,436 - subp.py[DEBUG]: Running command ['systemctl', 'status', 'sshd'] with allowed return codes [0] (shell=False, capture=True)
2025-06-29 11:43:57,455 - util.py[DEBUG]: Reading from /etc/ssh/sshd_config (quiet=False)
2025-06-29 11:43:57,455 - util.py[DEBUG]: Read 3854 bytes from /etc/ssh/sshd_config
2025-06-29 11:43:57,455 - ssh_util.py[DEBUG]: line 65: option PasswordAuthentication already set to no
2025-06-29 11:43:57,455 - cc_set_passwords.py[DEBUG]: No need to restart SSH service, PasswordAuthentication not updated.
2025-06-29 11:43:57,456 - handlers.py[DEBUG]: finish: modules-config/config-set-passwords: SUCCESS: config-set-passwords ran successfully
2025-06-29 11:43:57,456 - modules.py[DEBUG]: Running module yum-variables (<module 'cloudinit.config.cc_yum_variables' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_yum_variables.py'>) with frequency once-per-instance
2025-06-29 11:43:57,456 - handlers.py[DEBUG]: start: modules-config/config-yum-variables: running config-yum-variables with frequency once-per-instance
2025-06-29 11:43:57,456 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_yum_variables - wb: [644] 24 bytes
2025-06-29 11:43:57,457 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_yum_variables (recursive=False)
2025-06-29 11:43:57,458 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_yum_variables (recursive=False)
2025-06-29 11:43:57,458 - helpers.py[DEBUG]: Running config-yum-variables using lock (<FileLock using file '/var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_yum_variables'>)
2025-06-29 11:43:57,458 - cc_yum_variables.py[INFO]: handling yum-variables
2025-06-29 11:43:57,458 - cc_yum_variables.py[INFO]: cfg = {'cloud_final_modules': [['scripts-user', 'always']], 'repo_releasever': 2023.7, 'repo_upgrade': 'none', 'power_state': {'delay': 'now', 'mode': 'reboot', 'message': 'Rebooting machine to apply SELinux kernel commandline setting', 'condition': 'test -f /run/cloud-init-selinux-reboot'}, 'write_metadata': [{'path': '/etc/dnf/vars/awsregion', 'data': [{'identity': 'region'}, 'default']}, {'path': '/etc/dnf/vars/awsdomain', 'data': [{'metadata': 'services/domain'}, 'amazonaws.com']}], '_log': ['[loggers]\nkeys=root,cloudinit\n\n[handlers]\nkeys=consoleHandler,cloudLogHandler\n\n[formatters]\nkeys=simpleFormatter,arg0Formatter\n\n[logger_root]\nlevel=DEBUG\nhandlers=consoleHandler,cloudLogHandler\n\n[logger_cloudinit]\nlevel=DEBUG\nqualname=cloudinit\nhandlers=\npropagate=1\n\n[handler_consoleHandler]\nclass=StreamHandler\nlevel=WARNING\nformatter=arg0Formatter\nargs=(sys.stderr,)\n\n[formatter_arg0Formatter]\nformat=%(asctime)s - %(filename)s[%(levelname)s]: %(message)s\n\n[formatter_simpleFormatter]\nformat=[CLOUDINIT] %(filename)s[%(levelname)s]: %(message)s\n', "[handler_cloudLogHandler]\nclass=FileHandler\nlevel=DEBUG\nformatter=arg0Formatter\nargs=('/var/log/cloud-init.log', 'a', 'UTF-8')\n", '[handler_cloudLogHandler]\nclass=handlers.SysLogHandler\nlevel=DEBUG\nformatter=simpleFormatter\nargs=("/dev/log", handlers.SysLogHandler.LOG_USER)\n'], 'log_cfgs': [['[loggers]\nkeys=root,cloudinit\n\n[handlers]\nkeys=consoleHandler,cloudLogHandler\n\n[formatters]\nkeys=simpleFormatter,arg0Formatter\n\n[logger_root]\nlevel=DEBUG\nhandlers=consoleHandler,cloudLogHandler\n\n[logger_cloudinit]\nlevel=DEBUG\nqualname=cloudinit\nhandlers=\npropagate=1\n\n[handler_consoleHandler]\nclass=StreamHandler\nlevel=WARNING\nformatter=arg0Formatter\nargs=(sys.stderr,)\n\n[formatter_arg0Formatter]\nformat=%(asctime)s - %(filename)s[%(levelname)s]: %(message)s\n\n[formatter_simpleFormatter]\nformat=[CLOUDINIT] %(filename)s[%(levelname)s]: %(message)s\n', "[handler_cloudLogHandler]\nclass=FileHandler\nlevel=DEBUG\nformatter=arg0Formatter\nargs=('/var/log/cloud-init.log', 'a', 'UTF-8')\n"]], 'output': {'all': '| tee -a /var/log/cloud-init-output.log'}, 'network': {'config': 'disabled'}, 'datasource_list': ['Ec2', 'None'], 'datasource': {'Ec2': {'metadata_urls': ['http://***************:80', 'http://[fd00:ec2::254]:80'], 'apply_full_imds_network_config': False}}, 'ssh_genkeytypes': ['ed25519', 'ecdsa'], 'users': ['default'], 'disable_root': True, 'mount_default_fields': [None, None, 'auto', 'defaults,nofail', '0', '2'], 'resize_rootfs': 'noblock', 'resize_rootfs_tmp': '/dev', 'ssh_pwauth': False, 'preserve_hostname': False, 'cloud_init_modules': ['migrator', 'seed_random', 'bootcmd', 'write-files', 'write-metadata', 'growpart', 'resizefs', 'disk_setup', 'mounts', 'set_hostname', 'update_hostname', 'update_etc_hosts', 'ca-certs', 'rsyslog', 'selinux', 'users-groups', 'ssh'], 'cloud_config_modules': ['ssh-import-id', 'keyboard', 'locale', 'set-passwords', 'yum-variables', 'yum-add-repo', 'ntp', 'timezone', 'disable-ec2-metadata', 'runcmd'], 'def_log_file': '/var/log/cloud-init.log', 'syslog_fix_perms': ['syslog:adm', 'root:adm', 'root:wheel', 'root:root'], 'vendor_data': {'enabled': True, 'prefix': []}, 'vendor_data2': {'enabled': True, 'prefix': []}}
2025-06-29 11:43:57,458 - cc_yum_variables.py[INFO]: No yum vars to delete
2025-06-29 11:43:57,458 - cc_yum_variables.py[INFO]: No yum vars to set
2025-06-29 11:43:57,458 - handlers.py[DEBUG]: finish: modules-config/config-yum-variables: SUCCESS: config-yum-variables ran successfully
2025-06-29 11:43:57,459 - modules.py[DEBUG]: Running module yum-add-repo (<module 'cloudinit.config.cc_yum_add_repo' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_yum_add_repo.py'>) with frequency once-per-instance
2025-06-29 11:43:57,459 - handlers.py[DEBUG]: start: modules-config/config-yum-add-repo: running config-yum-add-repo with frequency once-per-instance
2025-06-29 11:43:57,459 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_yum_add_repo - wb: [644] 24 bytes
2025-06-29 11:43:57,459 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_yum_add_repo (recursive=False)
2025-06-29 11:43:57,460 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_yum_add_repo (recursive=False)
2025-06-29 11:43:57,460 - helpers.py[DEBUG]: Running config-yum-add-repo using lock (<FileLock using file '/var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_yum_add_repo'>)
2025-06-29 11:43:57,460 - cc_yum_add_repo.py[DEBUG]: Skipping module named yum-add-repo, no 'yum_repos' configuration found
2025-06-29 11:43:57,460 - handlers.py[DEBUG]: finish: modules-config/config-yum-add-repo: SUCCESS: config-yum-add-repo ran successfully
2025-06-29 11:43:57,460 - modules.py[DEBUG]: Running module ntp (<module 'cloudinit.config.cc_ntp' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_ntp.py'>) with frequency once-per-instance
2025-06-29 11:43:57,461 - handlers.py[DEBUG]: start: modules-config/config-ntp: running config-ntp with frequency once-per-instance
2025-06-29 11:43:57,461 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_ntp - wb: [644] 25 bytes
2025-06-29 11:43:57,461 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_ntp (recursive=False)
2025-06-29 11:43:57,462 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_ntp (recursive=False)
2025-06-29 11:43:57,462 - helpers.py[DEBUG]: Running config-ntp using lock (<FileLock using file '/var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_ntp'>)
2025-06-29 11:43:57,462 - cc_ntp.py[DEBUG]: Skipping module named ntp, not present or disabled by cfg
2025-06-29 11:43:57,462 - handlers.py[DEBUG]: finish: modules-config/config-ntp: SUCCESS: config-ntp ran successfully
2025-06-29 11:43:57,462 - modules.py[DEBUG]: Running module timezone (<module 'cloudinit.config.cc_timezone' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_timezone.py'>) with frequency once-per-instance
2025-06-29 11:43:57,463 - handlers.py[DEBUG]: start: modules-config/config-timezone: running config-timezone with frequency once-per-instance
2025-06-29 11:43:57,463 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_timezone - wb: [644] 25 bytes
2025-06-29 11:43:57,463 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_timezone (recursive=False)
2025-06-29 11:43:57,464 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_timezone (recursive=False)
2025-06-29 11:43:57,464 - helpers.py[DEBUG]: Running config-timezone using lock (<FileLock using file '/var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_timezone'>)
2025-06-29 11:43:57,464 - cc_timezone.py[DEBUG]: Skipping module named timezone, no 'timezone' specified
2025-06-29 11:43:57,464 - handlers.py[DEBUG]: finish: modules-config/config-timezone: SUCCESS: config-timezone ran successfully
2025-06-29 11:43:57,464 - modules.py[DEBUG]: Running module disable-ec2-metadata (<module 'cloudinit.config.cc_disable_ec2_metadata' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_disable_ec2_metadata.py'>) with frequency always
2025-06-29 11:43:57,464 - handlers.py[DEBUG]: start: modules-config/config-disable-ec2-metadata: running config-disable-ec2-metadata with frequency always
2025-06-29 11:43:57,464 - helpers.py[DEBUG]: Running config-disable-ec2-metadata using lock (<cloudinit.helpers.DummyLock object at 0x7fbdadf37220>)
2025-06-29 11:43:57,464 - cc_disable_ec2_metadata.py[DEBUG]: Skipping module named disable-ec2-metadata, disabling the ec2 route not enabled
2025-06-29 11:43:57,465 - handlers.py[DEBUG]: finish: modules-config/config-disable-ec2-metadata: SUCCESS: config-disable-ec2-metadata ran successfully
2025-06-29 11:43:57,465 - modules.py[DEBUG]: Running module runcmd (<module 'cloudinit.config.cc_runcmd' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_runcmd.py'>) with frequency once-per-instance
2025-06-29 11:43:57,465 - handlers.py[DEBUG]: start: modules-config/config-runcmd: running config-runcmd with frequency once-per-instance
2025-06-29 11:43:57,465 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_runcmd - wb: [644] 25 bytes
2025-06-29 11:43:57,465 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_runcmd (recursive=False)
2025-06-29 11:43:57,466 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_runcmd (recursive=False)
2025-06-29 11:43:57,466 - helpers.py[DEBUG]: Running config-runcmd using lock (<FileLock using file '/var/lib/cloud/instances/i-06cc7bc2ee1b17d55/sem/config_runcmd'>)
2025-06-29 11:43:57,466 - cc_runcmd.py[DEBUG]: Skipping module named runcmd, no 'runcmd' key in configuration
2025-06-29 11:43:57,466 - handlers.py[DEBUG]: finish: modules-config/config-runcmd: SUCCESS: config-runcmd ran successfully
2025-06-29 11:43:57,466 - main.py[DEBUG]: Ran 9 modules with 0 failures
2025-06-29 11:43:57,467 - atomic_helper.py[DEBUG]: Atomically writing to file /var/lib/cloud/data/status.json (via temporary file /var/lib/cloud/data/tmpxwri0yxn) - w: [644] 514 bytes/chars
2025-06-29 11:43:57,467 - util.py[DEBUG]: Reading from /proc/uptime (quiet=False)
2025-06-29 11:43:57,467 - util.py[DEBUG]: Read 10 bytes from /proc/uptime
2025-06-29 11:43:57,467 - util.py[DEBUG]: cloud-init mode 'modules' took 0.170 seconds (0.17)
2025-06-29 11:43:57,467 - handlers.py[DEBUG]: finish: modules-config: SUCCESS: running modules for config
2025-06-29 11:43:57,978 - util.py[DEBUG]: Cloud-init v. 22.2.2 running 'modules:final' at Sun, 29 Jun 2025 11:43:57 +0000. Up 8.54 seconds.
2025-06-29 11:43:57,980 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-29 11:43:57,981 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-29 11:43:57,983 - stages.py[DEBUG]: Using distro class <class 'cloudinit.distros.amazon.Distro'>
2025-06-29 11:43:57,983 - modules.py[DEBUG]: Running module scripts-user (<module 'cloudinit.config.cc_scripts_user' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_scripts_user.py'>) with frequency always
2025-06-29 11:43:57,984 - handlers.py[DEBUG]: start: modules-final/config-scripts-user: running config-scripts-user with frequency always
2025-06-29 11:43:57,984 - helpers.py[DEBUG]: Running config-scripts-user using lock (<cloudinit.helpers.DummyLock object at 0x7fcac54a1f70>)
2025-06-29 11:43:57,984 - subp.py[DEBUG]: Running command ['/var/lib/cloud/instance/scripts/user-data.txt'] with allowed return codes [0] (shell=False, capture=False)
2025-06-29 11:44:02,535 - handlers.py[DEBUG]: finish: modules-final/config-scripts-user: SUCCESS: config-scripts-user ran successfully
2025-06-29 11:44:02,535 - main.py[DEBUG]: Ran 1 modules with 0 failures
2025-06-29 11:44:02,536 - atomic_helper.py[DEBUG]: Atomically writing to file /var/lib/cloud/data/status.json (via temporary file /var/lib/cloud/data/tmpdh3dbn94) - w: [644] 541 bytes/chars
2025-06-29 11:44:02,536 - atomic_helper.py[DEBUG]: Atomically writing to file /var/lib/cloud/data/result.json (via temporary file /var/lib/cloud/data/tmpyjvy0pb4) - w: [644] 64 bytes/chars
2025-06-29 11:44:02,536 - util.py[DEBUG]: Creating symbolic link from '/run/cloud-init/result.json' => '../../var/lib/cloud/data/result.json'
2025-06-29 11:44:02,536 - util.py[DEBUG]: Reading from /proc/uptime (quiet=False)
2025-06-29 11:44:02,536 - util.py[DEBUG]: Read 12 bytes from /proc/uptime
2025-06-29 11:44:02,536 - util.py[DEBUG]: cloud-init mode 'modules' took 4.626 seconds (4.63)
2025-06-29 11:44:02,536 - handlers.py[DEBUG]: finish: modules-final: SUCCESS: running modules for final
