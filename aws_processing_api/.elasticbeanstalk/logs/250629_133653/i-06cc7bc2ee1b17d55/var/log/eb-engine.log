2025/06/29 11:44:00.182816 [INFO] Starting...
2025/06/29 11:44:00.182863 [INFO] Starting EBPlatform-PlatformEngine
2025/06/29 11:44:00.182962 [INFO] reading event message file
2025/06/29 11:44:00.183384 [INFO] Engine received EB command userdata-exec

2025/06/29 11:44:00.189145 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 -r AWSEBAutoScalingGroup --region us-west-2
2025/06/29 11:44:00.492596 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 -r AWSEBBeanstalkMetadata --region us-west-2
2025/06/29 11:44:00.858996 [INFO] This is a workflow controlled instance.
2025/06/29 11:44:00.859082 [INFO] Engine command: (env-launch)

2025/06/29 11:44:00.862706 [INFO] Executing instruction: SyncClock
2025/06/29 11:44:00.862719 [INFO] Starting SyncClock
2025/06/29 11:44:00.862729 [INFO] Running command: /usr/bin/chronyc tracking
2025/06/29 11:44:00.868800 [INFO] Reference ID    : 00000000 ()
Stratum         : 0
Ref time (UTC)  : Thu Jan 01 00:00:00 1970
System time     : 0.000000000 seconds slow of NTP time
Last offset     : +0.000000000 seconds
RMS offset      : 0.000000000 seconds
Frequency       : 4.195 ppm slow
Residual freq   : +0.000 ppm
Skew            : 0.000 ppm
Root delay      : 1.000000000 seconds
Root dispersion : 1.000000000 seconds
Update interval : 0.0 seconds
Leap status     : Not synchronised

2025/06/29 11:44:00.868822 [INFO] Running command: /usr/bin/chronyc -a makestep
2025/06/29 11:44:00.878475 [INFO] 200 OK

2025/06/29 11:44:00.878589 [INFO] Skipping Configure OS
2025/06/29 11:44:00.878596 [INFO] Skipping LockRepo
2025/06/29 11:44:00.878600 [INFO] Skipping GenerateEBBanner
2025/06/29 11:44:00.878604 [INFO] Skipping Install Process Manager
2025/06/29 11:44:00.878608 [INFO] Skipping install syslog
2025/06/29 11:44:00.878612 [INFO] Skipping install cron
2025/06/29 11:44:00.878616 [INFO] Skipping install proxy
2025/06/29 11:44:00.878619 [INFO] Skipping installhealthd
2025/06/29 11:44:00.878623 [INFO] Skipping Install Log Streaming Manager
2025/06/29 11:44:00.878627 [INFO] Skipping install X-Ray
2025/06/29 11:44:00.878631 [INFO] Skipping install Third Party License
2025/06/29 11:44:00.878634 [INFO] Skipping install httpd
2025/06/29 11:44:00.878639 [INFO] Instance has NOT been bootstrapped
2025/06/29 11:44:00.878641 [INFO] Executing instruction: installSqsd
2025/06/29 11:44:00.878644 [INFO] This is a web server environment instance, skip install sqsd daemon ...
2025/06/29 11:44:00.878647 [INFO] Instance has NOT been bootstrapped
2025/06/29 11:44:00.878649 [INFO] Executing instruction: bootstraphealthd
2025/06/29 11:44:00.878652 [INFO] this is an enhanced health env ...
2025/06/29 11:44:00.878669 [INFO] bootstrap healthd....
2025/06/29 11:44:00.878681 [INFO] Running command: /usr/bin/id -u healthd || /usr/sbin/useradd --user-group healthd -s /sbin/nologin --create-home
2025/06/29 11:44:01.058656 [INFO] /usr/bin/id: ‘healthd’: no such user

2025/06/29 11:44:01.059068 [INFO] bootstrap healthd....
2025/06/29 11:44:01.059085 [INFO] Running command: /usr/bin/id -u healthd || /usr/sbin/useradd --user-group healthd -s /sbin/nologin --create-home
2025/06/29 11:44:01.062665 [INFO] 1001

2025/06/29 11:44:01.063007 [INFO] configure bundle log for healthd...
2025/06/29 11:44:01.063077 [INFO] Executing instruction: GetSetupProxyLog
2025/06/29 11:44:01.063151 [INFO] Skipping Install yum packages
2025/06/29 11:44:01.063157 [INFO] Skipping Install Python Bundle
2025/06/29 11:44:01.063163 [INFO] Skipping Configure Python site-packages
2025/06/29 11:44:01.063167 [INFO] Skipping Install Python Modules
2025/06/29 11:44:01.063171 [INFO] Skipping MarkBaked
2025/06/29 11:44:01.063175 [INFO] Instance has NOT been bootstrapped
2025/06/29 11:44:01.063177 [INFO] Executing instruction: TuneSystemSettings
2025/06/29 11:44:01.063179 [INFO] Starting TuneSystemSettings
2025/06/29 11:44:01.063182 [INFO] Instance has NOT been bootstrapped
2025/06/29 11:44:01.064497 [INFO] Executing instruction: GetSetupLogRotate
2025/06/29 11:44:01.064503 [INFO] Initialize LogRotate files and directories
2025/06/29 11:44:01.084253 [INFO] Instance has NOT been bootstrapped
2025/06/29 11:44:01.084273 [INFO] Executing instruction: BootstrapCFNHup
2025/06/29 11:44:01.084277 [INFO] Bootstrap cfn-hup
2025/06/29 11:44:01.088721 [INFO] Copying file /opt/elasticbeanstalk/config/private/aws-eb-command-handler.conf to /etc/cfn/hooks.d/aws-eb-command-handler.conf
2025/06/29 11:44:01.091187 [INFO] Executing instruction: StartCFNHup
2025/06/29 11:44:01.091201 [INFO] Start cfn-hup
2025/06/29 11:44:01.091468 [INFO] Running command: systemctl show -p PartOf cfn-hup.service
2025/06/29 11:44:01.121662 [INFO] cfn-hup is not registered with EB yet, registering it now
2025/06/29 11:44:01.121700 [INFO] Running command: systemctl show -p PartOf cfn-hup.service
2025/06/29 11:44:01.140132 [INFO] Running command: systemctl daemon-reload
2025/06/29 11:44:01.389020 [INFO] Running command: systemctl reset-failed
2025/06/29 11:44:01.402026 [INFO] Running command: systemctl is-enabled aws-eb.target
2025/06/29 11:44:01.412223 [INFO] Running command: systemctl enable aws-eb.target
2025/06/29 11:44:01.706147 [INFO] Running command: systemctl start aws-eb.target
2025/06/29 11:44:01.715709 [INFO] Running command: systemctl enable cfn-hup.service
2025/06/29 11:44:01.964592 [INFO] Created symlink /etc/systemd/system/multi-user.target.wants/cfn-hup.service → /etc/systemd/system/cfn-hup.service.

2025/06/29 11:44:01.964627 [INFO] Running command: systemctl is-active cfn-hup.service
2025/06/29 11:44:01.973690 [INFO] cfn-hup process is not running, starting it now
2025/06/29 11:44:01.973721 [INFO] Running command: systemctl show -p PartOf cfn-hup.service
2025/06/29 11:44:01.984228 [INFO] Running command: systemctl is-active cfn-hup.service
2025/06/29 11:44:01.992600 [INFO] Running command: systemctl start cfn-hup.service
2025/06/29 11:44:02.058000 [INFO] Instance has NOT been bootstrapped
2025/06/29 11:44:02.058024 [INFO] Executing instruction: SetupPublishLogCronjob
2025/06/29 11:44:02.058028 [INFO] Setup publish logs cron job...
2025/06/29 11:44:02.058032 [INFO] Copying file /opt/elasticbeanstalk/config/private/logtasks/cron/publishlogs to /etc/cron.d/publishlogs
2025/06/29 11:44:02.060479 [INFO] Instance has NOT been bootstrapped
2025/06/29 11:44:02.060503 [INFO] Executing instruction: MarkBootstrapped
2025/06/29 11:44:02.060507 [INFO] Starting MarkBootstrapped
2025/06/29 11:44:02.060513 [INFO] Instance has NOT been bootstrapped
2025/06/29 11:44:02.060605 [INFO] Marked instance as Bootstrapped
2025/06/29 11:44:02.060610 [INFO] Executing instruction: Save CFN Stack Info
2025/06/29 11:44:02.060676 [INFO] Executing cleanup logic
2025/06/29 11:44:02.060687 [INFO] Platform Engine finished execution on command: env-launch

2025/06/29 11:46:03.697101 [INFO] Starting...
2025/06/29 11:46:03.697152 [INFO] Starting EBPlatform-PlatformEngine
2025/06/29 11:46:03.697189 [INFO] reading event message file
2025/06/29 11:46:03.697532 [INFO] Engine received EB command cfn-hup-exec

2025/06/29 11:46:03.764061 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 -r AWSEBAutoScalingGroup --region us-west-2
2025/06/29 11:46:04.064085 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 -r AWSEBBeanstalkMetadata --region us-west-2
2025/06/29 11:46:04.356271 [INFO] checking whether command app-deploy is applicable to this instance...
2025/06/29 11:46:04.356284 [INFO] this command is applicable to the instance, thus instance should execute command
2025/06/29 11:46:04.356287 [INFO] Engine command: (app-deploy)

2025/06/29 11:46:04.356290 [INFO] Downloading EB Application...
2025/06/29 11:46:04.356292 [INFO] Region: us-west-2
2025/06/29 11:46:04.356294 [INFO] envID: e-dptrdke822
2025/06/29 11:46:04.356301 [INFO] envBucket: elasticbeanstalk-us-west-2-************
2025/06/29 11:46:04.356303 [INFO] accountID: ************
2025/06/29 11:46:04.356305 [INFO] Using manifest file name from command request
2025/06/29 11:46:04.356310 [INFO] Unable to get version manifest file.
2025/06/29 11:46:04.356312 [INFO] Downloading latest manifest available.
2025/06/29 11:46:04.356314 [INFO] Download latest app version manifest
2025/06/29 11:46:04.356440 [INFO] resources/environments/e-dptrdke822/_runtime/versions/manifest
2025/06/29 11:46:04.395202 [INFO] latestManifest key *: resources/environments/e-dptrdke822/_runtime/versions/manifest_1751197389720

2025/06/29 11:46:04.395437 [INFO] Downloading: bucket: elasticbeanstalk-us-west-2-************, object: resources/environments/e-dptrdke822/_runtime/versions/manifest_1751197389720, expected bucket owner: ************
2025/06/29 11:46:04.436479 [INFO] Download successful157bytes downloaded
2025/06/29 11:46:04.436580 [INFO] Trying to read and parse version manifest...
2025/06/29 11:46:04.436671 [INFO] Downloading: bucket: elasticbeanstalk-us-west-2-************, object: resources/environments/e-dptrdke822/_runtime/_versions/winmemory-processing-api/app-250629_124259983035, expected bucket owner: ************
2025/06/29 11:46:04.482665 [INFO] Download successful2702bytes downloaded
2025/06/29 11:46:04.482928 [INFO] Executing instruction: ElectLeader
2025/06/29 11:46:04.482935 [INFO] Running leader election for instance i-06cc7bc2ee1b17d55...
2025/06/29 11:46:04.482937 [INFO] Calling the cfn-elect-cmd-leader to elect the command leader.
2025/06/29 11:46:04.482948 [INFO] Running command: /opt/aws/bin/cfn-elect-cmd-leader --stack arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 --command-name ElasticBeanstalkCommand-AWSEBAutoScalingGroup --invocation-id 79cc5f1c-7974-4461-9812-e42eaf9aaf3f --listener-id i-06cc7bc2ee1b17d55 --region us-west-2
2025/06/29 11:46:04.792753 [INFO] Instance is Leader.
2025/06/29 11:46:04.792813 [INFO] Executing instruction: stopSqsd
2025/06/29 11:46:04.792818 [INFO] This is a web server environment instance, skip stop sqsd daemon ...
2025/06/29 11:46:04.792822 [INFO] Executing instruction: PreBuildEbExtension
2025/06/29 11:46:04.792825 [INFO] Starting executing the config set Infra-EmbeddedPreBuild.
2025/06/29 11:46:04.792837 [INFO] Running command: /opt/aws/bin/cfn-init -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 -r AWSEBAutoScalingGroup --region us-west-2 --configsets Infra-EmbeddedPreBuild
2025/06/29 11:46:22.660031 [INFO] Finished executing the config set Infra-EmbeddedPreBuild.

2025/06/29 11:46:22.660737 [INFO] Executing instruction: StageApplication
2025/06/29 11:46:22.660747 [INFO] Recreating /var/app/staging/
2025/06/29 11:46:22.662396 [INFO] extracting /opt/elasticbeanstalk/deployment/app_source_bundle to /var/app/staging/
2025/06/29 11:46:22.662426 [INFO] Running command: /usr/bin/unzip -q -o /opt/elasticbeanstalk/deployment/app_source_bundle -d /var/app/staging/
2025/06/29 11:46:22.668624 [INFO] finished extracting /opt/elasticbeanstalk/deployment/app_source_bundle to /var/app/staging/ successfully
2025/06/29 11:46:22.669291 [INFO] Executing instruction: RunAppDeployPreBuildHooks
2025/06/29 11:46:22.669412 [INFO] Executing platform hooks in .platform/hooks/prebuild/
2025/06/29 11:46:22.669430 [INFO] The dir .platform/hooks/prebuild/ does not exist
2025/06/29 11:46:22.669434 [INFO] Finished running scripts in /var/app/staging/.platform/hooks/prebuild
2025/06/29 11:46:22.669439 [INFO] Executing instruction: InstallDependency
2025/06/29 11:46:22.669445 [INFO] checking dependencies file
2025/06/29 11:46:22.669456 [INFO] Installing dependencies with requirements.txt by using Pip
2025/06/29 11:46:22.669466 [INFO] Running command: /var/app/venv/staging-LQM1lest/bin/pip install -r requirements.txt
2025/06/29 11:46:30.202846 [INFO] Collecting flask
  Downloading flask-3.1.1-py3-none-any.whl (103 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 103.3/103.3 kB 6.6 MB/s eta 0:00:00
Collecting opencv-python
  Downloading opencv_python-*********-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (63.0 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 63.0/63.0 MB 37.0 MB/s eta 0:00:00
Collecting numpy
  Downloading numpy-2.3.1-cp311-cp311-manylinux_2_28_x86_64.whl (16.9 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 16.9/16.9 MB 107.5 MB/s eta 0:00:00
Collecting flask-cors
  Downloading flask_cors-6.0.1-py3-none-any.whl (13 kB)
Collecting blinker>=1.9.0
  Downloading blinker-1.9.0-py3-none-any.whl (8.5 kB)
Collecting click>=8.1.3
  Downloading click-8.2.1-py3-none-any.whl (102 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 102.2/102.2 kB 32.7 MB/s eta 0:00:00
Collecting itsdangerous>=2.2.0
  Downloading itsdangerous-2.2.0-py3-none-any.whl (16 kB)
Collecting jinja2>=3.1.2
  Downloading jinja2-3.1.6-py3-none-any.whl (134 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 134.9/134.9 kB 42.3 MB/s eta 0:00:00
Collecting markupsafe>=2.1.1
  Downloading MarkupSafe-3.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (23 kB)
Collecting werkzeug>=3.1.0
  Downloading werkzeug-3.1.3-py3-none-any.whl (224 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 224.5/224.5 kB 62.7 MB/s eta 0:00:00
Installing collected packages: numpy, markupsafe, itsdangerous, click, blinker, werkzeug, opencv-python, jinja2, flask, flask-cors
Successfully installed blinker-1.9.0 click-8.2.1 flask-3.1.1 flask-cors-6.0.1 itsdangerous-2.2.0 jinja2-3.1.6 markupsafe-3.0.2 numpy-2.3.1 opencv-python-********* werkzeug-3.1.3

2025/06/29 11:46:30.203640 [INFO] 
[notice] A new release of pip available: 22.3.1 -> 25.1.1
[notice] To update, run: python3.11 -m pip install --upgrade pip

2025/06/29 11:46:30.203650 [INFO] Executing instruction: check Procfile
2025/06/29 11:46:30.203700 [INFO] detected Procfile in application source bundle ...
2025/06/29 11:46:30.203708 [INFO] Executing instruction: configure X-Ray
2025/06/29 11:46:30.203712 [INFO] X-Ray is not enabled.
2025/06/29 11:46:30.203715 [INFO] Executing instruction: configure proxy server
2025/06/29 11:46:30.203731 [INFO] Recreating /var/proxy/staging/nginx
2025/06/29 11:46:30.208569 [INFO] Running command: cp -rp /var/app/staging/.platform/nginx/. /var/proxy/staging/nginx
2025/06/29 11:46:30.221170 [INFO] Executing instruction: configure healthd specific proxy conf
2025/06/29 11:46:30.223712 [INFO] Running command: systemctl show -p PartOf healthd.service
2025/06/29 11:46:30.239194 [INFO] Running command: systemctl daemon-reload
2025/06/29 11:46:30.526387 [INFO] Running command: systemctl reset-failed
2025/06/29 11:46:30.535788 [INFO] Running command: systemctl is-enabled aws-eb.target
2025/06/29 11:46:30.543801 [INFO] Running command: systemctl enable aws-eb.target
2025/06/29 11:46:30.815782 [INFO] Running command: systemctl start aws-eb.target
2025/06/29 11:46:30.824577 [INFO] Running command: systemctl enable healthd.service
2025/06/29 11:46:31.047257 [INFO] Created symlink /etc/systemd/system/multi-user.target.wants/healthd.service → /etc/systemd/system/healthd.service.

2025/06/29 11:46:31.047293 [INFO] Running command: systemctl show -p PartOf healthd.service
2025/06/29 11:46:31.058525 [INFO] Running command: systemctl is-active healthd.service
2025/06/29 11:46:31.066030 [INFO] Running command: systemctl start healthd.service
2025/06/29 11:46:31.124410 [INFO] Copying file /opt/elasticbeanstalk/config/private/healthd/healthd_logformat.conf to /var/proxy/staging/nginx/conf.d/healthd_logformat.conf
2025/06/29 11:46:31.125592 [INFO] Copying file /opt/elasticbeanstalk/config/private/healthd/healthd_nginx.conf to /var/proxy/staging/nginx/conf.d/elasticbeanstalk/healthd.conf
2025/06/29 11:46:31.126721 [INFO] Executing instruction: configure log streaming
2025/06/29 11:46:31.126733 [INFO] log streaming is not enabled
2025/06/29 11:46:31.126735 [INFO] disable log stream
2025/06/29 11:46:31.126746 [INFO] Running command: systemctl show -p PartOf amazon-cloudwatch-agent.service
2025/06/29 11:46:31.139729 [INFO] Running command: systemctl stop amazon-cloudwatch-agent.service
2025/06/29 11:46:31.156041 [INFO] Executing instruction: GetToggleForceRotate
2025/06/29 11:46:31.156065 [INFO] Checking if logs need forced rotation
2025/06/29 11:46:31.156123 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 -r AWSEBAutoScalingGroup --region us-west-2
2025/06/29 11:46:31.525010 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 -r AWSEBBeanstalkMetadata --region us-west-2
2025/06/29 11:46:31.884349 [INFO] Generating rsyslog config from Procfile
2025/06/29 11:46:31.887488 [INFO] Running command: systemctl restart rsyslog.service
2025/06/29 11:46:32.321844 [INFO] Executing instruction: PostBuildEbExtension
2025/06/29 11:46:32.321871 [INFO] Starting executing the config set Infra-EmbeddedPostBuild.
2025/06/29 11:46:32.321882 [INFO] Running command: /opt/aws/bin/cfn-init -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 -r AWSEBAutoScalingGroup --region us-west-2 --configsets Infra-EmbeddedPostBuild
2025/06/29 11:46:32.628143 [INFO] Finished executing the config set Infra-EmbeddedPostBuild.

2025/06/29 11:46:32.628166 [INFO] Executing instruction: CleanEbExtensions
2025/06/29 11:46:32.628316 [INFO] Cleaned ebextensions subdirectories from app staging directory.
2025/06/29 11:46:32.628319 [INFO] Executing instruction: RunAppDeployPreDeployHooks
2025/06/29 11:46:32.628358 [INFO] Executing platform hooks in .platform/hooks/predeploy/
2025/06/29 11:46:32.628371 [INFO] The dir .platform/hooks/predeploy/ does not exist
2025/06/29 11:46:32.628373 [INFO] Finished running scripts in /var/app/staging/.platform/hooks/predeploy
2025/06/29 11:46:32.628377 [INFO] Executing instruction: stop X-Ray
2025/06/29 11:46:32.628380 [INFO] stop X-Ray ...
2025/06/29 11:46:32.628389 [INFO] Running command: systemctl show -p PartOf xray.service
2025/06/29 11:46:32.642557 [WARN] stopProcess Warning: process xray is not registered 
2025/06/29 11:46:32.642584 [INFO] Running command: systemctl stop xray.service
2025/06/29 11:46:32.655240 [INFO] Executing instruction: stop proxy
2025/06/29 11:46:32.655270 [INFO] Running command: systemctl show -p PartOf httpd.service
2025/06/29 11:46:32.669246 [WARN] deregisterProcess Warning: process httpd is not registered, skipping...

2025/06/29 11:46:32.669277 [INFO] Running command: systemctl show -p PartOf nginx.service
2025/06/29 11:46:32.681842 [WARN] deregisterProcess Warning: process nginx is not registered, skipping...

2025/06/29 11:46:32.681863 [INFO] Executing instruction: FlipApplication
2025/06/29 11:46:32.681866 [INFO] Fetching environment variables...
2025/06/29 11:46:32.682140 [INFO] Purge old process...
2025/06/29 11:46:32.682156 [INFO] Removing /var/app/current/ if it exists
2025/06/29 11:46:32.682164 [INFO] Renaming /var/app/staging/ to /var/app/current/
2025/06/29 11:46:32.682181 [INFO] Register application processes...
2025/06/29 11:46:32.682183 [INFO] Registering the proc: web

2025/06/29 11:46:32.682192 [INFO] Running command: systemctl show -p PartOf web.service
2025/06/29 11:46:32.694112 [INFO] Running command: systemctl daemon-reload
2025/06/29 11:46:32.924152 [INFO] Running command: systemctl reset-failed
2025/06/29 11:46:32.931524 [INFO] Running command: systemctl is-enabled eb-app.target
2025/06/29 11:46:32.939060 [INFO] Copying file /opt/elasticbeanstalk/config/private/aws-eb.target to /etc/systemd/system/eb-app.target
2025/06/29 11:46:32.940424 [INFO] Running command: systemctl enable eb-app.target
2025/06/29 11:46:33.183095 [INFO] Created symlink /etc/systemd/system/multi-user.target.wants/eb-app.target → /etc/systemd/system/eb-app.target.

2025/06/29 11:46:33.183128 [INFO] Running command: systemctl start eb-app.target
2025/06/29 11:46:33.191851 [INFO] Running command: systemctl enable web.service
2025/06/29 11:46:33.466812 [INFO] Created symlink /etc/systemd/system/multi-user.target.wants/web.service → /etc/systemd/system/web.service.

2025/06/29 11:46:33.466844 [INFO] Running command: systemctl show -p PartOf web.service
2025/06/29 11:46:33.478071 [INFO] Running command: systemctl is-active web.service
2025/06/29 11:46:33.485707 [INFO] Running command: systemctl start web.service
2025/06/29 11:46:33.543699 [INFO] Executing instruction: start X-Ray
2025/06/29 11:46:33.543715 [INFO] X-Ray is not enabled.
2025/06/29 11:46:33.543718 [INFO] Executing instruction: start proxy with new configuration
2025/06/29 11:46:33.543740 [INFO] Running command: /usr/sbin/nginx -t -c /var/proxy/staging/nginx/nginx.conf
2025/06/29 11:46:33.592218 [INFO] nginx: [warn] could not build optimal types_hash, you should increase either types_hash_max_size: 1024 or types_hash_bucket_size: 64; ignoring types_hash_bucket_size
nginx: the configuration file /var/proxy/staging/nginx/nginx.conf syntax is ok
nginx: configuration file /var/proxy/staging/nginx/nginx.conf test is successful

2025/06/29 11:46:33.592432 [INFO] Running command: cp -rp /var/proxy/staging/nginx/* /etc/nginx
2025/06/29 11:46:33.596845 [INFO] Running command: systemctl show -p PartOf nginx.service
2025/06/29 11:46:33.617580 [INFO] Running command: systemctl daemon-reload
2025/06/29 11:46:33.943546 [INFO] Running command: systemctl reset-failed
2025/06/29 11:46:34.025259 [INFO] Running command: systemctl show -p PartOf nginx.service
2025/06/29 11:46:34.039393 [INFO] Running command: systemctl is-active nginx.service
2025/06/29 11:46:34.048269 [INFO] Running command: systemctl start nginx.service
2025/06/29 11:46:34.178892 [INFO] Executing instruction: configureSqsd
2025/06/29 11:46:34.178909 [INFO] This is a web server environment instance, skip configure sqsd daemon ...
2025/06/29 11:46:34.178911 [INFO] Executing instruction: startSqsd
2025/06/29 11:46:34.178914 [INFO] This is a web server environment instance, skip start sqsd daemon ...
2025/06/29 11:46:34.178916 [INFO] Executing instruction: Track pids in healthd
2025/06/29 11:46:34.178919 [INFO] This is an enhanced health env...
2025/06/29 11:46:34.178929 [INFO] Running command: systemctl show -p ConsistsOf aws-eb.target | cut -d= -f2
2025/06/29 11:46:34.190411 [INFO] cfn-hup.service nginx.service healthd.service

2025/06/29 11:46:34.190440 [INFO] Running command: systemctl show -p ConsistsOf eb-app.target | cut -d= -f2
2025/06/29 11:46:34.200979 [INFO] web.service

2025/06/29 11:46:34.201191 [INFO] Executing instruction: RunAppDeployPostDeployHooks
2025/06/29 11:46:34.201205 [INFO] Executing platform hooks in .platform/hooks/postdeploy/
2025/06/29 11:46:34.201218 [INFO] The dir .platform/hooks/postdeploy/ does not exist
2025/06/29 11:46:34.201220 [INFO] Finished running scripts in /var/app/current/.platform/hooks/postdeploy
2025/06/29 11:46:34.201224 [INFO] Executing cleanup logic
2025/06/29 11:46:34.201327 [INFO] CommandService Response: {"status":"SUCCESS","api_version":"1.0","results":[{"status":"SUCCESS","msg":"Engine execution has succeeded.","returncode":0,"events":[{"msg":"Instance deployment used the commands in your 'Procfile' to initiate startup of your application.","timestamp":1751197590203,"severity":"INFO"},{"msg":"Instance deployment completed successfully.","timestamp":1751197594201,"severity":"INFO"}]}]}

2025/06/29 11:46:34.201446 [INFO] Platform Engine finished execution on command: app-deploy

2025/06/29 11:48:00.564844 [INFO] Starting...
2025/06/29 11:48:00.564890 [INFO] Starting EBPlatform-PlatformEngine
2025/06/29 11:48:00.564924 [INFO] reading event message file
2025/06/29 11:48:00.565237 [INFO] Engine received EB command cfn-hup-exec

2025/06/29 11:48:00.635070 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 -r AWSEBAutoScalingGroup --region us-west-2
2025/06/29 11:48:00.935247 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 -r AWSEBBeanstalkMetadata --region us-west-2
2025/06/29 11:48:01.352343 [INFO] checking whether command bundle-log is applicable to this instance...
2025/06/29 11:48:01.352440 [INFO] this command is applicable to the instance, thus instance should execute command
2025/06/29 11:48:01.352446 [INFO] Engine command: (bundle-log)

2025/06/29 11:48:01.352510 [INFO] Executing instruction: GetBundleLogs
2025/06/29 11:48:01.352514 [INFO] Bundle Logs...
2025/06/29 11:48:01.498648 [INFO] Executing cleanup logic
2025/06/29 11:48:01.498752 [INFO] CommandService Response: {"status":"SUCCESS","api_version":"1.0","results":[{"status":"SUCCESS","msg":"Engine execution has succeeded.","returncode":0,"events":[{"msg":"Instance deployment completed successfully.","timestamp":1751197681498,"severity":"INFO"}]}]}

2025/06/29 11:48:01.498770 [INFO] Platform Engine finished execution on command: bundle-log

2025/06/29 11:48:52.453258 [INFO] Starting...
2025/06/29 11:48:52.453300 [INFO] Starting EBPlatform-PlatformEngine
2025/06/29 11:48:52.453335 [INFO] reading event message file
2025/06/29 11:48:52.453634 [INFO] Engine received EB command cfn-hup-exec

2025/06/29 11:48:52.521829 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 -r AWSEBAutoScalingGroup --region us-west-2
2025/06/29 11:48:52.831072 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 -r AWSEBBeanstalkMetadata --region us-west-2
2025/06/29 11:48:53.129293 [INFO] checking whether command app-deploy is applicable to this instance...
2025/06/29 11:48:53.129305 [INFO] this command is applicable to the instance, thus instance should execute command
2025/06/29 11:48:53.129308 [INFO] Engine command: (app-deploy)

2025/06/29 11:48:53.129312 [INFO] Downloading EB Application...
2025/06/29 11:48:53.129314 [INFO] Region: us-west-2
2025/06/29 11:48:53.129316 [INFO] envID: e-dptrdke822
2025/06/29 11:48:53.129319 [INFO] envBucket: elasticbeanstalk-us-west-2-************
2025/06/29 11:48:53.129322 [INFO] accountID: ************
2025/06/29 11:48:53.129326 [INFO] Using manifest file name from command request
2025/06/29 11:48:53.129333 [INFO] Manifest name is : manifest_1751197730337
2025/06/29 11:48:53.129336 [INFO] Download app version manifest
2025/06/29 11:48:53.129517 [INFO] Downloading: bucket: elasticbeanstalk-us-west-2-************, object: resources/environments/e-dptrdke822/_runtime/versions/manifest_1751197730337, expected bucket owner: ************
2025/06/29 11:48:53.163438 [INFO] Download successful142bytes downloaded
2025/06/29 11:48:53.163595 [INFO] Trying to read and parse version manifest...
2025/06/29 11:48:53.163667 [INFO] Downloading: bucket: elasticbeanstalk-us-west-2-************, object: resources/environments/e-dptrdke822/_runtime/_versions/winmemory-processing-api/app-250629_124845405393-stage-250629_124845405420, expected bucket owner: ************
2025/06/29 11:48:53.183695 [INFO] Download successful2683bytes downloaded
2025/06/29 11:48:53.183965 [INFO] Executing instruction: ElectLeader
2025/06/29 11:48:53.183972 [INFO] Running leader election for instance i-06cc7bc2ee1b17d55...
2025/06/29 11:48:53.183975 [INFO] Calling the cfn-elect-cmd-leader to elect the command leader.
2025/06/29 11:48:53.183987 [INFO] Running command: /opt/aws/bin/cfn-elect-cmd-leader --stack arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 --command-name ElasticBeanstalkCommand-AWSEBAutoScalingGroup --invocation-id 879e7ee1-1448-491b-812b-3448dec09fc4 --listener-id i-06cc7bc2ee1b17d55 --region us-west-2
2025/06/29 11:48:53.494212 [INFO] Instance is Leader.
2025/06/29 11:48:53.494272 [INFO] Executing instruction: stopSqsd
2025/06/29 11:48:53.494279 [INFO] This is a web server environment instance, skip stop sqsd daemon ...
2025/06/29 11:48:53.494282 [INFO] Executing instruction: PreBuildEbExtension
2025/06/29 11:48:53.494286 [INFO] Starting executing the config set Infra-EmbeddedPreBuild.
2025/06/29 11:48:53.494297 [INFO] Running command: /opt/aws/bin/cfn-init -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 -r AWSEBAutoScalingGroup --region us-west-2 --configsets Infra-EmbeddedPreBuild
2025/06/29 11:49:06.270244 [INFO] Finished executing the config set Infra-EmbeddedPreBuild.

2025/06/29 11:49:06.270280 [INFO] Executing instruction: StageApplication
2025/06/29 11:49:06.270286 [INFO] Recreating /var/app/staging/
2025/06/29 11:49:06.270396 [INFO] extracting /opt/elasticbeanstalk/deployment/app_source_bundle to /var/app/staging/
2025/06/29 11:49:06.270409 [INFO] Running command: /usr/bin/unzip -q -o /opt/elasticbeanstalk/deployment/app_source_bundle -d /var/app/staging/
2025/06/29 11:49:06.276235 [INFO] finished extracting /opt/elasticbeanstalk/deployment/app_source_bundle to /var/app/staging/ successfully
2025/06/29 11:49:06.278381 [INFO] Executing instruction: RunAppDeployPreBuildHooks
2025/06/29 11:49:06.278411 [INFO] Executing platform hooks in .platform/hooks/prebuild/
2025/06/29 11:49:06.278427 [INFO] The dir .platform/hooks/prebuild/ does not exist
2025/06/29 11:49:06.278429 [INFO] Finished running scripts in /var/app/staging/.platform/hooks/prebuild
2025/06/29 11:49:06.278433 [INFO] Executing instruction: InstallDependency
2025/06/29 11:49:06.278439 [INFO] checking dependencies file
2025/06/29 11:49:06.278450 [INFO] Installing dependencies with requirements.txt by using Pip
2025/06/29 11:49:06.278459 [INFO] Running command: /var/app/venv/staging-LQM1lest/bin/pip install -r requirements.txt
2025/06/29 11:49:07.552574 [INFO] Requirement already satisfied: flask in /var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages (from -r requirements.txt (line 1)) (3.1.1)
Requirement already satisfied: opencv-python in /var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages (from -r requirements.txt (line 2)) (*********)
Requirement already satisfied: numpy in /var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages (from -r requirements.txt (line 3)) (2.3.1)
Requirement already satisfied: flask-cors in /var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages (from -r requirements.txt (line 4)) (6.0.1)
Requirement already satisfied: blinker>=1.9.0 in /var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages (from flask->-r requirements.txt (line 1)) (1.9.0)
Requirement already satisfied: click>=8.1.3 in /var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages (from flask->-r requirements.txt (line 1)) (8.2.1)
Requirement already satisfied: itsdangerous>=2.2.0 in /var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages (from flask->-r requirements.txt (line 1)) (2.2.0)
Requirement already satisfied: jinja2>=3.1.2 in /var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages (from flask->-r requirements.txt (line 1)) (3.1.6)
Requirement already satisfied: markupsafe>=2.1.1 in /var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages (from flask->-r requirements.txt (line 1)) (3.0.2)
Requirement already satisfied: werkzeug>=3.1.0 in /var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages (from flask->-r requirements.txt (line 1)) (3.1.3)

2025/06/29 11:49:07.552596 [INFO] 
[notice] A new release of pip available: 22.3.1 -> 25.1.1
[notice] To update, run: python3.11 -m pip install --upgrade pip

2025/06/29 11:49:07.552603 [INFO] Executing instruction: check Procfile
2025/06/29 11:49:07.552639 [INFO] detected Procfile in application source bundle ...
2025/06/29 11:49:07.552653 [INFO] Executing instruction: configure X-Ray
2025/06/29 11:49:07.552656 [INFO] X-Ray is not enabled.
2025/06/29 11:49:07.552659 [INFO] Executing instruction: configure proxy server
2025/06/29 11:49:07.552675 [INFO] Recreating /var/proxy/staging/nginx
2025/06/29 11:49:07.558827 [INFO] Running command: cp -rp /var/app/staging/.platform/nginx/. /var/proxy/staging/nginx
2025/06/29 11:49:07.567196 [INFO] Executing instruction: configure healthd specific proxy conf
2025/06/29 11:49:07.570154 [INFO] Running command: systemctl show -p PartOf healthd.service
2025/06/29 11:49:07.629109 [WARN] Warning: process healthd is already registered...
Deregistering the process ...
2025/06/29 11:49:07.629145 [INFO] Running command: systemctl show -p PartOf healthd.service
2025/06/29 11:49:07.642418 [INFO] Running command: systemctl is-active healthd.service
2025/06/29 11:49:07.657293 [INFO] Running command: systemctl show -p PartOf healthd.service
2025/06/29 11:49:07.671069 [INFO] Running command: systemctl stop healthd.service
2025/06/29 11:49:07.755108 [INFO] Running command: systemctl disable healthd.service
2025/06/29 11:49:08.050841 [INFO] Removed "/etc/systemd/system/multi-user.target.wants/healthd.service".

2025/06/29 11:49:08.051626 [INFO] Running command: systemctl daemon-reload
2025/06/29 11:49:08.336281 [INFO] Running command: systemctl reset-failed
2025/06/29 11:49:08.347789 [INFO] Running command: systemctl daemon-reload
2025/06/29 11:49:08.594953 [INFO] Running command: systemctl reset-failed
2025/06/29 11:49:08.603795 [INFO] Running command: systemctl is-enabled aws-eb.target
2025/06/29 11:49:08.611720 [INFO] Running command: systemctl enable aws-eb.target
2025/06/29 11:49:08.851936 [INFO] Running command: systemctl start aws-eb.target
2025/06/29 11:49:08.860611 [INFO] Running command: systemctl enable healthd.service
2025/06/29 11:49:09.112932 [INFO] Created symlink /etc/systemd/system/multi-user.target.wants/healthd.service → /etc/systemd/system/healthd.service.

2025/06/29 11:49:09.112981 [INFO] Running command: systemctl show -p PartOf healthd.service
2025/06/29 11:49:09.123634 [INFO] Running command: systemctl is-active healthd.service
2025/06/29 11:49:09.131658 [INFO] Running command: systemctl start healthd.service
2025/06/29 11:49:09.188495 [INFO] Copying file /opt/elasticbeanstalk/config/private/healthd/healthd_logformat.conf to /var/proxy/staging/nginx/conf.d/healthd_logformat.conf
2025/06/29 11:49:09.190101 [INFO] Copying file /opt/elasticbeanstalk/config/private/healthd/healthd_nginx.conf to /var/proxy/staging/nginx/conf.d/elasticbeanstalk/healthd.conf
2025/06/29 11:49:09.191304 [INFO] Executing instruction: configure log streaming
2025/06/29 11:49:09.191317 [INFO] log streaming is not enabled
2025/06/29 11:49:09.191321 [INFO] disable log stream
2025/06/29 11:49:09.191331 [INFO] Running command: systemctl show -p PartOf amazon-cloudwatch-agent.service
2025/06/29 11:49:09.203716 [INFO] Running command: systemctl stop amazon-cloudwatch-agent.service
2025/06/29 11:49:09.215978 [INFO] Executing instruction: GetToggleForceRotate
2025/06/29 11:49:09.215998 [INFO] Checking if logs need forced rotation
2025/06/29 11:49:09.216012 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 -r AWSEBAutoScalingGroup --region us-west-2
2025/06/29 11:49:09.643772 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 -r AWSEBBeanstalkMetadata --region us-west-2
2025/06/29 11:49:10.018382 [INFO] Generating rsyslog config from Procfile
2025/06/29 11:49:10.021863 [INFO] Running command: systemctl restart rsyslog.service
2025/06/29 11:49:10.347283 [INFO] Executing instruction: PostBuildEbExtension
2025/06/29 11:49:10.347315 [INFO] Starting executing the config set Infra-EmbeddedPostBuild.
2025/06/29 11:49:10.347333 [INFO] Running command: /opt/aws/bin/cfn-init -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 -r AWSEBAutoScalingGroup --region us-west-2 --configsets Infra-EmbeddedPostBuild
2025/06/29 11:49:10.666496 [INFO] Finished executing the config set Infra-EmbeddedPostBuild.

2025/06/29 11:49:10.666519 [INFO] Executing instruction: CleanEbExtensions
2025/06/29 11:49:10.666627 [INFO] Cleaned ebextensions subdirectories from app staging directory.
2025/06/29 11:49:10.666631 [INFO] Executing instruction: RunAppDeployPreDeployHooks
2025/06/29 11:49:10.666644 [INFO] Executing platform hooks in .platform/hooks/predeploy/
2025/06/29 11:49:10.666656 [INFO] The dir .platform/hooks/predeploy/ does not exist
2025/06/29 11:49:10.666659 [INFO] Finished running scripts in /var/app/staging/.platform/hooks/predeploy
2025/06/29 11:49:10.666663 [INFO] Executing instruction: stop X-Ray
2025/06/29 11:49:10.666666 [INFO] stop X-Ray ...
2025/06/29 11:49:10.666678 [INFO] Running command: systemctl show -p PartOf xray.service
2025/06/29 11:49:10.680045 [WARN] stopProcess Warning: process xray is not registered 
2025/06/29 11:49:10.680070 [INFO] Running command: systemctl stop xray.service
2025/06/29 11:49:10.693083 [INFO] Executing instruction: stop proxy
2025/06/29 11:49:10.693111 [INFO] Running command: systemctl show -p PartOf httpd.service
2025/06/29 11:49:10.708380 [WARN] deregisterProcess Warning: process httpd is not registered, skipping...

2025/06/29 11:49:10.708409 [INFO] Running command: systemctl show -p PartOf nginx.service
2025/06/29 11:49:10.720679 [INFO] Running command: systemctl is-active nginx.service
2025/06/29 11:49:10.728503 [INFO] Running command: systemctl show -p PartOf nginx.service
2025/06/29 11:49:10.738267 [INFO] Running command: systemctl stop nginx.service
2025/06/29 11:49:10.947553 [INFO] Running command: systemctl disable nginx.service
2025/06/29 11:49:11.221876 [INFO] Running command: systemctl daemon-reload
2025/06/29 11:49:11.490839 [INFO] Running command: systemctl reset-failed
2025/06/29 11:49:11.499244 [INFO] Executing instruction: FlipApplication
2025/06/29 11:49:11.499268 [INFO] Fetching environment variables...
2025/06/29 11:49:11.499432 [INFO] Purge old process...
2025/06/29 11:49:11.499450 [INFO] Running command: systemctl stop eb-app.target
2025/06/29 11:49:11.507539 [INFO] Running command: systemctl show -p ConsistsOf eb-app.target | cut -d= -f2
2025/06/29 11:49:11.517204 [INFO] web.service

2025/06/29 11:49:11.517227 [INFO] deregistering process: web
2025/06/29 11:49:11.517238 [INFO] Running command: systemctl show -p PartOf web.service
2025/06/29 11:49:11.527534 [INFO] Running command: systemctl is-active web.service
2025/06/29 11:49:11.534889 [INFO] Running command: systemctl disable web.service
2025/06/29 11:49:11.790626 [INFO] Removed "/etc/systemd/system/multi-user.target.wants/web.service".

2025/06/29 11:49:11.790707 [INFO] Running command: systemctl daemon-reload
2025/06/29 11:49:12.073452 [INFO] Running command: systemctl reset-failed
2025/06/29 11:49:12.081684 [INFO] Running command: systemctl is-active web.service
2025/06/29 11:49:12.089179 [INFO] Process web has been fully terminated
2025/06/29 11:49:12.089195 [INFO] All processes have been fully terminated
2025/06/29 11:49:12.089199 [INFO] Removing /var/app/current/ if it exists
2025/06/29 11:49:12.089388 [INFO] Renaming /var/app/staging/ to /var/app/current/
2025/06/29 11:49:12.089411 [INFO] Register application processes...
2025/06/29 11:49:12.089415 [INFO] Registering the proc: web

2025/06/29 11:49:12.089422 [INFO] Running command: systemctl show -p PartOf web.service
2025/06/29 11:49:12.101728 [INFO] Running command: systemctl daemon-reload
2025/06/29 11:49:12.342052 [INFO] Running command: systemctl reset-failed
2025/06/29 11:49:12.351150 [INFO] Running command: systemctl is-enabled eb-app.target
2025/06/29 11:49:12.359260 [INFO] Running command: systemctl enable eb-app.target
2025/06/29 11:49:12.607628 [INFO] Running command: systemctl start eb-app.target
2025/06/29 11:49:12.617584 [INFO] Running command: systemctl enable web.service
2025/06/29 11:49:12.846618 [INFO] Created symlink /etc/systemd/system/multi-user.target.wants/web.service → /etc/systemd/system/web.service.

2025/06/29 11:49:12.846655 [INFO] Running command: systemctl show -p PartOf web.service
2025/06/29 11:49:12.858366 [INFO] Running command: systemctl is-active web.service
2025/06/29 11:49:12.865959 [INFO] Running command: systemctl start web.service
2025/06/29 11:49:12.950581 [INFO] Executing instruction: start X-Ray
2025/06/29 11:49:12.950610 [INFO] X-Ray is not enabled.
2025/06/29 11:49:12.950616 [INFO] Executing instruction: start proxy with new configuration
2025/06/29 11:49:12.950638 [INFO] Running command: /usr/sbin/nginx -t -c /var/proxy/staging/nginx/nginx.conf
2025/06/29 11:49:12.978968 [INFO] nginx: [warn] could not build optimal types_hash, you should increase either types_hash_max_size: 1024 or types_hash_bucket_size: 64; ignoring types_hash_bucket_size
nginx: the configuration file /var/proxy/staging/nginx/nginx.conf syntax is ok
nginx: configuration file /var/proxy/staging/nginx/nginx.conf test is successful

2025/06/29 11:49:12.980035 [INFO] Running command: cp -rp /var/proxy/staging/nginx/* /etc/nginx
2025/06/29 11:49:12.985062 [INFO] Running command: systemctl show -p PartOf nginx.service
2025/06/29 11:49:13.009115 [INFO] Running command: systemctl daemon-reload
2025/06/29 11:49:13.357719 [INFO] Running command: systemctl reset-failed
2025/06/29 11:49:13.371766 [INFO] Running command: systemctl show -p PartOf nginx.service
2025/06/29 11:49:13.388744 [INFO] Running command: systemctl is-active nginx.service
2025/06/29 11:49:13.402290 [INFO] Running command: systemctl start nginx.service
2025/06/29 11:49:13.542529 [INFO] Executing instruction: configureSqsd
2025/06/29 11:49:13.542550 [INFO] This is a web server environment instance, skip configure sqsd daemon ...
2025/06/29 11:49:13.542555 [INFO] Executing instruction: startSqsd
2025/06/29 11:49:13.542558 [INFO] This is a web server environment instance, skip start sqsd daemon ...
2025/06/29 11:49:13.542562 [INFO] Executing instruction: Track pids in healthd
2025/06/29 11:49:13.542566 [INFO] This is an enhanced health env...
2025/06/29 11:49:13.542578 [INFO] Running command: systemctl show -p ConsistsOf aws-eb.target | cut -d= -f2
2025/06/29 11:49:13.554009 [INFO] cfn-hup.service healthd.service nginx.service

2025/06/29 11:49:13.554042 [INFO] Running command: systemctl show -p ConsistsOf eb-app.target | cut -d= -f2
2025/06/29 11:49:13.564730 [INFO] web.service

2025/06/29 11:49:13.564962 [INFO] Executing instruction: RunAppDeployPostDeployHooks
2025/06/29 11:49:13.565024 [INFO] Executing platform hooks in .platform/hooks/postdeploy/
2025/06/29 11:49:13.565037 [INFO] The dir .platform/hooks/postdeploy/ does not exist
2025/06/29 11:49:13.565039 [INFO] Finished running scripts in /var/app/current/.platform/hooks/postdeploy
2025/06/29 11:49:13.565044 [INFO] Executing cleanup logic
2025/06/29 11:49:13.565112 [INFO] CommandService Response: {"status":"SUCCESS","api_version":"1.0","results":[{"status":"SUCCESS","msg":"Engine execution has succeeded.","returncode":0,"events":[{"msg":"Instance deployment used the commands in your 'Procfile' to initiate startup of your application.","timestamp":1751197747552,"severity":"INFO"},{"msg":"Instance deployment completed successfully.","timestamp":1751197753565,"severity":"INFO"}]}]}

2025/06/29 11:49:13.565369 [INFO] Platform Engine finished execution on command: app-deploy

2025/06/29 11:49:37.872023 [INFO] Starting...
2025/06/29 11:49:37.872065 [INFO] Starting EBPlatform-PlatformEngine
2025/06/29 11:49:37.872097 [INFO] reading event message file
2025/06/29 11:49:37.872406 [INFO] Engine received EB command cfn-hup-exec

2025/06/29 11:49:37.939338 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 -r AWSEBAutoScalingGroup --region us-west-2
2025/06/29 11:49:38.255732 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 -r AWSEBBeanstalkMetadata --region us-west-2
2025/06/29 11:49:38.546840 [INFO] checking whether command bundle-log is applicable to this instance...
2025/06/29 11:49:38.546857 [INFO] this command is applicable to the instance, thus instance should execute command
2025/06/29 11:49:38.546860 [INFO] Engine command: (bundle-log)

2025/06/29 11:49:38.546921 [INFO] Executing instruction: GetBundleLogs
2025/06/29 11:49:38.546923 [INFO] Bundle Logs...
2025/06/29 11:49:38.663071 [INFO] Executing cleanup logic
2025/06/29 11:49:38.663155 [INFO] CommandService Response: {"status":"SUCCESS","api_version":"1.0","results":[{"status":"SUCCESS","msg":"Engine execution has succeeded.","returncode":0,"events":[{"msg":"Instance deployment completed successfully.","timestamp":1751197778663,"severity":"INFO"}]}]}

2025/06/29 11:49:38.663172 [INFO] Platform Engine finished execution on command: bundle-log

2025/06/29 11:52:34.175642 [INFO] Starting...
2025/06/29 11:52:34.175696 [INFO] Starting EBPlatform-PlatformEngine
2025/06/29 11:52:34.175734 [INFO] reading event message file
2025/06/29 11:52:34.176066 [INFO] Engine received EB command cfn-hup-exec

2025/06/29 11:52:34.244004 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 -r AWSEBAutoScalingGroup --region us-west-2
2025/06/29 11:52:34.530844 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 -r AWSEBBeanstalkMetadata --region us-west-2
2025/06/29 11:52:34.813584 [INFO] checking whether command bundle-log is applicable to this instance...
2025/06/29 11:52:34.813597 [INFO] this command is applicable to the instance, thus instance should execute command
2025/06/29 11:52:34.813599 [INFO] Engine command: (bundle-log)

2025/06/29 11:52:34.813647 [INFO] Executing instruction: GetBundleLogs
2025/06/29 11:52:34.813651 [INFO] Bundle Logs...
2025/06/29 11:52:34.934139 [INFO] Executing cleanup logic
2025/06/29 11:52:34.934215 [INFO] CommandService Response: {"status":"SUCCESS","api_version":"1.0","results":[{"status":"SUCCESS","msg":"Engine execution has succeeded.","returncode":0,"events":[{"msg":"Instance deployment completed successfully.","timestamp":1751197954934,"severity":"INFO"}]}]}

2025/06/29 11:52:34.934234 [INFO] Platform Engine finished execution on command: bundle-log

2025/06/29 12:29:41.004956 [INFO] Starting...
2025/06/29 12:29:41.005001 [INFO] Starting EBPlatform-PlatformEngine
2025/06/29 12:29:41.005034 [INFO] reading event message file
2025/06/29 12:29:41.005340 [INFO] Engine received EB command cfn-hup-exec

2025/06/29 12:29:41.078866 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 -r AWSEBAutoScalingGroup --region us-west-2
2025/06/29 12:29:41.369832 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 -r AWSEBBeanstalkMetadata --region us-west-2
2025/06/29 12:29:41.658555 [INFO] checking whether command app-deploy is applicable to this instance...
2025/06/29 12:29:41.658568 [INFO] this command is applicable to the instance, thus instance should execute command
2025/06/29 12:29:41.658570 [INFO] Engine command: (app-deploy)

2025/06/29 12:29:41.658573 [INFO] Downloading EB Application...
2025/06/29 12:29:41.658576 [INFO] Region: us-west-2
2025/06/29 12:29:41.658578 [INFO] envID: e-dptrdke822
2025/06/29 12:29:41.658580 [INFO] envBucket: elasticbeanstalk-us-west-2-************
2025/06/29 12:29:41.658583 [INFO] accountID: ************
2025/06/29 12:29:41.658586 [INFO] Using manifest file name from command request
2025/06/29 12:29:41.658593 [INFO] Manifest name is : manifest_1751200178748
2025/06/29 12:29:41.658600 [INFO] Download app version manifest
2025/06/29 12:29:41.658776 [INFO] Downloading: bucket: elasticbeanstalk-us-west-2-************, object: resources/environments/e-dptrdke822/_runtime/versions/manifest_1751200178748, expected bucket owner: ************
2025/06/29 12:29:41.695340 [INFO] Download successful142bytes downloaded
2025/06/29 12:29:41.695479 [INFO] Trying to read and parse version manifest...
2025/06/29 12:29:41.695565 [INFO] Downloading: bucket: elasticbeanstalk-us-west-2-************, object: resources/environments/e-dptrdke822/_runtime/_versions/winmemory-processing-api/app-250629_132933691488-stage-250629_132933691517, expected bucket owner: ************
2025/06/29 12:29:41.714982 [INFO] Download successful2683bytes downloaded
2025/06/29 12:29:41.715260 [INFO] Executing instruction: ElectLeader
2025/06/29 12:29:41.715267 [INFO] Running leader election for instance i-06cc7bc2ee1b17d55...
2025/06/29 12:29:41.715270 [INFO] Calling the cfn-elect-cmd-leader to elect the command leader.
2025/06/29 12:29:41.715281 [INFO] Running command: /opt/aws/bin/cfn-elect-cmd-leader --stack arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 --command-name ElasticBeanstalkCommand-AWSEBAutoScalingGroup --invocation-id 4c11e404-2ab9-4141-bfd5-5ec78c2c83e2 --listener-id i-06cc7bc2ee1b17d55 --region us-west-2
2025/06/29 12:29:42.017571 [INFO] Instance is Leader.
2025/06/29 12:29:42.017629 [INFO] Executing instruction: stopSqsd
2025/06/29 12:29:42.017635 [INFO] This is a web server environment instance, skip stop sqsd daemon ...
2025/06/29 12:29:42.017638 [INFO] Executing instruction: PreBuildEbExtension
2025/06/29 12:29:42.017641 [INFO] Starting executing the config set Infra-EmbeddedPreBuild.
2025/06/29 12:29:42.017654 [INFO] Running command: /opt/aws/bin/cfn-init -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 -r AWSEBAutoScalingGroup --region us-west-2 --configsets Infra-EmbeddedPreBuild
2025/06/29 12:29:53.569149 [INFO] Finished executing the config set Infra-EmbeddedPreBuild.

2025/06/29 12:29:53.569189 [INFO] Executing instruction: StageApplication
2025/06/29 12:29:53.569193 [INFO] Recreating /var/app/staging/
2025/06/29 12:29:53.572914 [INFO] extracting /opt/elasticbeanstalk/deployment/app_source_bundle to /var/app/staging/
2025/06/29 12:29:53.574063 [INFO] Running command: /usr/bin/unzip -q -o /opt/elasticbeanstalk/deployment/app_source_bundle -d /var/app/staging/
2025/06/29 12:29:53.585452 [INFO] finished extracting /opt/elasticbeanstalk/deployment/app_source_bundle to /var/app/staging/ successfully
2025/06/29 12:29:53.588640 [INFO] Executing instruction: RunAppDeployPreBuildHooks
2025/06/29 12:29:53.588665 [INFO] Executing platform hooks in .platform/hooks/prebuild/
2025/06/29 12:29:53.588714 [INFO] The dir .platform/hooks/prebuild/ does not exist
2025/06/29 12:29:53.588718 [INFO] Finished running scripts in /var/app/staging/.platform/hooks/prebuild
2025/06/29 12:29:53.588722 [INFO] Executing instruction: InstallDependency
2025/06/29 12:29:53.589627 [INFO] checking dependencies file
2025/06/29 12:29:53.589660 [INFO] Installing dependencies with requirements.txt by using Pip
2025/06/29 12:29:53.589671 [INFO] Running command: /var/app/venv/staging-LQM1lest/bin/pip install -r requirements.txt
2025/06/29 12:29:54.475158 [INFO] Requirement already satisfied: flask in /var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages (from -r requirements.txt (line 1)) (3.1.1)
Requirement already satisfied: opencv-python in /var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages (from -r requirements.txt (line 2)) (*********)
Requirement already satisfied: numpy in /var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages (from -r requirements.txt (line 3)) (2.3.1)
Requirement already satisfied: flask-cors in /var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages (from -r requirements.txt (line 4)) (6.0.1)
Requirement already satisfied: blinker>=1.9.0 in /var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages (from flask->-r requirements.txt (line 1)) (1.9.0)
Requirement already satisfied: click>=8.1.3 in /var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages (from flask->-r requirements.txt (line 1)) (8.2.1)
Requirement already satisfied: itsdangerous>=2.2.0 in /var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages (from flask->-r requirements.txt (line 1)) (2.2.0)
Requirement already satisfied: jinja2>=3.1.2 in /var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages (from flask->-r requirements.txt (line 1)) (3.1.6)
Requirement already satisfied: markupsafe>=2.1.1 in /var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages (from flask->-r requirements.txt (line 1)) (3.0.2)
Requirement already satisfied: werkzeug>=3.1.0 in /var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages (from flask->-r requirements.txt (line 1)) (3.1.3)

2025/06/29 12:29:54.475190 [INFO] 
[notice] A new release of pip available: 22.3.1 -> 25.1.1
[notice] To update, run: python3.11 -m pip install --upgrade pip

2025/06/29 12:29:54.475196 [INFO] Executing instruction: check Procfile
2025/06/29 12:29:54.475242 [INFO] detected Procfile in application source bundle ...
2025/06/29 12:29:54.475248 [INFO] Executing instruction: configure X-Ray
2025/06/29 12:29:54.475253 [INFO] X-Ray is not enabled.
2025/06/29 12:29:54.475256 [INFO] Executing instruction: configure proxy server
2025/06/29 12:29:54.475271 [INFO] Recreating /var/proxy/staging/nginx
2025/06/29 12:29:54.482257 [INFO] Running command: cp -rp /var/app/staging/.platform/nginx/. /var/proxy/staging/nginx
2025/06/29 12:29:54.491942 [INFO] Executing instruction: configure healthd specific proxy conf
2025/06/29 12:29:54.494320 [INFO] Running command: systemctl show -p PartOf healthd.service
2025/06/29 12:29:54.550196 [WARN] Warning: process healthd is already registered...
Deregistering the process ...
2025/06/29 12:29:54.550232 [INFO] Running command: systemctl show -p PartOf healthd.service
2025/06/29 12:29:54.562543 [INFO] Running command: systemctl is-active healthd.service
2025/06/29 12:29:54.573052 [INFO] Running command: systemctl show -p PartOf healthd.service
2025/06/29 12:29:54.584512 [INFO] Running command: systemctl stop healthd.service
2025/06/29 12:29:54.673005 [INFO] Running command: systemctl disable healthd.service
2025/06/29 12:29:54.932636 [INFO] Removed "/etc/systemd/system/multi-user.target.wants/healthd.service".

2025/06/29 12:29:54.932799 [INFO] Running command: systemctl daemon-reload
2025/06/29 12:29:55.216183 [INFO] Running command: systemctl reset-failed
2025/06/29 12:29:55.227784 [INFO] Running command: systemctl daemon-reload
2025/06/29 12:29:55.472339 [INFO] Running command: systemctl reset-failed
2025/06/29 12:29:55.480728 [INFO] Running command: systemctl is-enabled aws-eb.target
2025/06/29 12:29:55.488656 [INFO] Running command: systemctl enable aws-eb.target
2025/06/29 12:29:55.724390 [INFO] Running command: systemctl start aws-eb.target
2025/06/29 12:29:55.733259 [INFO] Running command: systemctl enable healthd.service
2025/06/29 12:29:55.963737 [INFO] Created symlink /etc/systemd/system/multi-user.target.wants/healthd.service → /etc/systemd/system/healthd.service.

2025/06/29 12:29:55.963763 [INFO] Running command: systemctl show -p PartOf healthd.service
2025/06/29 12:29:55.974282 [INFO] Running command: systemctl is-active healthd.service
2025/06/29 12:29:55.981762 [INFO] Running command: systemctl start healthd.service
2025/06/29 12:29:56.080444 [INFO] Copying file /opt/elasticbeanstalk/config/private/healthd/healthd_logformat.conf to /var/proxy/staging/nginx/conf.d/healthd_logformat.conf
2025/06/29 12:29:56.081702 [INFO] Copying file /opt/elasticbeanstalk/config/private/healthd/healthd_nginx.conf to /var/proxy/staging/nginx/conf.d/elasticbeanstalk/healthd.conf
2025/06/29 12:29:56.082852 [INFO] Executing instruction: configure log streaming
2025/06/29 12:29:56.082864 [INFO] log streaming is not enabled
2025/06/29 12:29:56.082866 [INFO] disable log stream
2025/06/29 12:29:56.082884 [INFO] Running command: systemctl show -p PartOf amazon-cloudwatch-agent.service
2025/06/29 12:29:56.099322 [INFO] Running command: systemctl stop amazon-cloudwatch-agent.service
2025/06/29 12:29:56.117399 [INFO] Executing instruction: GetToggleForceRotate
2025/06/29 12:29:56.117435 [INFO] Checking if logs need forced rotation
2025/06/29 12:29:56.117455 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 -r AWSEBAutoScalingGroup --region us-west-2
2025/06/29 12:29:56.504670 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 -r AWSEBBeanstalkMetadata --region us-west-2
2025/06/29 12:29:56.885364 [INFO] Generating rsyslog config from Procfile
2025/06/29 12:29:56.887668 [INFO] Running command: systemctl restart rsyslog.service
2025/06/29 12:29:57.299164 [INFO] Executing instruction: PostBuildEbExtension
2025/06/29 12:29:57.299202 [INFO] Starting executing the config set Infra-EmbeddedPostBuild.
2025/06/29 12:29:57.299215 [INFO] Running command: /opt/aws/bin/cfn-init -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 -r AWSEBAutoScalingGroup --region us-west-2 --configsets Infra-EmbeddedPostBuild
2025/06/29 12:29:57.607525 [INFO] Finished executing the config set Infra-EmbeddedPostBuild.

2025/06/29 12:29:57.607562 [INFO] Executing instruction: CleanEbExtensions
2025/06/29 12:29:57.607688 [INFO] Cleaned ebextensions subdirectories from app staging directory.
2025/06/29 12:29:57.607693 [INFO] Executing instruction: RunAppDeployPreDeployHooks
2025/06/29 12:29:57.607708 [INFO] Executing platform hooks in .platform/hooks/predeploy/
2025/06/29 12:29:57.607726 [INFO] The dir .platform/hooks/predeploy/ does not exist
2025/06/29 12:29:57.607729 [INFO] Finished running scripts in /var/app/staging/.platform/hooks/predeploy
2025/06/29 12:29:57.607735 [INFO] Executing instruction: stop X-Ray
2025/06/29 12:29:57.607738 [INFO] stop X-Ray ...
2025/06/29 12:29:57.607749 [INFO] Running command: systemctl show -p PartOf xray.service
2025/06/29 12:29:57.621660 [WARN] stopProcess Warning: process xray is not registered 
2025/06/29 12:29:57.621684 [INFO] Running command: systemctl stop xray.service
2025/06/29 12:29:57.636111 [INFO] Executing instruction: stop proxy
2025/06/29 12:29:57.636147 [INFO] Running command: systemctl show -p PartOf httpd.service
2025/06/29 12:29:57.650699 [WARN] deregisterProcess Warning: process httpd is not registered, skipping...

2025/06/29 12:29:57.650773 [INFO] Running command: systemctl show -p PartOf nginx.service
2025/06/29 12:29:57.662731 [INFO] Running command: systemctl is-active nginx.service
2025/06/29 12:29:57.670732 [INFO] Running command: systemctl show -p PartOf nginx.service
2025/06/29 12:29:57.679857 [INFO] Running command: systemctl stop nginx.service
2025/06/29 12:29:57.897559 [INFO] Running command: systemctl disable nginx.service
2025/06/29 12:29:58.167791 [INFO] Running command: systemctl daemon-reload
2025/06/29 12:29:58.405729 [INFO] Running command: systemctl reset-failed
2025/06/29 12:29:58.414379 [INFO] Executing instruction: FlipApplication
2025/06/29 12:29:58.414404 [INFO] Fetching environment variables...
2025/06/29 12:29:58.414668 [INFO] Purge old process...
2025/06/29 12:29:58.414688 [INFO] Running command: systemctl stop eb-app.target
2025/06/29 12:29:58.426466 [INFO] Running command: systemctl show -p ConsistsOf eb-app.target | cut -d= -f2
2025/06/29 12:29:58.441496 [INFO] web.service

2025/06/29 12:29:58.441523 [INFO] deregistering process: web
2025/06/29 12:29:58.441534 [INFO] Running command: systemctl show -p PartOf web.service
2025/06/29 12:29:58.456113 [INFO] Running command: systemctl is-active web.service
2025/06/29 12:29:58.466913 [INFO] Running command: systemctl disable web.service
2025/06/29 12:29:58.732592 [INFO] Removed "/etc/systemd/system/multi-user.target.wants/web.service".

2025/06/29 12:29:58.732681 [INFO] Running command: systemctl daemon-reload
2025/06/29 12:29:59.032823 [INFO] Running command: systemctl reset-failed
2025/06/29 12:29:59.041691 [INFO] Running command: systemctl is-active web.service
2025/06/29 12:29:59.049322 [INFO] Process web has been fully terminated
2025/06/29 12:29:59.049336 [INFO] All processes have been fully terminated
2025/06/29 12:29:59.049341 [INFO] Removing /var/app/current/ if it exists
2025/06/29 12:29:59.049562 [INFO] Renaming /var/app/staging/ to /var/app/current/
2025/06/29 12:29:59.049580 [INFO] Register application processes...
2025/06/29 12:29:59.049584 [INFO] Registering the proc: web

2025/06/29 12:29:59.049593 [INFO] Running command: systemctl show -p PartOf web.service
2025/06/29 12:29:59.062243 [INFO] Running command: systemctl daemon-reload
2025/06/29 12:29:59.344065 [INFO] Running command: systemctl reset-failed
2025/06/29 12:29:59.352745 [INFO] Running command: systemctl is-enabled eb-app.target
2025/06/29 12:29:59.361002 [INFO] Running command: systemctl enable eb-app.target
2025/06/29 12:29:59.572171 [INFO] Running command: systemctl start eb-app.target
2025/06/29 12:29:59.581362 [INFO] Running command: systemctl enable web.service
2025/06/29 12:29:59.874276 [INFO] Created symlink /etc/systemd/system/multi-user.target.wants/web.service → /etc/systemd/system/web.service.

2025/06/29 12:29:59.874310 [INFO] Running command: systemctl show -p PartOf web.service
2025/06/29 12:29:59.885480 [INFO] Running command: systemctl is-active web.service
2025/06/29 12:29:59.893053 [INFO] Running command: systemctl start web.service
2025/06/29 12:29:59.954152 [INFO] Executing instruction: start X-Ray
2025/06/29 12:29:59.954176 [INFO] X-Ray is not enabled.
2025/06/29 12:29:59.954181 [INFO] Executing instruction: start proxy with new configuration
2025/06/29 12:29:59.954205 [INFO] Running command: /usr/sbin/nginx -t -c /var/proxy/staging/nginx/nginx.conf
2025/06/29 12:29:59.979550 [INFO] nginx: [warn] could not build optimal types_hash, you should increase either types_hash_max_size: 1024 or types_hash_bucket_size: 64; ignoring types_hash_bucket_size
nginx: the configuration file /var/proxy/staging/nginx/nginx.conf syntax is ok
nginx: configuration file /var/proxy/staging/nginx/nginx.conf test is successful

2025/06/29 12:29:59.979873 [INFO] Running command: cp -rp /var/proxy/staging/nginx/* /etc/nginx
2025/06/29 12:29:59.984753 [INFO] Running command: systemctl show -p PartOf nginx.service
2025/06/29 12:30:00.019823 [INFO] Running command: systemctl daemon-reload
2025/06/29 12:30:00.516751 [INFO] Running command: systemctl reset-failed
2025/06/29 12:30:00.548751 [INFO] Running command: systemctl show -p PartOf nginx.service
2025/06/29 12:30:00.570532 [INFO] Running command: systemctl is-active nginx.service
2025/06/29 12:30:00.585621 [INFO] Running command: systemctl start nginx.service
2025/06/29 12:30:00.758440 [INFO] Executing instruction: configureSqsd
2025/06/29 12:30:00.758461 [INFO] This is a web server environment instance, skip configure sqsd daemon ...
2025/06/29 12:30:00.758465 [INFO] Executing instruction: startSqsd
2025/06/29 12:30:00.758468 [INFO] This is a web server environment instance, skip start sqsd daemon ...
2025/06/29 12:30:00.758471 [INFO] Executing instruction: Track pids in healthd
2025/06/29 12:30:00.758475 [INFO] This is an enhanced health env...
2025/06/29 12:30:00.758488 [INFO] Running command: systemctl show -p ConsistsOf aws-eb.target | cut -d= -f2
2025/06/29 12:30:00.772516 [INFO] cfn-hup.service nginx.service healthd.service

2025/06/29 12:30:00.772554 [INFO] Running command: systemctl show -p ConsistsOf eb-app.target | cut -d= -f2
2025/06/29 12:30:00.789755 [INFO] web.service

2025/06/29 12:30:00.790110 [INFO] Executing instruction: RunAppDeployPostDeployHooks
2025/06/29 12:30:00.790173 [INFO] Executing platform hooks in .platform/hooks/postdeploy/
2025/06/29 12:30:00.790191 [INFO] The dir .platform/hooks/postdeploy/ does not exist
2025/06/29 12:30:00.790194 [INFO] Finished running scripts in /var/app/current/.platform/hooks/postdeploy
2025/06/29 12:30:00.790209 [INFO] Executing cleanup logic
2025/06/29 12:30:00.791730 [INFO] CommandService Response: {"status":"SUCCESS","api_version":"1.0","results":[{"status":"SUCCESS","msg":"Engine execution has succeeded.","returncode":0,"events":[{"msg":"Instance deployment used the commands in your 'Procfile' to initiate startup of your application.","timestamp":1751200194475,"severity":"INFO"},{"msg":"Instance deployment completed successfully.","timestamp":1751200200790,"severity":"INFO"}]}]}

2025/06/29 12:30:00.792040 [INFO] Platform Engine finished execution on command: app-deploy

2025/06/29 12:31:03.862855 [INFO] Starting...
2025/06/29 12:31:03.862906 [INFO] Starting EBPlatform-PlatformEngine
2025/06/29 12:31:03.862942 [INFO] reading event message file
2025/06/29 12:31:03.867079 [INFO] Engine received EB command cfn-hup-exec

2025/06/29 12:31:03.943163 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 -r AWSEBAutoScalingGroup --region us-west-2
2025/06/29 12:31:04.246293 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 -r AWSEBBeanstalkMetadata --region us-west-2
2025/06/29 12:31:04.539671 [INFO] checking whether command bundle-log is applicable to this instance...
2025/06/29 12:31:04.539686 [INFO] this command is applicable to the instance, thus instance should execute command
2025/06/29 12:31:04.539689 [INFO] Engine command: (bundle-log)

2025/06/29 12:31:04.539749 [INFO] Executing instruction: GetBundleLogs
2025/06/29 12:31:04.539753 [INFO] Bundle Logs...
2025/06/29 12:31:04.622357 [INFO] Executing cleanup logic
2025/06/29 12:31:04.622440 [INFO] CommandService Response: {"status":"SUCCESS","api_version":"1.0","results":[{"status":"SUCCESS","msg":"Engine execution has succeeded.","returncode":0,"events":[{"msg":"Instance deployment completed successfully.","timestamp":1751200264622,"severity":"INFO"}]}]}

2025/06/29 12:31:04.622455 [INFO] Platform Engine finished execution on command: bundle-log

2025/06/29 12:36:48.571444 [INFO] Starting...
2025/06/29 12:36:48.571489 [INFO] Starting EBPlatform-PlatformEngine
2025/06/29 12:36:48.571520 [INFO] reading event message file
2025/06/29 12:36:48.571824 [INFO] Engine received EB command cfn-hup-exec

2025/06/29 12:36:48.636833 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 -r AWSEBAutoScalingGroup --region us-west-2
2025/06/29 12:36:48.925839 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-dptrdke822-stack/3b3de3b0-54de-11f0-a15a-0292084a4773 -r AWSEBBeanstalkMetadata --region us-west-2
2025/06/29 12:36:49.215038 [INFO] checking whether command bundle-log is applicable to this instance...
2025/06/29 12:36:49.215049 [INFO] this command is applicable to the instance, thus instance should execute command
2025/06/29 12:36:49.215052 [INFO] Engine command: (bundle-log)

2025/06/29 12:36:49.215091 [INFO] Executing instruction: GetBundleLogs
2025/06/29 12:36:49.215094 [INFO] Bundle Logs...
