2025/06/28 21:54:47.633641 [INFO] Starting...
2025/06/28 21:54:47.633699 [INFO] Starting EBPlatform-PlatformEngine
2025/06/28 21:54:47.633781 [INFO] reading event message file
2025/06/28 21:54:47.634100 [INFO] Engine received EB command userdata-exec

2025/06/28 21:54:47.641494 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-k4qmvnc3m7-stack/6540d6a0-546a-11f0-847c-0ab3c89e0719 -r AWSEBAutoScalingGroup --region us-west-2
2025/06/28 21:54:47.933024 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-k4qmvnc3m7-stack/6540d6a0-546a-11f0-847c-0ab3c89e0719 -r AWSEBBeanstalkMetadata --region us-west-2
2025/06/28 21:54:48.266011 [INFO] This is a workflow controlled instance.
2025/06/28 21:54:48.266107 [INFO] Engine command: (env-launch)

2025/06/28 21:54:48.266524 [INFO] Executing instruction: SyncClock
2025/06/28 21:54:48.266529 [INFO] Starting SyncClock
2025/06/28 21:54:48.266538 [INFO] Running command: /usr/bin/chronyc tracking
2025/06/28 21:54:48.270055 [INFO] Reference ID    : 00000000 ()
Stratum         : 0
Ref time (UTC)  : Thu Jan 01 00:00:00 1970
System time     : 0.000000000 seconds slow of NTP time
Last offset     : +0.000000000 seconds
RMS offset      : 0.000000000 seconds
Frequency       : 4.195 ppm slow
Residual freq   : +0.000 ppm
Skew            : 0.000 ppm
Root delay      : 1.000000000 seconds
Root dispersion : 1.000000000 seconds
Update interval : 0.0 seconds
Leap status     : Not synchronised

2025/06/28 21:54:48.270069 [INFO] Running command: /usr/bin/chronyc -a makestep
2025/06/28 21:54:48.272699 [INFO] 200 OK

2025/06/28 21:54:48.272752 [INFO] Skipping Configure OS
2025/06/28 21:54:48.272759 [INFO] Skipping LockRepo
2025/06/28 21:54:48.272765 [INFO] Skipping GenerateEBBanner
2025/06/28 21:54:48.272826 [INFO] Skipping Install Process Manager
2025/06/28 21:54:48.272833 [INFO] Skipping install syslog
2025/06/28 21:54:48.272839 [INFO] Skipping install cron
2025/06/28 21:54:48.272843 [INFO] Skipping install proxy
2025/06/28 21:54:48.272851 [INFO] Skipping installhealthd
2025/06/28 21:54:48.272856 [INFO] Skipping Install Log Streaming Manager
2025/06/28 21:54:48.272862 [INFO] Skipping install X-Ray
2025/06/28 21:54:48.272867 [INFO] Skipping install Third Party License
2025/06/28 21:54:48.272872 [INFO] Skipping install httpd
2025/06/28 21:54:48.272879 [INFO] Instance has NOT been bootstrapped
2025/06/28 21:54:48.272882 [INFO] Executing instruction: installSqsd
2025/06/28 21:54:48.272886 [INFO] This is a web server environment instance, skip install sqsd daemon ...
2025/06/28 21:54:48.272891 [INFO] Instance has NOT been bootstrapped
2025/06/28 21:54:48.272894 [INFO] Executing instruction: bootstraphealthd
2025/06/28 21:54:48.272898 [INFO] this is an enhanced health env ...
2025/06/28 21:54:48.272910 [INFO] bootstrap healthd....
2025/06/28 21:54:48.272922 [INFO] Running command: /usr/bin/id -u healthd || /usr/sbin/useradd --user-group healthd -s /sbin/nologin --create-home
2025/06/28 21:54:48.483221 [INFO] /usr/bin/id: ‘healthd’: no such user

2025/06/28 21:54:48.483643 [INFO] bootstrap healthd....
2025/06/28 21:54:48.483669 [INFO] Running command: /usr/bin/id -u healthd || /usr/sbin/useradd --user-group healthd -s /sbin/nologin --create-home
2025/06/28 21:54:48.486678 [INFO] 1001

2025/06/28 21:54:48.486956 [INFO] configure bundle log for healthd...
2025/06/28 21:54:48.487015 [INFO] Executing instruction: GetSetupProxyLog
2025/06/28 21:54:48.487081 [INFO] Skipping Install yum packages
2025/06/28 21:54:48.487085 [INFO] Skipping Install Python Bundle
2025/06/28 21:54:48.487089 [INFO] Skipping Configure Python site-packages
2025/06/28 21:54:48.487093 [INFO] Skipping Install Python Modules
2025/06/28 21:54:48.487097 [INFO] Skipping MarkBaked
2025/06/28 21:54:48.487102 [INFO] Instance has NOT been bootstrapped
2025/06/28 21:54:48.487104 [INFO] Executing instruction: TuneSystemSettings
2025/06/28 21:54:48.487106 [INFO] Starting TuneSystemSettings
2025/06/28 21:54:48.487109 [INFO] Instance has NOT been bootstrapped
2025/06/28 21:54:48.488494 [INFO] Executing instruction: GetSetupLogRotate
2025/06/28 21:54:48.488500 [INFO] Initialize LogRotate files and directories
2025/06/28 21:54:48.516537 [INFO] Instance has NOT been bootstrapped
2025/06/28 21:54:48.516548 [INFO] Executing instruction: BootstrapCFNHup
2025/06/28 21:54:48.516551 [INFO] Bootstrap cfn-hup
2025/06/28 21:54:48.518777 [INFO] Copying file /opt/elasticbeanstalk/config/private/aws-eb-command-handler.conf to /etc/cfn/hooks.d/aws-eb-command-handler.conf
2025/06/28 21:54:48.520904 [INFO] Executing instruction: StartCFNHup
2025/06/28 21:54:48.520911 [INFO] Start cfn-hup
2025/06/28 21:54:48.520924 [INFO] Running command: systemctl show -p PartOf cfn-hup.service
2025/06/28 21:54:48.534747 [INFO] cfn-hup is not registered with EB yet, registering it now
2025/06/28 21:54:48.534781 [INFO] Running command: systemctl show -p PartOf cfn-hup.service
2025/06/28 21:54:48.549328 [INFO] Running command: systemctl daemon-reload
2025/06/28 21:54:48.740936 [INFO] Running command: systemctl reset-failed
2025/06/28 21:54:48.752430 [INFO] Running command: systemctl is-enabled aws-eb.target
2025/06/28 21:54:48.761012 [INFO] Running command: systemctl enable aws-eb.target
2025/06/28 21:54:49.006245 [INFO] Running command: systemctl start aws-eb.target
2025/06/28 21:54:49.015639 [INFO] Running command: systemctl enable cfn-hup.service
2025/06/28 21:54:49.265163 [INFO] Created symlink /etc/systemd/system/multi-user.target.wants/cfn-hup.service → /etc/systemd/system/cfn-hup.service.

2025/06/28 21:54:49.265195 [INFO] Running command: systemctl is-active cfn-hup.service
2025/06/28 21:54:49.273745 [INFO] cfn-hup process is not running, starting it now
2025/06/28 21:54:49.273772 [INFO] Running command: systemctl show -p PartOf cfn-hup.service
2025/06/28 21:54:49.283966 [INFO] Running command: systemctl is-active cfn-hup.service
2025/06/28 21:54:49.291552 [INFO] Running command: systemctl start cfn-hup.service
2025/06/28 21:54:49.327992 [INFO] Instance has NOT been bootstrapped
2025/06/28 21:54:49.328009 [INFO] Executing instruction: SetupPublishLogCronjob
2025/06/28 21:54:49.328013 [INFO] Setup publish logs cron job...
2025/06/28 21:54:49.328017 [INFO] Copying file /opt/elasticbeanstalk/config/private/logtasks/cron/publishlogs to /etc/cron.d/publishlogs
2025/06/28 21:54:49.330185 [INFO] Instance has NOT been bootstrapped
2025/06/28 21:54:49.330195 [INFO] Executing instruction: MarkBootstrapped
2025/06/28 21:54:49.330199 [INFO] Starting MarkBootstrapped
2025/06/28 21:54:49.330204 [INFO] Instance has NOT been bootstrapped
2025/06/28 21:54:49.330283 [INFO] Marked instance as Bootstrapped
2025/06/28 21:54:49.330287 [INFO] Executing instruction: Save CFN Stack Info
2025/06/28 21:54:49.330344 [INFO] Executing cleanup logic
2025/06/28 21:54:49.330354 [INFO] Platform Engine finished execution on command: env-launch

2025/06/28 21:56:52.406625 [INFO] Starting...
2025/06/28 21:56:52.406667 [INFO] Starting EBPlatform-PlatformEngine
2025/06/28 21:56:52.406706 [INFO] reading event message file
2025/06/28 21:56:52.406996 [INFO] Engine received EB command cfn-hup-exec

2025/06/28 21:56:52.467703 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-k4qmvnc3m7-stack/6540d6a0-546a-11f0-847c-0ab3c89e0719 -r AWSEBAutoScalingGroup --region us-west-2
2025/06/28 21:56:52.756748 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-k4qmvnc3m7-stack/6540d6a0-546a-11f0-847c-0ab3c89e0719 -r AWSEBBeanstalkMetadata --region us-west-2
2025/06/28 21:56:53.035642 [INFO] checking whether command app-deploy is applicable to this instance...
2025/06/28 21:56:53.035658 [INFO] this command is applicable to the instance, thus instance should execute command
2025/06/28 21:56:53.035661 [INFO] Engine command: (app-deploy)

2025/06/28 21:56:53.035665 [INFO] Downloading EB Application...
2025/06/28 21:56:53.035667 [INFO] Region: us-west-2
2025/06/28 21:56:53.035669 [INFO] envID: e-k4qmvnc3m7
2025/06/28 21:56:53.035676 [INFO] envBucket: elasticbeanstalk-us-west-2-************
2025/06/28 21:56:53.035678 [INFO] accountID: ************
2025/06/28 21:56:53.035681 [INFO] Using manifest file name from command request
2025/06/28 21:56:53.035686 [INFO] Unable to get version manifest file.
2025/06/28 21:56:53.035688 [INFO] Downloading latest manifest available.
2025/06/28 21:56:53.035690 [INFO] Download latest app version manifest
2025/06/28 21:56:53.035820 [INFO] resources/environments/e-k4qmvnc3m7/_runtime/versions/manifest
2025/06/28 21:56:53.074484 [INFO] latestManifest key *: resources/environments/e-k4qmvnc3m7/_runtime/versions/manifest_1751147638496

2025/06/28 21:56:53.074712 [INFO] Downloading: bucket: elasticbeanstalk-us-west-2-************, object: resources/environments/e-k4qmvnc3m7/_runtime/versions/manifest_1751147638496, expected bucket owner: ************
2025/06/28 21:56:53.106237 [INFO] Download successful157bytes downloaded
2025/06/28 21:56:53.106359 [INFO] Trying to read and parse version manifest...
2025/06/28 21:56:53.106610 [INFO] Downloading: bucket: elasticbeanstalk-us-west-2-************, object: resources/environments/e-k4qmvnc3m7/_runtime/_versions/winmemory-processing-api/app-250628_225347922060, expected bucket owner: ************
2025/06/28 21:56:53.131317 [INFO] Download successful2703bytes downloaded
2025/06/28 21:56:53.131586 [INFO] Executing instruction: ElectLeader
2025/06/28 21:56:53.131594 [INFO] Running leader election for instance i-08b2210ccd2afe431...
2025/06/28 21:56:53.131597 [INFO] Calling the cfn-elect-cmd-leader to elect the command leader.
2025/06/28 21:56:53.131607 [INFO] Running command: /opt/aws/bin/cfn-elect-cmd-leader --stack arn:aws:cloudformation:us-west-2:************:stack/awseb-e-k4qmvnc3m7-stack/6540d6a0-546a-11f0-847c-0ab3c89e0719 --command-name ElasticBeanstalkCommand-AWSEBAutoScalingGroup --invocation-id 79379506-01cb-499b-a074-59fadca7c01c --listener-id i-08b2210ccd2afe431 --region us-west-2
2025/06/28 21:56:53.422738 [INFO] Instance is Leader.
2025/06/28 21:56:53.422800 [INFO] Executing instruction: stopSqsd
2025/06/28 21:56:53.422807 [INFO] This is a web server environment instance, skip stop sqsd daemon ...
2025/06/28 21:56:53.422810 [INFO] Executing instruction: PreBuildEbExtension
2025/06/28 21:56:53.422813 [INFO] Starting executing the config set Infra-EmbeddedPreBuild.
2025/06/28 21:56:53.422826 [INFO] Running command: /opt/aws/bin/cfn-init -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-k4qmvnc3m7-stack/6540d6a0-546a-11f0-847c-0ab3c89e0719 -r AWSEBAutoScalingGroup --region us-west-2 --configsets Infra-EmbeddedPreBuild
2025/06/28 21:57:11.540400 [INFO] Finished executing the config set Infra-EmbeddedPreBuild.

2025/06/28 21:57:11.540845 [INFO] Executing instruction: StageApplication
2025/06/28 21:57:11.540851 [INFO] Recreating /var/app/staging/
2025/06/28 21:57:11.541899 [INFO] extracting /opt/elasticbeanstalk/deployment/app_source_bundle to /var/app/staging/
2025/06/28 21:57:11.541919 [INFO] Running command: /usr/bin/unzip -q -o /opt/elasticbeanstalk/deployment/app_source_bundle -d /var/app/staging/
2025/06/28 21:57:11.547729 [INFO] finished extracting /opt/elasticbeanstalk/deployment/app_source_bundle to /var/app/staging/ successfully
2025/06/28 21:57:11.548398 [INFO] Executing instruction: RunAppDeployPreBuildHooks
2025/06/28 21:57:11.548492 [INFO] Executing platform hooks in .platform/hooks/prebuild/
2025/06/28 21:57:11.548510 [INFO] The dir .platform/hooks/prebuild/ does not exist
2025/06/28 21:57:11.548512 [INFO] Finished running scripts in /var/app/staging/.platform/hooks/prebuild
2025/06/28 21:57:11.548517 [INFO] Executing instruction: InstallDependency
2025/06/28 21:57:11.548522 [INFO] checking dependencies file
2025/06/28 21:57:11.548533 [INFO] Installing dependencies with requirements.txt by using Pip
2025/06/28 21:57:11.548543 [INFO] Running command: /var/app/venv/staging-LQM1lest/bin/pip install -r requirements.txt
2025/06/28 21:57:18.930733 [INFO] Collecting flask
  Downloading flask-3.1.1-py3-none-any.whl (103 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 103.3/103.3 kB 5.3 MB/s eta 0:00:00
Collecting opencv-python
  Downloading opencv_python-*********-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (63.0 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 63.0/63.0 MB 37.3 MB/s eta 0:00:00
Collecting numpy
  Downloading numpy-2.3.1-cp311-cp311-manylinux_2_28_x86_64.whl (16.9 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 16.9/16.9 MB 100.3 MB/s eta 0:00:00
Collecting flask-cors
  Downloading flask_cors-6.0.1-py3-none-any.whl (13 kB)
Collecting blinker>=1.9.0
  Downloading blinker-1.9.0-py3-none-any.whl (8.5 kB)
Collecting click>=8.1.3
  Downloading click-8.2.1-py3-none-any.whl (102 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 102.2/102.2 kB 33.2 MB/s eta 0:00:00
Collecting itsdangerous>=2.2.0
  Downloading itsdangerous-2.2.0-py3-none-any.whl (16 kB)
Collecting jinja2>=3.1.2
  Downloading jinja2-3.1.6-py3-none-any.whl (134 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 134.9/134.9 kB 43.6 MB/s eta 0:00:00
Collecting markupsafe>=2.1.1
  Downloading MarkupSafe-3.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (23 kB)
Collecting werkzeug>=3.1.0
  Downloading werkzeug-3.1.3-py3-none-any.whl (224 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 224.5/224.5 kB 63.7 MB/s eta 0:00:00
Installing collected packages: numpy, markupsafe, itsdangerous, click, blinker, werkzeug, opencv-python, jinja2, flask, flask-cors
Successfully installed blinker-1.9.0 click-8.2.1 flask-3.1.1 flask-cors-6.0.1 itsdangerous-2.2.0 jinja2-3.1.6 markupsafe-3.0.2 numpy-2.3.1 opencv-python-********* werkzeug-3.1.3

2025/06/28 21:57:18.931362 [INFO] 
[notice] A new release of pip available: 22.3.1 -> 25.1.1
[notice] To update, run: python3.11 -m pip install --upgrade pip

2025/06/28 21:57:18.931371 [INFO] Executing instruction: check Procfile
2025/06/28 21:57:18.931428 [INFO] detected Procfile in application source bundle ...
2025/06/28 21:57:18.931437 [INFO] Executing instruction: configure X-Ray
2025/06/28 21:57:18.931441 [INFO] X-Ray is not enabled.
2025/06/28 21:57:18.931444 [INFO] Executing instruction: configure proxy server
2025/06/28 21:57:18.931458 [INFO] Recreating /var/proxy/staging/nginx
2025/06/28 21:57:18.936192 [INFO] Running command: cp -rp /var/app/staging/.platform/nginx/. /var/proxy/staging/nginx
2025/06/28 21:57:18.948851 [INFO] Executing instruction: configure healthd specific proxy conf
2025/06/28 21:57:18.951802 [INFO] Running command: systemctl show -p PartOf healthd.service
2025/06/28 21:57:18.966073 [INFO] Running command: systemctl daemon-reload
2025/06/28 21:57:19.221139 [INFO] Running command: systemctl reset-failed
2025/06/28 21:57:19.230451 [INFO] Running command: systemctl is-enabled aws-eb.target
2025/06/28 21:57:19.238488 [INFO] Running command: systemctl enable aws-eb.target
2025/06/28 21:57:19.515936 [INFO] Running command: systemctl start aws-eb.target
2025/06/28 21:57:19.524567 [INFO] Running command: systemctl enable healthd.service
2025/06/28 21:57:19.772516 [INFO] Created symlink /etc/systemd/system/multi-user.target.wants/healthd.service → /etc/systemd/system/healthd.service.

2025/06/28 21:57:19.772552 [INFO] Running command: systemctl show -p PartOf healthd.service
2025/06/28 21:57:19.783227 [INFO] Running command: systemctl is-active healthd.service
2025/06/28 21:57:19.790970 [INFO] Running command: systemctl start healthd.service
2025/06/28 21:57:19.834024 [INFO] Copying file /opt/elasticbeanstalk/config/private/healthd/healthd_logformat.conf to /var/proxy/staging/nginx/conf.d/healthd_logformat.conf
2025/06/28 21:57:19.835222 [INFO] Copying file /opt/elasticbeanstalk/config/private/healthd/healthd_nginx.conf to /var/proxy/staging/nginx/conf.d/elasticbeanstalk/healthd.conf
2025/06/28 21:57:19.836386 [INFO] Executing instruction: configure log streaming
2025/06/28 21:57:19.836397 [INFO] log streaming is not enabled
2025/06/28 21:57:19.836399 [INFO] disable log stream
2025/06/28 21:57:19.836409 [INFO] Running command: systemctl show -p PartOf amazon-cloudwatch-agent.service
2025/06/28 21:57:19.850435 [INFO] Running command: systemctl stop amazon-cloudwatch-agent.service
2025/06/28 21:57:19.865433 [INFO] Executing instruction: GetToggleForceRotate
2025/06/28 21:57:19.865458 [INFO] Checking if logs need forced rotation
2025/06/28 21:57:19.865477 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-k4qmvnc3m7-stack/6540d6a0-546a-11f0-847c-0ab3c89e0719 -r AWSEBAutoScalingGroup --region us-west-2
2025/06/28 21:57:20.191056 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-k4qmvnc3m7-stack/6540d6a0-546a-11f0-847c-0ab3c89e0719 -r AWSEBBeanstalkMetadata --region us-west-2
2025/06/28 21:57:20.486188 [INFO] Generating rsyslog config from Procfile
2025/06/28 21:57:20.489205 [INFO] Running command: systemctl restart rsyslog.service
2025/06/28 21:57:20.981280 [INFO] Executing instruction: PostBuildEbExtension
2025/06/28 21:57:20.981302 [INFO] Starting executing the config set Infra-EmbeddedPostBuild.
2025/06/28 21:57:20.981313 [INFO] Running command: /opt/aws/bin/cfn-init -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-k4qmvnc3m7-stack/6540d6a0-546a-11f0-847c-0ab3c89e0719 -r AWSEBAutoScalingGroup --region us-west-2 --configsets Infra-EmbeddedPostBuild
2025/06/28 21:57:21.298153 [INFO] Finished executing the config set Infra-EmbeddedPostBuild.

2025/06/28 21:57:21.298189 [INFO] Executing instruction: CleanEbExtensions
2025/06/28 21:57:21.298359 [INFO] Cleaned ebextensions subdirectories from app staging directory.
2025/06/28 21:57:21.298363 [INFO] Executing instruction: RunAppDeployPreDeployHooks
2025/06/28 21:57:21.298410 [INFO] Executing platform hooks in .platform/hooks/predeploy/
2025/06/28 21:57:21.298424 [INFO] The dir .platform/hooks/predeploy/ does not exist
2025/06/28 21:57:21.298426 [INFO] Finished running scripts in /var/app/staging/.platform/hooks/predeploy
2025/06/28 21:57:21.298431 [INFO] Executing instruction: stop X-Ray
2025/06/28 21:57:21.298433 [INFO] stop X-Ray ...
2025/06/28 21:57:21.298443 [INFO] Running command: systemctl show -p PartOf xray.service
2025/06/28 21:57:21.312735 [WARN] stopProcess Warning: process xray is not registered 
2025/06/28 21:57:21.312793 [INFO] Running command: systemctl stop xray.service
2025/06/28 21:57:21.324997 [INFO] Executing instruction: stop proxy
2025/06/28 21:57:21.325029 [INFO] Running command: systemctl show -p PartOf httpd.service
2025/06/28 21:57:21.347092 [WARN] deregisterProcess Warning: process httpd is not registered, skipping...

2025/06/28 21:57:21.347127 [INFO] Running command: systemctl show -p PartOf nginx.service
2025/06/28 21:57:21.360319 [WARN] deregisterProcess Warning: process nginx is not registered, skipping...

2025/06/28 21:57:21.360343 [INFO] Executing instruction: FlipApplication
2025/06/28 21:57:21.360377 [INFO] Fetching environment variables...
2025/06/28 21:57:21.360502 [INFO] Purge old process...
2025/06/28 21:57:21.360516 [INFO] Removing /var/app/current/ if it exists
2025/06/28 21:57:21.360524 [INFO] Renaming /var/app/staging/ to /var/app/current/
2025/06/28 21:57:21.360541 [INFO] Register application processes...
2025/06/28 21:57:21.360543 [INFO] Registering the proc: web

2025/06/28 21:57:21.360556 [INFO] Running command: systemctl show -p PartOf web.service
2025/06/28 21:57:21.372868 [INFO] Running command: systemctl daemon-reload
2025/06/28 21:57:21.627347 [INFO] Running command: systemctl reset-failed
2025/06/28 21:57:21.635945 [INFO] Running command: systemctl is-enabled eb-app.target
2025/06/28 21:57:21.643116 [INFO] Copying file /opt/elasticbeanstalk/config/private/aws-eb.target to /etc/systemd/system/eb-app.target
2025/06/28 21:57:21.644982 [INFO] Running command: systemctl enable eb-app.target
2025/06/28 21:57:21.897416 [INFO] Created symlink /etc/systemd/system/multi-user.target.wants/eb-app.target → /etc/systemd/system/eb-app.target.

2025/06/28 21:57:21.897448 [INFO] Running command: systemctl start eb-app.target
2025/06/28 21:57:21.907422 [INFO] Running command: systemctl enable web.service
2025/06/28 21:57:22.185851 [INFO] Created symlink /etc/systemd/system/multi-user.target.wants/web.service → /etc/systemd/system/web.service.

2025/06/28 21:57:22.185882 [INFO] Running command: systemctl show -p PartOf web.service
2025/06/28 21:57:22.196951 [INFO] Running command: systemctl is-active web.service
2025/06/28 21:57:22.204520 [INFO] Running command: systemctl start web.service
2025/06/28 21:57:22.286008 [INFO] Executing instruction: start X-Ray
2025/06/28 21:57:22.286028 [INFO] X-Ray is not enabled.
2025/06/28 21:57:22.286033 [INFO] Executing instruction: start proxy with new configuration
2025/06/28 21:57:22.286055 [INFO] Running command: /usr/sbin/nginx -t -c /var/proxy/staging/nginx/nginx.conf
2025/06/28 21:57:22.326304 [INFO] nginx: [warn] could not build optimal types_hash, you should increase either types_hash_max_size: 1024 or types_hash_bucket_size: 64; ignoring types_hash_bucket_size
nginx: the configuration file /var/proxy/staging/nginx/nginx.conf syntax is ok
nginx: configuration file /var/proxy/staging/nginx/nginx.conf test is successful

2025/06/28 21:57:22.326493 [INFO] Running command: cp -rp /var/proxy/staging/nginx/* /etc/nginx
2025/06/28 21:57:22.329128 [INFO] Running command: systemctl show -p PartOf nginx.service
2025/06/28 21:57:22.344049 [INFO] Running command: systemctl daemon-reload
2025/06/28 21:57:22.579917 [INFO] Running command: systemctl reset-failed
2025/06/28 21:57:22.588290 [INFO] Running command: systemctl show -p PartOf nginx.service
2025/06/28 21:57:22.600649 [INFO] Running command: systemctl is-active nginx.service
2025/06/28 21:57:22.702965 [INFO] Running command: systemctl start nginx.service
2025/06/28 21:57:22.780802 [INFO] Executing instruction: configureSqsd
2025/06/28 21:57:22.780816 [INFO] This is a web server environment instance, skip configure sqsd daemon ...
2025/06/28 21:57:22.780819 [INFO] Executing instruction: startSqsd
2025/06/28 21:57:22.780821 [INFO] This is a web server environment instance, skip start sqsd daemon ...
2025/06/28 21:57:22.780824 [INFO] Executing instruction: Track pids in healthd
2025/06/28 21:57:22.780826 [INFO] This is an enhanced health env...
2025/06/28 21:57:22.780837 [INFO] Running command: systemctl show -p ConsistsOf aws-eb.target | cut -d= -f2
2025/06/28 21:57:22.790622 [INFO] nginx.service healthd.service cfn-hup.service

2025/06/28 21:57:22.790651 [INFO] Running command: systemctl show -p ConsistsOf eb-app.target | cut -d= -f2
2025/06/28 21:57:22.800553 [INFO] web.service

2025/06/28 21:57:22.800625 [WARN] failed to read file /var/pids/web.pid with error: open /var/pids/web.pid: no such file or directory
2025/06/28 21:57:23.800722 [WARN] failed to read file /var/pids/web.pid with error: open /var/pids/web.pid: no such file or directory
2025/06/28 21:57:25.801850 [WARN] failed to read file /var/pids/web.pid with error: open /var/pids/web.pid: no such file or directory
2025/06/28 21:57:28.802071 [WARN] failed to read file /var/pids/web.pid with error: open /var/pids/web.pid: no such file or directory
2025/06/28 21:57:32.805191 [WARN] failed to read file /var/pids/web.pid with error: open /var/pids/web.pid: no such file or directory
2025/06/28 21:57:37.809311 [WARN] failed to read file /var/pids/web.pid with error: open /var/pids/web.pid: no such file or directory
2025/06/28 21:57:37.809361 [ERROR] update processes [web nginx healthd cfn-hup] pid symlinks failed with error failed to read file /var/pids/web.pid after 6 attempts
2025/06/28 21:57:37.809372 [ERROR] An error occurred during execution of command [app-deploy] - [Track pids in healthd]. Stop running the command. Error: update processes [web nginx healthd cfn-hup] pid symlinks failed with error failed to read file /var/pids/web.pid after 6 attempts 

2025/06/28 21:57:37.809382 [INFO] Executing cleanup logic
2025/06/28 21:57:37.809492 [INFO] CommandService Response: {"status":"FAILURE","api_version":"1.0","results":[{"status":"FAILURE","msg":"Engine execution has encountered an error.","returncode":1,"events":[{"msg":"Instance deployment used the commands in your 'Procfile' to initiate startup of your application.","timestamp":1751147838931,"severity":"INFO"},{"msg":"Instance deployment failed. For details, see 'eb-engine.log'.","timestamp":1751147857809,"severity":"ERROR"}]}]}

2025/06/28 21:57:37.809718 [INFO] Platform Engine finished execution on command: app-deploy

2025/06/29 11:34:54.298518 [INFO] Starting...
2025/06/29 11:34:54.298567 [INFO] Starting EBPlatform-PlatformEngine
2025/06/29 11:34:54.298605 [INFO] reading event message file
2025/06/29 11:34:54.298935 [INFO] Engine received EB command cfn-hup-exec

2025/06/29 11:34:54.362845 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-k4qmvnc3m7-stack/6540d6a0-546a-11f0-847c-0ab3c89e0719 -r AWSEBAutoScalingGroup --region us-west-2
2025/06/29 11:34:54.646189 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-k4qmvnc3m7-stack/6540d6a0-546a-11f0-847c-0ab3c89e0719 -r AWSEBBeanstalkMetadata --region us-west-2
2025/06/29 11:34:54.929439 [INFO] checking whether command app-deploy is applicable to this instance...
2025/06/29 11:34:54.929451 [INFO] this command is applicable to the instance, thus instance should execute command
2025/06/29 11:34:54.929453 [INFO] Engine command: (app-deploy)

2025/06/29 11:34:54.929457 [INFO] Downloading EB Application...
2025/06/29 11:34:54.929459 [INFO] Region: us-west-2
2025/06/29 11:34:54.929461 [INFO] envID: e-k4qmvnc3m7
2025/06/29 11:34:54.929463 [INFO] envBucket: elasticbeanstalk-us-west-2-************
2025/06/29 11:34:54.929465 [INFO] accountID: ************
2025/06/29 11:34:54.929467 [INFO] Using manifest file name from command request
2025/06/29 11:34:54.929472 [INFO] Manifest name is : manifest_1751196892052
2025/06/29 11:34:54.929474 [INFO] Download app version manifest
2025/06/29 11:34:54.929730 [INFO] Downloading: bucket: elasticbeanstalk-us-west-2-************, object: resources/environments/e-k4qmvnc3m7/_runtime/versions/manifest_1751196892052, expected bucket owner: ************
2025/06/29 11:34:54.974256 [INFO] Download successful142bytes downloaded
2025/06/29 11:34:54.974377 [INFO] Trying to read and parse version manifest...
2025/06/29 11:34:54.974442 [INFO] Downloading: bucket: elasticbeanstalk-us-west-2-************, object: resources/environments/e-k4qmvnc3m7/_runtime/_versions/winmemory-processing-api/app-250629_123446983860-stage-250629_123446983910, expected bucket owner: ************
2025/06/29 11:34:54.994287 [INFO] Download successful2702bytes downloaded
2025/06/29 11:34:54.994531 [INFO] Executing instruction: ElectLeader
2025/06/29 11:34:54.994536 [INFO] Running leader election for instance i-08b2210ccd2afe431...
2025/06/29 11:34:54.994539 [INFO] Calling the cfn-elect-cmd-leader to elect the command leader.
2025/06/29 11:34:54.994553 [INFO] Running command: /opt/aws/bin/cfn-elect-cmd-leader --stack arn:aws:cloudformation:us-west-2:************:stack/awseb-e-k4qmvnc3m7-stack/6540d6a0-546a-11f0-847c-0ab3c89e0719 --command-name ElasticBeanstalkCommand-AWSEBAutoScalingGroup --invocation-id f7815aed-0c66-4696-89a5-fe936c910699 --listener-id i-08b2210ccd2afe431 --region us-west-2
2025/06/29 11:34:55.288625 [INFO] Instance is Leader.
2025/06/29 11:34:55.288671 [INFO] Executing instruction: stopSqsd
2025/06/29 11:34:55.288675 [INFO] This is a web server environment instance, skip stop sqsd daemon ...
2025/06/29 11:34:55.288678 [INFO] Executing instruction: PreBuildEbExtension
2025/06/29 11:34:55.288681 [INFO] Starting executing the config set Infra-EmbeddedPreBuild.
2025/06/29 11:34:55.288691 [INFO] Running command: /opt/aws/bin/cfn-init -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-k4qmvnc3m7-stack/6540d6a0-546a-11f0-847c-0ab3c89e0719 -r AWSEBAutoScalingGroup --region us-west-2 --configsets Infra-EmbeddedPreBuild
2025/06/29 11:35:07.170119 [INFO] Finished executing the config set Infra-EmbeddedPreBuild.

2025/06/29 11:35:07.170189 [INFO] Executing instruction: StageApplication
2025/06/29 11:35:07.170192 [INFO] Recreating /var/app/staging/
2025/06/29 11:35:07.170423 [INFO] extracting /opt/elasticbeanstalk/deployment/app_source_bundle to /var/app/staging/
2025/06/29 11:35:07.170438 [INFO] Running command: /usr/bin/unzip -q -o /opt/elasticbeanstalk/deployment/app_source_bundle -d /var/app/staging/
2025/06/29 11:35:07.174865 [INFO] finished extracting /opt/elasticbeanstalk/deployment/app_source_bundle to /var/app/staging/ successfully
2025/06/29 11:35:07.176109 [INFO] Executing instruction: RunAppDeployPreBuildHooks
2025/06/29 11:35:07.176132 [INFO] Executing platform hooks in .platform/hooks/prebuild/
2025/06/29 11:35:07.176146 [INFO] The dir .platform/hooks/prebuild/ does not exist
2025/06/29 11:35:07.176148 [INFO] Finished running scripts in /var/app/staging/.platform/hooks/prebuild
2025/06/29 11:35:07.176153 [INFO] Executing instruction: InstallDependency
2025/06/29 11:35:07.176157 [INFO] checking dependencies file
2025/06/29 11:35:07.176171 [INFO] Installing dependencies with requirements.txt by using Pip
2025/06/29 11:35:07.176179 [INFO] Running command: /var/app/venv/staging-LQM1lest/bin/pip install -r requirements.txt
2025/06/29 11:35:08.422750 [INFO] Requirement already satisfied: flask in /var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages (from -r requirements.txt (line 1)) (3.1.1)
Requirement already satisfied: opencv-python in /var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages (from -r requirements.txt (line 2)) (*********)
Requirement already satisfied: numpy in /var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages (from -r requirements.txt (line 3)) (2.3.1)
Requirement already satisfied: flask-cors in /var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages (from -r requirements.txt (line 4)) (6.0.1)
Requirement already satisfied: blinker>=1.9.0 in /var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages (from flask->-r requirements.txt (line 1)) (1.9.0)
Requirement already satisfied: click>=8.1.3 in /var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages (from flask->-r requirements.txt (line 1)) (8.2.1)
Requirement already satisfied: itsdangerous>=2.2.0 in /var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages (from flask->-r requirements.txt (line 1)) (2.2.0)
Requirement already satisfied: jinja2>=3.1.2 in /var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages (from flask->-r requirements.txt (line 1)) (3.1.6)
Requirement already satisfied: markupsafe>=2.1.1 in /var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages (from flask->-r requirements.txt (line 1)) (3.0.2)
Requirement already satisfied: werkzeug>=3.1.0 in /var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages (from flask->-r requirements.txt (line 1)) (3.1.3)

2025/06/29 11:35:08.422772 [INFO] 
[notice] A new release of pip available: 22.3.1 -> 25.1.1
[notice] To update, run: python3.11 -m pip install --upgrade pip

2025/06/29 11:35:08.422778 [INFO] Executing instruction: check Procfile
2025/06/29 11:35:08.422832 [INFO] detected Procfile in application source bundle ...
2025/06/29 11:35:08.422838 [INFO] Executing instruction: configure X-Ray
2025/06/29 11:35:08.422841 [INFO] X-Ray is not enabled.
2025/06/29 11:35:08.422844 [INFO] Executing instruction: configure proxy server
2025/06/29 11:35:08.422858 [INFO] Recreating /var/proxy/staging/nginx
2025/06/29 11:35:08.428007 [INFO] Running command: cp -rp /var/app/staging/.platform/nginx/. /var/proxy/staging/nginx
2025/06/29 11:35:08.435437 [INFO] Executing instruction: configure healthd specific proxy conf
2025/06/29 11:35:08.438149 [INFO] Running command: systemctl show -p PartOf healthd.service
2025/06/29 11:35:08.452498 [WARN] Warning: process healthd is already registered...
Deregistering the process ...
2025/06/29 11:35:08.452531 [INFO] Running command: systemctl show -p PartOf healthd.service
2025/06/29 11:35:08.461435 [INFO] Running command: systemctl is-active healthd.service
2025/06/29 11:35:08.468340 [INFO] Running command: systemctl show -p PartOf healthd.service
2025/06/29 11:35:08.476836 [INFO] Running command: systemctl stop healthd.service
2025/06/29 11:35:08.582481 [INFO] Running command: systemctl disable healthd.service
2025/06/29 11:35:08.848147 [INFO] Removed "/etc/systemd/system/multi-user.target.wants/healthd.service".

2025/06/29 11:35:08.849082 [INFO] Running command: systemctl daemon-reload
2025/06/29 11:35:09.097826 [INFO] Running command: systemctl reset-failed
2025/06/29 11:35:09.108525 [INFO] Running command: systemctl daemon-reload
2025/06/29 11:35:09.356494 [INFO] Running command: systemctl reset-failed
2025/06/29 11:35:09.365072 [INFO] Running command: systemctl is-enabled aws-eb.target
2025/06/29 11:35:09.372773 [INFO] Running command: systemctl enable aws-eb.target
2025/06/29 11:35:09.646822 [INFO] Running command: systemctl start aws-eb.target
2025/06/29 11:35:09.655688 [INFO] Running command: systemctl enable healthd.service
2025/06/29 11:35:09.936441 [INFO] Created symlink /etc/systemd/system/multi-user.target.wants/healthd.service → /etc/systemd/system/healthd.service.

2025/06/29 11:35:09.936477 [INFO] Running command: systemctl show -p PartOf healthd.service
2025/06/29 11:35:09.947640 [INFO] Running command: systemctl is-active healthd.service
2025/06/29 11:35:09.955157 [INFO] Running command: systemctl start healthd.service
2025/06/29 11:35:10.007912 [INFO] Copying file /opt/elasticbeanstalk/config/private/healthd/healthd_logformat.conf to /var/proxy/staging/nginx/conf.d/healthd_logformat.conf
2025/06/29 11:35:10.008977 [INFO] Copying file /opt/elasticbeanstalk/config/private/healthd/healthd_nginx.conf to /var/proxy/staging/nginx/conf.d/elasticbeanstalk/healthd.conf
2025/06/29 11:35:10.010073 [INFO] Executing instruction: configure log streaming
2025/06/29 11:35:10.010083 [INFO] log streaming is not enabled
2025/06/29 11:35:10.010085 [INFO] disable log stream
2025/06/29 11:35:10.010097 [INFO] Running command: systemctl show -p PartOf amazon-cloudwatch-agent.service
2025/06/29 11:35:10.026741 [INFO] Running command: systemctl stop amazon-cloudwatch-agent.service
2025/06/29 11:35:10.042919 [INFO] Executing instruction: GetToggleForceRotate
2025/06/29 11:35:10.042944 [INFO] Checking if logs need forced rotation
2025/06/29 11:35:10.042961 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-k4qmvnc3m7-stack/6540d6a0-546a-11f0-847c-0ab3c89e0719 -r AWSEBAutoScalingGroup --region us-west-2
2025/06/29 11:35:10.441464 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-k4qmvnc3m7-stack/6540d6a0-546a-11f0-847c-0ab3c89e0719 -r AWSEBBeanstalkMetadata --region us-west-2
2025/06/29 11:35:10.851938 [INFO] Generating rsyslog config from Procfile
2025/06/29 11:35:10.856815 [INFO] Running command: systemctl restart rsyslog.service
2025/06/29 11:35:11.248058 [INFO] Executing instruction: PostBuildEbExtension
2025/06/29 11:35:11.248085 [INFO] Starting executing the config set Infra-EmbeddedPostBuild.
2025/06/29 11:35:11.248105 [INFO] Running command: /opt/aws/bin/cfn-init -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-k4qmvnc3m7-stack/6540d6a0-546a-11f0-847c-0ab3c89e0719 -r AWSEBAutoScalingGroup --region us-west-2 --configsets Infra-EmbeddedPostBuild
2025/06/29 11:35:11.554016 [INFO] Finished executing the config set Infra-EmbeddedPostBuild.

2025/06/29 11:35:11.554040 [INFO] Executing instruction: CleanEbExtensions
2025/06/29 11:35:11.554146 [INFO] Cleaned ebextensions subdirectories from app staging directory.
2025/06/29 11:35:11.554150 [INFO] Executing instruction: RunAppDeployPreDeployHooks
2025/06/29 11:35:11.554164 [INFO] Executing platform hooks in .platform/hooks/predeploy/
2025/06/29 11:35:11.554182 [INFO] The dir .platform/hooks/predeploy/ does not exist
2025/06/29 11:35:11.554185 [INFO] Finished running scripts in /var/app/staging/.platform/hooks/predeploy
2025/06/29 11:35:11.554189 [INFO] Executing instruction: stop X-Ray
2025/06/29 11:35:11.554191 [INFO] stop X-Ray ...
2025/06/29 11:35:11.554201 [INFO] Running command: systemctl show -p PartOf xray.service
2025/06/29 11:35:11.565759 [WARN] stopProcess Warning: process xray is not registered 
2025/06/29 11:35:11.565785 [INFO] Running command: systemctl stop xray.service
2025/06/29 11:35:11.577694 [INFO] Executing instruction: stop proxy
2025/06/29 11:35:11.577729 [INFO] Running command: systemctl show -p PartOf httpd.service
2025/06/29 11:35:11.592173 [WARN] deregisterProcess Warning: process httpd is not registered, skipping...

2025/06/29 11:35:11.592205 [INFO] Running command: systemctl show -p PartOf nginx.service
2025/06/29 11:35:11.603550 [INFO] Running command: systemctl is-active nginx.service
2025/06/29 11:35:11.610817 [INFO] Running command: systemctl show -p PartOf nginx.service
2025/06/29 11:35:11.619733 [INFO] Running command: systemctl stop nginx.service
2025/06/29 11:35:11.846517 [INFO] Running command: systemctl disable nginx.service
2025/06/29 11:35:12.098225 [INFO] Running command: systemctl daemon-reload
2025/06/29 11:35:12.343576 [INFO] Running command: systemctl reset-failed
2025/06/29 11:35:12.351818 [INFO] Executing instruction: FlipApplication
2025/06/29 11:35:12.351841 [INFO] Fetching environment variables...
2025/06/29 11:35:12.352099 [INFO] Purge old process...
2025/06/29 11:35:12.352118 [INFO] Running command: systemctl stop eb-app.target
2025/06/29 11:35:12.359908 [INFO] Running command: systemctl show -p ConsistsOf eb-app.target | cut -d= -f2
2025/06/29 11:35:12.369530 [INFO] web.service

2025/06/29 11:35:12.369552 [INFO] deregistering process: web
2025/06/29 11:35:12.369572 [INFO] Running command: systemctl show -p PartOf web.service
2025/06/29 11:35:12.379738 [INFO] Running command: systemctl is-active web.service
2025/06/29 11:35:12.387320 [INFO] Running command: systemctl disable web.service
2025/06/29 11:35:12.635189 [INFO] Removed "/etc/systemd/system/multi-user.target.wants/web.service".

2025/06/29 11:35:12.635279 [INFO] Running command: systemctl daemon-reload
2025/06/29 11:35:12.841040 [INFO] Running command: systemctl reset-failed
2025/06/29 11:35:12.848331 [INFO] Running command: systemctl is-active web.service
2025/06/29 11:35:12.855497 [INFO] Process web has been fully terminated
2025/06/29 11:35:12.855516 [INFO] All processes have been fully terminated
2025/06/29 11:35:12.855519 [INFO] Removing /var/app/current/ if it exists
2025/06/29 11:35:12.855662 [INFO] Renaming /var/app/staging/ to /var/app/current/
2025/06/29 11:35:12.855680 [INFO] Register application processes...
2025/06/29 11:35:12.855683 [INFO] Registering the proc: web

2025/06/29 11:35:12.855689 [INFO] Running command: systemctl show -p PartOf web.service
2025/06/29 11:35:12.866798 [INFO] Running command: systemctl daemon-reload
2025/06/29 11:35:13.115582 [INFO] Running command: systemctl reset-failed
2025/06/29 11:35:13.124330 [INFO] Running command: systemctl is-enabled eb-app.target
2025/06/29 11:35:13.132146 [INFO] Running command: systemctl enable eb-app.target
2025/06/29 11:35:13.362971 [INFO] Running command: systemctl start eb-app.target
2025/06/29 11:35:13.371841 [INFO] Running command: systemctl enable web.service
2025/06/29 11:35:13.616295 [INFO] Created symlink /etc/systemd/system/multi-user.target.wants/web.service → /etc/systemd/system/web.service.

2025/06/29 11:35:13.616331 [INFO] Running command: systemctl show -p PartOf web.service
2025/06/29 11:35:13.627354 [INFO] Running command: systemctl is-active web.service
2025/06/29 11:35:13.634715 [INFO] Running command: systemctl start web.service
2025/06/29 11:35:13.689458 [INFO] Executing instruction: start X-Ray
2025/06/29 11:35:13.689479 [INFO] X-Ray is not enabled.
2025/06/29 11:35:13.689484 [INFO] Executing instruction: start proxy with new configuration
2025/06/29 11:35:13.689525 [INFO] Running command: /usr/sbin/nginx -t -c /var/proxy/staging/nginx/nginx.conf
2025/06/29 11:35:13.716042 [INFO] nginx: [warn] could not build optimal types_hash, you should increase either types_hash_max_size: 1024 or types_hash_bucket_size: 64; ignoring types_hash_bucket_size
nginx: the configuration file /var/proxy/staging/nginx/nginx.conf syntax is ok
nginx: configuration file /var/proxy/staging/nginx/nginx.conf test is successful

2025/06/29 11:35:13.717032 [INFO] Running command: cp -rp /var/proxy/staging/nginx/* /etc/nginx
2025/06/29 11:35:13.721655 [INFO] Running command: systemctl show -p PartOf nginx.service
2025/06/29 11:35:13.745307 [INFO] Running command: systemctl daemon-reload
2025/06/29 11:35:14.075212 [INFO] Running command: systemctl reset-failed
2025/06/29 11:35:14.135885 [INFO] Running command: systemctl show -p PartOf nginx.service
2025/06/29 11:35:14.150203 [INFO] Running command: systemctl is-active nginx.service
2025/06/29 11:35:14.158491 [INFO] Running command: systemctl start nginx.service
2025/06/29 11:35:14.307469 [INFO] Executing instruction: configureSqsd
2025/06/29 11:35:14.307489 [INFO] This is a web server environment instance, skip configure sqsd daemon ...
2025/06/29 11:35:14.307493 [INFO] Executing instruction: startSqsd
2025/06/29 11:35:14.307496 [INFO] This is a web server environment instance, skip start sqsd daemon ...
2025/06/29 11:35:14.307500 [INFO] Executing instruction: Track pids in healthd
2025/06/29 11:35:14.307504 [INFO] This is an enhanced health env...
2025/06/29 11:35:14.307516 [INFO] Running command: systemctl show -p ConsistsOf aws-eb.target | cut -d= -f2
2025/06/29 11:35:14.323018 [INFO] cfn-hup.service healthd.service nginx.service

2025/06/29 11:35:14.323053 [INFO] Running command: systemctl show -p ConsistsOf eb-app.target | cut -d= -f2
2025/06/29 11:35:14.338740 [INFO] web.service

2025/06/29 11:35:14.339052 [INFO] Executing instruction: RunAppDeployPostDeployHooks
2025/06/29 11:35:14.339118 [INFO] Executing platform hooks in .platform/hooks/postdeploy/
2025/06/29 11:35:14.339138 [INFO] The dir .platform/hooks/postdeploy/ does not exist
2025/06/29 11:35:14.339141 [INFO] Finished running scripts in /var/app/current/.platform/hooks/postdeploy
2025/06/29 11:35:14.339147 [INFO] Executing cleanup logic
2025/06/29 11:35:14.339244 [INFO] CommandService Response: {"status":"SUCCESS","api_version":"1.0","results":[{"status":"SUCCESS","msg":"Engine execution has succeeded.","returncode":0,"events":[{"msg":"Instance deployment used the commands in your 'Procfile' to initiate startup of your application.","timestamp":1751196908422,"severity":"INFO"},{"msg":"Instance deployment completed successfully.","timestamp":1751196914339,"severity":"INFO"}]}]}

2025/06/29 11:35:14.339413 [INFO] Platform Engine finished execution on command: app-deploy

2025/06/29 11:35:38.535341 [INFO] Starting...
2025/06/29 11:35:38.535381 [INFO] Starting EBPlatform-PlatformEngine
2025/06/29 11:35:38.535414 [INFO] reading event message file
2025/06/29 11:35:38.535705 [INFO] Engine received EB command cfn-hup-exec

2025/06/29 11:35:38.594927 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-k4qmvnc3m7-stack/6540d6a0-546a-11f0-847c-0ab3c89e0719 -r AWSEBAutoScalingGroup --region us-west-2
2025/06/29 11:35:38.881751 [INFO] Running command: /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:************:stack/awseb-e-k4qmvnc3m7-stack/6540d6a0-546a-11f0-847c-0ab3c89e0719 -r AWSEBBeanstalkMetadata --region us-west-2
2025/06/29 11:35:39.176555 [INFO] checking whether command bundle-log is applicable to this instance...
2025/06/29 11:35:39.176566 [INFO] this command is applicable to the instance, thus instance should execute command
2025/06/29 11:35:39.176569 [INFO] Engine command: (bundle-log)

2025/06/29 11:35:39.176621 [INFO] Executing instruction: GetBundleLogs
2025/06/29 11:35:39.176624 [INFO] Bundle Logs...
