Jun 28 21:57:22 ip-172-31-36-197 web[2238]: [2025-06-28 21:57:22 +0000] [2238] [INFO] Starting gunicorn 23.0.0
Jun 28 21:57:22 ip-172-31-36-197 web[2238]: [2025-06-28 21:57:22 +0000] [2238] [INFO] Listening at: http://127.0.0.1:8000 (2238)
Jun 28 21:57:22 ip-172-31-36-197 web[2238]: [2025-06-28 21:57:22 +0000] [2238] [INFO] Using worker: sync
Jun 28 21:57:22 ip-172-31-36-197 web[2270]: [2025-06-28 21:57:22 +0000] [2270] [INFO] Booting worker with pid: 2270
Jun 28 21:57:22 ip-172-31-36-197 web[2270]: [2025-06-28 21:57:22 +0000] [2270] [ERROR] Exception in worker process
Jun 28 21:57:22 ip-172-31-36-197 web[2270]: Traceback (most recent call last):
Jun 28 21:57:22 ip-172-31-36-197 web[2270]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
Jun 28 21:57:22 ip-172-31-36-197 web[2270]:    worker.init_process()
Jun 28 21:57:22 ip-172-31-36-197 web[2270]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 135, in init_process
Jun 28 21:57:22 ip-172-31-36-197 web[2270]:    self.load_wsgi()
Jun 28 21:57:22 ip-172-31-36-197 web[2270]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
Jun 28 21:57:22 ip-172-31-36-197 web[2270]:    self.wsgi = self.app.wsgi()
Jun 28 21:57:22 ip-172-31-36-197 web[2270]:                ^^^^^^^^^^^^^^^
Jun 28 21:57:22 ip-172-31-36-197 web[2270]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/base.py", line 66, in wsgi
Jun 28 21:57:22 ip-172-31-36-197 web[2270]:    self.callable = self.load()
Jun 28 21:57:22 ip-172-31-36-197 web[2270]:                    ^^^^^^^^^^^
Jun 28 21:57:22 ip-172-31-36-197 web[2270]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
Jun 28 21:57:22 ip-172-31-36-197 web[2270]:    return self.load_wsgiapp()
Jun 28 21:57:22 ip-172-31-36-197 web[2270]:           ^^^^^^^^^^^^^^^^^^^
Jun 28 21:57:22 ip-172-31-36-197 web[2270]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
Jun 28 21:57:22 ip-172-31-36-197 web[2270]:    return util.import_app(self.app_uri)
Jun 28 21:57:22 ip-172-31-36-197 web[2270]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 28 21:57:22 ip-172-31-36-197 web[2270]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/util.py", line 370, in import_app
Jun 28 21:57:22 ip-172-31-36-197 web[2270]:    mod = importlib.import_module(module)
Jun 28 21:57:22 ip-172-31-36-197 web[2270]:          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 28 21:57:22 ip-172-31-36-197 web[2270]:  File "/usr/lib64/python3.11/importlib/__init__.py", line 126, in import_module
Jun 28 21:57:22 ip-172-31-36-197 web[2270]:    return _bootstrap._gcd_import(name[level:], package, level)
Jun 28 21:57:22 ip-172-31-36-197 web[2270]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 28 21:57:22 ip-172-31-36-197 web[2270]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 28 21:57:22 ip-172-31-36-197 web[2270]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 28 21:57:22 ip-172-31-36-197 web[2270]:  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
Jun 28 21:57:22 ip-172-31-36-197 web[2270]:  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
Jun 28 21:57:22 ip-172-31-36-197 web[2270]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 28 21:57:22 ip-172-31-36-197 web[2270]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 28 21:57:22 ip-172-31-36-197 web[2270]:  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
Jun 28 21:57:22 ip-172-31-36-197 web[2270]: ModuleNotFoundError: No module named 'aws-processing-api'
Jun 28 21:57:22 ip-172-31-36-197 web[2270]: [2025-06-28 21:57:22 +0000] [2270] [INFO] Worker exiting (pid: 2270)
Jun 28 21:57:22 ip-172-31-36-197 web[2238]: [2025-06-28 21:57:22 +0000] [2238] [ERROR] Worker (pid:2270) exited with code 3
Jun 28 21:57:22 ip-172-31-36-197 web[2238]: [2025-06-28 21:57:22 +0000] [2238] [ERROR] Shutting down: Master
Jun 28 21:57:22 ip-172-31-36-197 web[2238]: [2025-06-28 21:57:22 +0000] [2238] [ERROR] Reason: Worker failed to boot.
Jun 28 21:57:23 ip-172-31-36-197 web[2291]: [2025-06-28 21:57:23 +0000] [2291] [INFO] Starting gunicorn 23.0.0
Jun 28 21:57:23 ip-172-31-36-197 web[2291]: [2025-06-28 21:57:23 +0000] [2291] [INFO] Listening at: http://127.0.0.1:8000 (2291)
Jun 28 21:57:23 ip-172-31-36-197 web[2291]: [2025-06-28 21:57:23 +0000] [2291] [INFO] Using worker: sync
Jun 28 21:57:23 ip-172-31-36-197 web[2295]: [2025-06-28 21:57:23 +0000] [2295] [INFO] Booting worker with pid: 2295
Jun 28 21:57:23 ip-172-31-36-197 web[2295]: [2025-06-28 21:57:23 +0000] [2295] [ERROR] Exception in worker process
Jun 28 21:57:23 ip-172-31-36-197 web[2295]: Traceback (most recent call last):
Jun 28 21:57:23 ip-172-31-36-197 web[2295]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
Jun 28 21:57:23 ip-172-31-36-197 web[2295]:    worker.init_process()
Jun 28 21:57:23 ip-172-31-36-197 web[2295]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 135, in init_process
Jun 28 21:57:23 ip-172-31-36-197 web[2295]:    self.load_wsgi()
Jun 28 21:57:23 ip-172-31-36-197 web[2295]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
Jun 28 21:57:23 ip-172-31-36-197 web[2295]:    self.wsgi = self.app.wsgi()
Jun 28 21:57:23 ip-172-31-36-197 web[2295]:                ^^^^^^^^^^^^^^^
Jun 28 21:57:23 ip-172-31-36-197 web[2295]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/base.py", line 66, in wsgi
Jun 28 21:57:23 ip-172-31-36-197 web[2295]:    self.callable = self.load()
Jun 28 21:57:23 ip-172-31-36-197 web[2295]:                    ^^^^^^^^^^^
Jun 28 21:57:23 ip-172-31-36-197 web[2295]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
Jun 28 21:57:23 ip-172-31-36-197 web[2295]:    return self.load_wsgiapp()
Jun 28 21:57:23 ip-172-31-36-197 web[2295]:           ^^^^^^^^^^^^^^^^^^^
Jun 28 21:57:23 ip-172-31-36-197 web[2295]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
Jun 28 21:57:23 ip-172-31-36-197 web[2295]:    return util.import_app(self.app_uri)
Jun 28 21:57:23 ip-172-31-36-197 web[2295]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 28 21:57:23 ip-172-31-36-197 web[2295]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/util.py", line 370, in import_app
Jun 28 21:57:23 ip-172-31-36-197 web[2295]:    mod = importlib.import_module(module)
Jun 28 21:57:23 ip-172-31-36-197 web[2295]:          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 28 21:57:23 ip-172-31-36-197 web[2295]:  File "/usr/lib64/python3.11/importlib/__init__.py", line 126, in import_module
Jun 28 21:57:23 ip-172-31-36-197 web[2295]:    return _bootstrap._gcd_import(name[level:], package, level)
Jun 28 21:57:23 ip-172-31-36-197 web[2295]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 28 21:57:23 ip-172-31-36-197 web[2295]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 28 21:57:23 ip-172-31-36-197 web[2295]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 28 21:57:23 ip-172-31-36-197 web[2295]:  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
Jun 28 21:57:23 ip-172-31-36-197 web[2295]:  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
Jun 28 21:57:23 ip-172-31-36-197 web[2295]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 28 21:57:23 ip-172-31-36-197 web[2295]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 28 21:57:23 ip-172-31-36-197 web[2295]:  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
Jun 28 21:57:23 ip-172-31-36-197 web[2295]: ModuleNotFoundError: No module named 'aws-processing-api'
Jun 28 21:57:23 ip-172-31-36-197 web[2295]: [2025-06-28 21:57:23 +0000] [2295] [INFO] Worker exiting (pid: 2295)
Jun 28 21:57:23 ip-172-31-36-197 web[2291]: [2025-06-28 21:57:23 +0000] [2291] [ERROR] Worker (pid:2295) exited with code 3
Jun 28 21:57:23 ip-172-31-36-197 web[2291]: [2025-06-28 21:57:23 +0000] [2291] [ERROR] Shutting down: Master
Jun 28 21:57:23 ip-172-31-36-197 web[2291]: [2025-06-28 21:57:23 +0000] [2291] [ERROR] Reason: Worker failed to boot.
Jun 28 21:57:23 ip-172-31-36-197 web[2297]: [2025-06-28 21:57:23 +0000] [2297] [INFO] Starting gunicorn 23.0.0
Jun 28 21:57:23 ip-172-31-36-197 web[2297]: [2025-06-28 21:57:23 +0000] [2297] [INFO] Listening at: http://127.0.0.1:8000 (2297)
Jun 28 21:57:23 ip-172-31-36-197 web[2297]: [2025-06-28 21:57:23 +0000] [2297] [INFO] Using worker: sync
Jun 28 21:57:23 ip-172-31-36-197 web[2301]: [2025-06-28 21:57:23 +0000] [2301] [INFO] Booting worker with pid: 2301
Jun 28 21:57:23 ip-172-31-36-197 web[2301]: [2025-06-28 21:57:23 +0000] [2301] [ERROR] Exception in worker process
Jun 28 21:57:23 ip-172-31-36-197 web[2301]: Traceback (most recent call last):
Jun 28 21:57:23 ip-172-31-36-197 web[2301]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
Jun 28 21:57:23 ip-172-31-36-197 web[2301]:    worker.init_process()
Jun 28 21:57:23 ip-172-31-36-197 web[2301]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 135, in init_process
Jun 28 21:57:23 ip-172-31-36-197 web[2301]:    self.load_wsgi()
Jun 28 21:57:23 ip-172-31-36-197 web[2301]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
Jun 28 21:57:23 ip-172-31-36-197 web[2301]:    self.wsgi = self.app.wsgi()
Jun 28 21:57:23 ip-172-31-36-197 web[2301]:                ^^^^^^^^^^^^^^^
Jun 28 21:57:23 ip-172-31-36-197 web[2301]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/base.py", line 66, in wsgi
Jun 28 21:57:23 ip-172-31-36-197 web[2301]:    self.callable = self.load()
Jun 28 21:57:23 ip-172-31-36-197 web[2301]:                    ^^^^^^^^^^^
Jun 28 21:57:23 ip-172-31-36-197 web[2301]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
Jun 28 21:57:23 ip-172-31-36-197 web[2301]:    return self.load_wsgiapp()
Jun 28 21:57:23 ip-172-31-36-197 web[2301]:           ^^^^^^^^^^^^^^^^^^^
Jun 28 21:57:23 ip-172-31-36-197 web[2301]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
Jun 28 21:57:23 ip-172-31-36-197 web[2301]:    return util.import_app(self.app_uri)
Jun 28 21:57:23 ip-172-31-36-197 web[2301]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 28 21:57:23 ip-172-31-36-197 web[2301]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/util.py", line 370, in import_app
Jun 28 21:57:23 ip-172-31-36-197 web[2301]:    mod = importlib.import_module(module)
Jun 28 21:57:23 ip-172-31-36-197 web[2301]:          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 28 21:57:23 ip-172-31-36-197 web[2301]:  File "/usr/lib64/python3.11/importlib/__init__.py", line 126, in import_module
Jun 28 21:57:23 ip-172-31-36-197 web[2301]:    return _bootstrap._gcd_import(name[level:], package, level)
Jun 28 21:57:23 ip-172-31-36-197 web[2301]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 28 21:57:23 ip-172-31-36-197 web[2301]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 28 21:57:23 ip-172-31-36-197 web[2301]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 28 21:57:23 ip-172-31-36-197 web[2301]:  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
Jun 28 21:57:23 ip-172-31-36-197 web[2301]:  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
Jun 28 21:57:23 ip-172-31-36-197 web[2301]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 28 21:57:23 ip-172-31-36-197 web[2301]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 28 21:57:23 ip-172-31-36-197 web[2301]:  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
Jun 28 21:57:23 ip-172-31-36-197 web[2301]: ModuleNotFoundError: No module named 'aws-processing-api'
Jun 28 21:57:23 ip-172-31-36-197 web[2301]: [2025-06-28 21:57:23 +0000] [2301] [INFO] Worker exiting (pid: 2301)
Jun 28 21:57:23 ip-172-31-36-197 web[2297]: [2025-06-28 21:57:23 +0000] [2297] [ERROR] Worker (pid:2301) exited with code 3
Jun 28 21:57:23 ip-172-31-36-197 web[2297]: [2025-06-28 21:57:23 +0000] [2297] [ERROR] Shutting down: Master
Jun 28 21:57:23 ip-172-31-36-197 web[2297]: [2025-06-28 21:57:23 +0000] [2297] [ERROR] Reason: Worker failed to boot.
Jun 28 21:57:24 ip-172-31-36-197 web[2303]: [2025-06-28 21:57:24 +0000] [2303] [INFO] Starting gunicorn 23.0.0
Jun 28 21:57:24 ip-172-31-36-197 web[2303]: [2025-06-28 21:57:24 +0000] [2303] [INFO] Listening at: http://127.0.0.1:8000 (2303)
Jun 28 21:57:24 ip-172-31-36-197 web[2303]: [2025-06-28 21:57:24 +0000] [2303] [INFO] Using worker: sync
Jun 28 21:57:24 ip-172-31-36-197 web[2307]: [2025-06-28 21:57:24 +0000] [2307] [INFO] Booting worker with pid: 2307
Jun 28 21:57:24 ip-172-31-36-197 web[2307]: [2025-06-28 21:57:24 +0000] [2307] [ERROR] Exception in worker process
Jun 28 21:57:24 ip-172-31-36-197 web[2307]: Traceback (most recent call last):
Jun 28 21:57:24 ip-172-31-36-197 web[2307]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
Jun 28 21:57:24 ip-172-31-36-197 web[2307]:    worker.init_process()
Jun 28 21:57:24 ip-172-31-36-197 web[2307]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 135, in init_process
Jun 28 21:57:24 ip-172-31-36-197 web[2307]:    self.load_wsgi()
Jun 28 21:57:24 ip-172-31-36-197 web[2307]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
Jun 28 21:57:24 ip-172-31-36-197 web[2307]:    self.wsgi = self.app.wsgi()
Jun 28 21:57:24 ip-172-31-36-197 web[2307]:                ^^^^^^^^^^^^^^^
Jun 28 21:57:24 ip-172-31-36-197 web[2307]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/base.py", line 66, in wsgi
Jun 28 21:57:24 ip-172-31-36-197 web[2307]:    self.callable = self.load()
Jun 28 21:57:24 ip-172-31-36-197 web[2307]:                    ^^^^^^^^^^^
Jun 28 21:57:24 ip-172-31-36-197 web[2307]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
Jun 28 21:57:24 ip-172-31-36-197 web[2307]:    return self.load_wsgiapp()
Jun 28 21:57:24 ip-172-31-36-197 web[2307]:           ^^^^^^^^^^^^^^^^^^^
Jun 28 21:57:24 ip-172-31-36-197 web[2307]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
Jun 28 21:57:24 ip-172-31-36-197 web[2307]:    return util.import_app(self.app_uri)
Jun 28 21:57:24 ip-172-31-36-197 web[2307]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 28 21:57:24 ip-172-31-36-197 web[2307]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/util.py", line 370, in import_app
Jun 28 21:57:24 ip-172-31-36-197 web[2307]:    mod = importlib.import_module(module)
Jun 28 21:57:24 ip-172-31-36-197 web[2307]:          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 28 21:57:24 ip-172-31-36-197 web[2307]:  File "/usr/lib64/python3.11/importlib/__init__.py", line 126, in import_module
Jun 28 21:57:24 ip-172-31-36-197 web[2307]:    return _bootstrap._gcd_import(name[level:], package, level)
Jun 28 21:57:24 ip-172-31-36-197 web[2307]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 28 21:57:24 ip-172-31-36-197 web[2307]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 28 21:57:24 ip-172-31-36-197 web[2307]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 28 21:57:24 ip-172-31-36-197 web[2307]:  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
Jun 28 21:57:24 ip-172-31-36-197 web[2307]:  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
Jun 28 21:57:24 ip-172-31-36-197 web[2307]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 28 21:57:24 ip-172-31-36-197 web[2307]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 28 21:57:24 ip-172-31-36-197 web[2307]:  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
Jun 28 21:57:24 ip-172-31-36-197 web[2307]: ModuleNotFoundError: No module named 'aws-processing-api'
Jun 28 21:57:24 ip-172-31-36-197 web[2307]: [2025-06-28 21:57:24 +0000] [2307] [INFO] Worker exiting (pid: 2307)
Jun 28 21:57:24 ip-172-31-36-197 web[2303]: [2025-06-28 21:57:24 +0000] [2303] [ERROR] Worker (pid:2307) exited with code 3
Jun 28 21:57:24 ip-172-31-36-197 web[2303]: [2025-06-28 21:57:24 +0000] [2303] [ERROR] Shutting down: Master
Jun 28 21:57:24 ip-172-31-36-197 web[2303]: [2025-06-28 21:57:24 +0000] [2303] [ERROR] Reason: Worker failed to boot.
Jun 28 21:57:24 ip-172-31-36-197 web[2309]: [2025-06-28 21:57:24 +0000] [2309] [INFO] Starting gunicorn 23.0.0
Jun 28 21:57:24 ip-172-31-36-197 web[2309]: [2025-06-28 21:57:24 +0000] [2309] [INFO] Listening at: http://127.0.0.1:8000 (2309)
Jun 28 21:57:24 ip-172-31-36-197 web[2309]: [2025-06-28 21:57:24 +0000] [2309] [INFO] Using worker: sync
Jun 28 21:57:24 ip-172-31-36-197 web[2313]: [2025-06-28 21:57:24 +0000] [2313] [INFO] Booting worker with pid: 2313
Jun 28 21:57:24 ip-172-31-36-197 web[2313]: [2025-06-28 21:57:24 +0000] [2313] [ERROR] Exception in worker process
Jun 28 21:57:24 ip-172-31-36-197 web[2313]: Traceback (most recent call last):
Jun 28 21:57:24 ip-172-31-36-197 web[2313]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
Jun 28 21:57:24 ip-172-31-36-197 web[2313]:    worker.init_process()
Jun 28 21:57:24 ip-172-31-36-197 web[2313]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 135, in init_process
Jun 28 21:57:24 ip-172-31-36-197 web[2313]:    self.load_wsgi()
Jun 28 21:57:24 ip-172-31-36-197 web[2313]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
Jun 28 21:57:24 ip-172-31-36-197 web[2313]:    self.wsgi = self.app.wsgi()
Jun 28 21:57:24 ip-172-31-36-197 web[2313]:                ^^^^^^^^^^^^^^^
Jun 28 21:57:24 ip-172-31-36-197 web[2313]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/base.py", line 66, in wsgi
Jun 28 21:57:24 ip-172-31-36-197 web[2313]:    self.callable = self.load()
Jun 28 21:57:24 ip-172-31-36-197 web[2313]:                    ^^^^^^^^^^^
Jun 28 21:57:24 ip-172-31-36-197 web[2313]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
Jun 28 21:57:24 ip-172-31-36-197 web[2313]:    return self.load_wsgiapp()
Jun 28 21:57:24 ip-172-31-36-197 web[2313]:           ^^^^^^^^^^^^^^^^^^^
Jun 28 21:57:24 ip-172-31-36-197 web[2313]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
Jun 28 21:57:24 ip-172-31-36-197 web[2313]:    return util.import_app(self.app_uri)
Jun 28 21:57:24 ip-172-31-36-197 web[2313]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 28 21:57:24 ip-172-31-36-197 web[2313]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/util.py", line 370, in import_app
Jun 28 21:57:24 ip-172-31-36-197 web[2313]:    mod = importlib.import_module(module)
Jun 28 21:57:24 ip-172-31-36-197 web[2313]:          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 28 21:57:24 ip-172-31-36-197 web[2313]:  File "/usr/lib64/python3.11/importlib/__init__.py", line 126, in import_module
Jun 28 21:57:24 ip-172-31-36-197 web[2313]:    return _bootstrap._gcd_import(name[level:], package, level)
Jun 28 21:57:24 ip-172-31-36-197 web[2313]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 28 21:57:24 ip-172-31-36-197 web[2313]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 28 21:57:24 ip-172-31-36-197 web[2313]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 28 21:57:24 ip-172-31-36-197 web[2313]:  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
Jun 28 21:57:24 ip-172-31-36-197 web[2313]:  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
Jun 28 21:57:24 ip-172-31-36-197 web[2313]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 28 21:57:24 ip-172-31-36-197 web[2313]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 28 21:57:24 ip-172-31-36-197 web[2313]:  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
Jun 28 21:57:24 ip-172-31-36-197 web[2313]: ModuleNotFoundError: No module named 'aws-processing-api'
Jun 28 21:57:24 ip-172-31-36-197 web[2313]: [2025-06-28 21:57:24 +0000] [2313] [INFO] Worker exiting (pid: 2313)
Jun 28 21:57:24 ip-172-31-36-197 web[2309]: [2025-06-28 21:57:24 +0000] [2309] [ERROR] Worker (pid:2313) exited with code 3
Jun 28 21:57:24 ip-172-31-36-197 web[2309]: [2025-06-28 21:57:24 +0000] [2309] [ERROR] Shutting down: Master
Jun 28 21:57:24 ip-172-31-36-197 web[2309]: [2025-06-28 21:57:24 +0000] [2309] [ERROR] Reason: Worker failed to boot.
Jun 28 21:57:25 ip-172-31-36-197 web[2315]: [2025-06-28 21:57:25 +0000] [2315] [INFO] Starting gunicorn 23.0.0
Jun 28 21:57:25 ip-172-31-36-197 web[2315]: [2025-06-28 21:57:25 +0000] [2315] [INFO] Listening at: http://127.0.0.1:8000 (2315)
Jun 28 21:57:25 ip-172-31-36-197 web[2315]: [2025-06-28 21:57:25 +0000] [2315] [INFO] Using worker: sync
Jun 28 21:57:25 ip-172-31-36-197 web[2319]: [2025-06-28 21:57:25 +0000] [2319] [INFO] Booting worker with pid: 2319
Jun 28 21:57:25 ip-172-31-36-197 web[2319]: [2025-06-28 21:57:25 +0000] [2319] [ERROR] Exception in worker process
Jun 28 21:57:25 ip-172-31-36-197 web[2319]: Traceback (most recent call last):
Jun 28 21:57:25 ip-172-31-36-197 web[2319]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
Jun 28 21:57:25 ip-172-31-36-197 web[2319]:    worker.init_process()
Jun 28 21:57:25 ip-172-31-36-197 web[2319]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 135, in init_process
Jun 28 21:57:25 ip-172-31-36-197 web[2319]:    self.load_wsgi()
Jun 28 21:57:25 ip-172-31-36-197 web[2319]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
Jun 28 21:57:25 ip-172-31-36-197 web[2319]:    self.wsgi = self.app.wsgi()
Jun 28 21:57:25 ip-172-31-36-197 web[2319]:                ^^^^^^^^^^^^^^^
Jun 28 21:57:25 ip-172-31-36-197 web[2319]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/base.py", line 66, in wsgi
Jun 28 21:57:25 ip-172-31-36-197 web[2319]:    self.callable = self.load()
Jun 28 21:57:25 ip-172-31-36-197 web[2319]:                    ^^^^^^^^^^^
Jun 28 21:57:25 ip-172-31-36-197 web[2319]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
Jun 28 21:57:25 ip-172-31-36-197 web[2319]:    return self.load_wsgiapp()
Jun 28 21:57:25 ip-172-31-36-197 web[2319]:           ^^^^^^^^^^^^^^^^^^^
Jun 28 21:57:25 ip-172-31-36-197 web[2319]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
Jun 28 21:57:25 ip-172-31-36-197 web[2319]:    return util.import_app(self.app_uri)
Jun 28 21:57:25 ip-172-31-36-197 web[2319]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 28 21:57:25 ip-172-31-36-197 web[2319]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/util.py", line 370, in import_app
Jun 28 21:57:25 ip-172-31-36-197 web[2319]:    mod = importlib.import_module(module)
Jun 28 21:57:25 ip-172-31-36-197 web[2319]:          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 28 21:57:25 ip-172-31-36-197 web[2319]:  File "/usr/lib64/python3.11/importlib/__init__.py", line 126, in import_module
Jun 28 21:57:25 ip-172-31-36-197 web[2319]:    return _bootstrap._gcd_import(name[level:], package, level)
Jun 28 21:57:25 ip-172-31-36-197 web[2319]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 28 21:57:25 ip-172-31-36-197 web[2319]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 28 21:57:25 ip-172-31-36-197 web[2319]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 28 21:57:25 ip-172-31-36-197 web[2319]:  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
Jun 28 21:57:25 ip-172-31-36-197 web[2319]:  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
Jun 28 21:57:25 ip-172-31-36-197 web[2319]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 28 21:57:25 ip-172-31-36-197 web[2319]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 28 21:57:25 ip-172-31-36-197 web[2319]:  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
Jun 28 21:57:25 ip-172-31-36-197 web[2319]: ModuleNotFoundError: No module named 'aws-processing-api'
Jun 28 21:57:25 ip-172-31-36-197 web[2319]: [2025-06-28 21:57:25 +0000] [2319] [INFO] Worker exiting (pid: 2319)
Jun 28 21:57:25 ip-172-31-36-197 web[2315]: [2025-06-28 21:57:25 +0000] [2315] [ERROR] Worker (pid:2319) exited with code 3
Jun 28 21:57:25 ip-172-31-36-197 web[2315]: [2025-06-28 21:57:25 +0000] [2315] [ERROR] Shutting down: Master
Jun 28 21:57:25 ip-172-31-36-197 web[2315]: [2025-06-28 21:57:25 +0000] [2315] [ERROR] Reason: Worker failed to boot.
Jun 29 11:35:13 ip-172-31-36-197 web[32865]: [2025-06-29 11:35:13 +0000] [32865] [INFO] Starting gunicorn 23.0.0
Jun 29 11:35:13 ip-172-31-36-197 web[32865]: [2025-06-29 11:35:13 +0000] [32865] [INFO] Listening at: http://127.0.0.1:8000 (32865)
Jun 29 11:35:13 ip-172-31-36-197 web[32865]: [2025-06-29 11:35:13 +0000] [32865] [INFO] Using worker: sync
Jun 29 11:35:13 ip-172-31-36-197 web[32873]: [2025-06-29 11:35:13 +0000] [32873] [INFO] Booting worker with pid: 32873
Jun 29 11:35:13 ip-172-31-36-197 web[32873]: [2025-06-29 11:35:13 +0000] [32873] [ERROR] Exception in worker process
Jun 29 11:35:13 ip-172-31-36-197 web[32873]: Traceback (most recent call last):
Jun 29 11:35:13 ip-172-31-36-197 web[32873]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
Jun 29 11:35:13 ip-172-31-36-197 web[32873]:    worker.init_process()
Jun 29 11:35:13 ip-172-31-36-197 web[32873]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 135, in init_process
Jun 29 11:35:13 ip-172-31-36-197 web[32873]:    self.load_wsgi()
Jun 29 11:35:13 ip-172-31-36-197 web[32873]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
Jun 29 11:35:13 ip-172-31-36-197 web[32873]:    self.wsgi = self.app.wsgi()
Jun 29 11:35:13 ip-172-31-36-197 web[32873]:                ^^^^^^^^^^^^^^^
Jun 29 11:35:13 ip-172-31-36-197 web[32873]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/base.py", line 66, in wsgi
Jun 29 11:35:13 ip-172-31-36-197 web[32873]:    self.callable = self.load()
Jun 29 11:35:13 ip-172-31-36-197 web[32873]:                    ^^^^^^^^^^^
Jun 29 11:35:13 ip-172-31-36-197 web[32873]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
Jun 29 11:35:13 ip-172-31-36-197 web[32873]:    return self.load_wsgiapp()
Jun 29 11:35:13 ip-172-31-36-197 web[32873]:           ^^^^^^^^^^^^^^^^^^^
Jun 29 11:35:13 ip-172-31-36-197 web[32873]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
Jun 29 11:35:13 ip-172-31-36-197 web[32873]:    return util.import_app(self.app_uri)
Jun 29 11:35:13 ip-172-31-36-197 web[32873]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:35:13 ip-172-31-36-197 web[32873]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/util.py", line 370, in import_app
Jun 29 11:35:13 ip-172-31-36-197 web[32873]:    mod = importlib.import_module(module)
Jun 29 11:35:13 ip-172-31-36-197 web[32873]:          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:35:13 ip-172-31-36-197 web[32873]:  File "/usr/lib64/python3.11/importlib/__init__.py", line 126, in import_module
Jun 29 11:35:13 ip-172-31-36-197 web[32873]:    return _bootstrap._gcd_import(name[level:], package, level)
Jun 29 11:35:13 ip-172-31-36-197 web[32873]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:35:13 ip-172-31-36-197 web[32873]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:35:13 ip-172-31-36-197 web[32873]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:35:13 ip-172-31-36-197 web[32873]:  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
Jun 29 11:35:13 ip-172-31-36-197 web[32873]:  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
Jun 29 11:35:13 ip-172-31-36-197 web[32873]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:35:13 ip-172-31-36-197 web[32873]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:35:13 ip-172-31-36-197 web[32873]:  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
Jun 29 11:35:13 ip-172-31-36-197 web[32873]: ModuleNotFoundError: No module named 'aws_processing_api'
Jun 29 11:35:13 ip-172-31-36-197 web[32873]: [2025-06-29 11:35:13 +0000] [32873] [INFO] Worker exiting (pid: 32873)
Jun 29 11:35:13 ip-172-31-36-197 web[32865]: [2025-06-29 11:35:13 +0000] [32865] [ERROR] Worker (pid:32873) exited with code 3
Jun 29 11:35:13 ip-172-31-36-197 web[32865]: [2025-06-29 11:35:13 +0000] [32865] [ERROR] Shutting down: Master
Jun 29 11:35:13 ip-172-31-36-197 web[32865]: [2025-06-29 11:35:13 +0000] [32865] [ERROR] Reason: Worker failed to boot.
Jun 29 11:35:14 ip-172-31-36-197 web[32906]: [2025-06-29 11:35:14 +0000] [32906] [INFO] Starting gunicorn 23.0.0
Jun 29 11:35:14 ip-172-31-36-197 web[32906]: [2025-06-29 11:35:14 +0000] [32906] [INFO] Listening at: http://127.0.0.1:8000 (32906)
Jun 29 11:35:14 ip-172-31-36-197 web[32906]: [2025-06-29 11:35:14 +0000] [32906] [INFO] Using worker: sync
Jun 29 11:35:14 ip-172-31-36-197 web[32923]: [2025-06-29 11:35:14 +0000] [32923] [INFO] Booting worker with pid: 32923
Jun 29 11:35:14 ip-172-31-36-197 web[32923]: [2025-06-29 11:35:14 +0000] [32923] [ERROR] Exception in worker process
Jun 29 11:35:14 ip-172-31-36-197 web[32923]: Traceback (most recent call last):
Jun 29 11:35:14 ip-172-31-36-197 web[32923]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
Jun 29 11:35:14 ip-172-31-36-197 web[32923]:    worker.init_process()
Jun 29 11:35:14 ip-172-31-36-197 web[32923]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 135, in init_process
Jun 29 11:35:14 ip-172-31-36-197 web[32923]:    self.load_wsgi()
Jun 29 11:35:14 ip-172-31-36-197 web[32923]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
Jun 29 11:35:14 ip-172-31-36-197 web[32923]:    self.wsgi = self.app.wsgi()
Jun 29 11:35:14 ip-172-31-36-197 web[32923]:                ^^^^^^^^^^^^^^^
Jun 29 11:35:14 ip-172-31-36-197 web[32923]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/base.py", line 66, in wsgi
Jun 29 11:35:14 ip-172-31-36-197 web[32923]:    self.callable = self.load()
Jun 29 11:35:14 ip-172-31-36-197 web[32923]:                    ^^^^^^^^^^^
Jun 29 11:35:14 ip-172-31-36-197 web[32923]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
Jun 29 11:35:14 ip-172-31-36-197 web[32923]:    return self.load_wsgiapp()
Jun 29 11:35:14 ip-172-31-36-197 web[32923]:           ^^^^^^^^^^^^^^^^^^^
Jun 29 11:35:14 ip-172-31-36-197 web[32923]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
Jun 29 11:35:14 ip-172-31-36-197 web[32923]:    return util.import_app(self.app_uri)
Jun 29 11:35:14 ip-172-31-36-197 web[32923]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:35:14 ip-172-31-36-197 web[32923]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/util.py", line 370, in import_app
Jun 29 11:35:14 ip-172-31-36-197 web[32923]:    mod = importlib.import_module(module)
Jun 29 11:35:14 ip-172-31-36-197 web[32923]:          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:35:14 ip-172-31-36-197 web[32923]:  File "/usr/lib64/python3.11/importlib/__init__.py", line 126, in import_module
Jun 29 11:35:14 ip-172-31-36-197 web[32923]:    return _bootstrap._gcd_import(name[level:], package, level)
Jun 29 11:35:14 ip-172-31-36-197 web[32923]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:35:14 ip-172-31-36-197 web[32923]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:35:14 ip-172-31-36-197 web[32923]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:35:14 ip-172-31-36-197 web[32923]:  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
Jun 29 11:35:14 ip-172-31-36-197 web[32923]:  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
Jun 29 11:35:14 ip-172-31-36-197 web[32923]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:35:14 ip-172-31-36-197 web[32923]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:35:14 ip-172-31-36-197 web[32923]:  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
Jun 29 11:35:14 ip-172-31-36-197 web[32923]: ModuleNotFoundError: No module named 'aws_processing_api'
Jun 29 11:35:14 ip-172-31-36-197 web[32923]: [2025-06-29 11:35:14 +0000] [32923] [INFO] Worker exiting (pid: 32923)
Jun 29 11:35:14 ip-172-31-36-197 web[32906]: [2025-06-29 11:35:14 +0000] [32906] [ERROR] Worker (pid:32923) exited with code 3
Jun 29 11:35:14 ip-172-31-36-197 web[32906]: [2025-06-29 11:35:14 +0000] [32906] [ERROR] Shutting down: Master
Jun 29 11:35:14 ip-172-31-36-197 web[32906]: [2025-06-29 11:35:14 +0000] [32906] [ERROR] Reason: Worker failed to boot.
Jun 29 11:35:15 ip-172-31-36-197 web[32928]: [2025-06-29 11:35:15 +0000] [32928] [INFO] Starting gunicorn 23.0.0
Jun 29 11:35:15 ip-172-31-36-197 web[32928]: [2025-06-29 11:35:15 +0000] [32928] [INFO] Listening at: http://127.0.0.1:8000 (32928)
Jun 29 11:35:15 ip-172-31-36-197 web[32928]: [2025-06-29 11:35:15 +0000] [32928] [INFO] Using worker: sync
Jun 29 11:35:15 ip-172-31-36-197 web[32932]: [2025-06-29 11:35:15 +0000] [32932] [INFO] Booting worker with pid: 32932
Jun 29 11:35:15 ip-172-31-36-197 web[32932]: [2025-06-29 11:35:15 +0000] [32932] [ERROR] Exception in worker process
Jun 29 11:35:15 ip-172-31-36-197 web[32932]: Traceback (most recent call last):
Jun 29 11:35:15 ip-172-31-36-197 web[32932]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
Jun 29 11:35:15 ip-172-31-36-197 web[32932]:    worker.init_process()
Jun 29 11:35:15 ip-172-31-36-197 web[32932]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 135, in init_process
Jun 29 11:35:15 ip-172-31-36-197 web[32932]:    self.load_wsgi()
Jun 29 11:35:15 ip-172-31-36-197 web[32932]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
Jun 29 11:35:15 ip-172-31-36-197 web[32932]:    self.wsgi = self.app.wsgi()
Jun 29 11:35:15 ip-172-31-36-197 web[32932]:                ^^^^^^^^^^^^^^^
Jun 29 11:35:15 ip-172-31-36-197 web[32932]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/base.py", line 66, in wsgi
Jun 29 11:35:15 ip-172-31-36-197 web[32932]:    self.callable = self.load()
Jun 29 11:35:15 ip-172-31-36-197 web[32932]:                    ^^^^^^^^^^^
Jun 29 11:35:15 ip-172-31-36-197 web[32932]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
Jun 29 11:35:15 ip-172-31-36-197 web[32932]:    return self.load_wsgiapp()
Jun 29 11:35:15 ip-172-31-36-197 web[32932]:           ^^^^^^^^^^^^^^^^^^^
Jun 29 11:35:15 ip-172-31-36-197 web[32932]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
Jun 29 11:35:15 ip-172-31-36-197 web[32932]:    return util.import_app(self.app_uri)
Jun 29 11:35:15 ip-172-31-36-197 web[32932]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:35:15 ip-172-31-36-197 web[32932]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/util.py", line 370, in import_app
Jun 29 11:35:15 ip-172-31-36-197 web[32932]:    mod = importlib.import_module(module)
Jun 29 11:35:15 ip-172-31-36-197 web[32932]:          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:35:15 ip-172-31-36-197 web[32932]:  File "/usr/lib64/python3.11/importlib/__init__.py", line 126, in import_module
Jun 29 11:35:15 ip-172-31-36-197 web[32932]:    return _bootstrap._gcd_import(name[level:], package, level)
Jun 29 11:35:15 ip-172-31-36-197 web[32932]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:35:15 ip-172-31-36-197 web[32932]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:35:15 ip-172-31-36-197 web[32932]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:35:15 ip-172-31-36-197 web[32932]:  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
Jun 29 11:35:15 ip-172-31-36-197 web[32932]:  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
Jun 29 11:35:15 ip-172-31-36-197 web[32932]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:35:15 ip-172-31-36-197 web[32932]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:35:15 ip-172-31-36-197 web[32932]:  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
Jun 29 11:35:15 ip-172-31-36-197 web[32932]: ModuleNotFoundError: No module named 'aws_processing_api'
Jun 29 11:35:15 ip-172-31-36-197 web[32932]: [2025-06-29 11:35:15 +0000] [32932] [INFO] Worker exiting (pid: 32932)
Jun 29 11:35:15 ip-172-31-36-197 web[32928]: [2025-06-29 11:35:15 +0000] [32928] [ERROR] Worker (pid:32932) exited with code 3
Jun 29 11:35:15 ip-172-31-36-197 web[32928]: [2025-06-29 11:35:15 +0000] [32928] [ERROR] Shutting down: Master
Jun 29 11:35:15 ip-172-31-36-197 web[32928]: [2025-06-29 11:35:15 +0000] [32928] [ERROR] Reason: Worker failed to boot.
Jun 29 11:35:15 ip-172-31-36-197 web[32934]: [2025-06-29 11:35:15 +0000] [32934] [INFO] Starting gunicorn 23.0.0
Jun 29 11:35:15 ip-172-31-36-197 web[32934]: [2025-06-29 11:35:15 +0000] [32934] [INFO] Listening at: http://127.0.0.1:8000 (32934)
Jun 29 11:35:15 ip-172-31-36-197 web[32934]: [2025-06-29 11:35:15 +0000] [32934] [INFO] Using worker: sync
Jun 29 11:35:15 ip-172-31-36-197 web[32938]: [2025-06-29 11:35:15 +0000] [32938] [INFO] Booting worker with pid: 32938
Jun 29 11:35:15 ip-172-31-36-197 web[32938]: [2025-06-29 11:35:15 +0000] [32938] [ERROR] Exception in worker process
Jun 29 11:35:15 ip-172-31-36-197 web[32938]: Traceback (most recent call last):
Jun 29 11:35:15 ip-172-31-36-197 web[32938]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
Jun 29 11:35:15 ip-172-31-36-197 web[32938]:    worker.init_process()
Jun 29 11:35:15 ip-172-31-36-197 web[32938]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 135, in init_process
Jun 29 11:35:15 ip-172-31-36-197 web[32938]:    self.load_wsgi()
Jun 29 11:35:15 ip-172-31-36-197 web[32938]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
Jun 29 11:35:15 ip-172-31-36-197 web[32938]:    self.wsgi = self.app.wsgi()
Jun 29 11:35:15 ip-172-31-36-197 web[32938]:                ^^^^^^^^^^^^^^^
Jun 29 11:35:15 ip-172-31-36-197 web[32938]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/base.py", line 66, in wsgi
Jun 29 11:35:15 ip-172-31-36-197 web[32938]:    self.callable = self.load()
Jun 29 11:35:15 ip-172-31-36-197 web[32938]:                    ^^^^^^^^^^^
Jun 29 11:35:15 ip-172-31-36-197 web[32938]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
Jun 29 11:35:15 ip-172-31-36-197 web[32938]:    return self.load_wsgiapp()
Jun 29 11:35:15 ip-172-31-36-197 web[32938]:           ^^^^^^^^^^^^^^^^^^^
Jun 29 11:35:15 ip-172-31-36-197 web[32938]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
Jun 29 11:35:15 ip-172-31-36-197 web[32938]:    return util.import_app(self.app_uri)
Jun 29 11:35:15 ip-172-31-36-197 web[32938]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:35:15 ip-172-31-36-197 web[32938]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/util.py", line 370, in import_app
Jun 29 11:35:15 ip-172-31-36-197 web[32938]:    mod = importlib.import_module(module)
Jun 29 11:35:15 ip-172-31-36-197 web[32938]:          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:35:15 ip-172-31-36-197 web[32938]:  File "/usr/lib64/python3.11/importlib/__init__.py", line 126, in import_module
Jun 29 11:35:15 ip-172-31-36-197 web[32938]:    return _bootstrap._gcd_import(name[level:], package, level)
Jun 29 11:35:15 ip-172-31-36-197 web[32938]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:35:15 ip-172-31-36-197 web[32938]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:35:15 ip-172-31-36-197 web[32938]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:35:15 ip-172-31-36-197 web[32938]:  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
Jun 29 11:35:15 ip-172-31-36-197 web[32938]:  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
Jun 29 11:35:15 ip-172-31-36-197 web[32938]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:35:15 ip-172-31-36-197 web[32938]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:35:15 ip-172-31-36-197 web[32938]:  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
Jun 29 11:35:15 ip-172-31-36-197 web[32938]: ModuleNotFoundError: No module named 'aws_processing_api'
Jun 29 11:35:15 ip-172-31-36-197 web[32938]: [2025-06-29 11:35:15 +0000] [32938] [INFO] Worker exiting (pid: 32938)
Jun 29 11:35:15 ip-172-31-36-197 web[32934]: [2025-06-29 11:35:15 +0000] [32934] [ERROR] Worker (pid:32938) exited with code 3
Jun 29 11:35:15 ip-172-31-36-197 web[32934]: [2025-06-29 11:35:15 +0000] [32934] [ERROR] Shutting down: Master
Jun 29 11:35:15 ip-172-31-36-197 web[32934]: [2025-06-29 11:35:15 +0000] [32934] [ERROR] Reason: Worker failed to boot.
Jun 29 11:35:16 ip-172-31-36-197 web[32940]: [2025-06-29 11:35:16 +0000] [32940] [INFO] Starting gunicorn 23.0.0
Jun 29 11:35:16 ip-172-31-36-197 web[32940]: [2025-06-29 11:35:16 +0000] [32940] [INFO] Listening at: http://127.0.0.1:8000 (32940)
Jun 29 11:35:16 ip-172-31-36-197 web[32940]: [2025-06-29 11:35:16 +0000] [32940] [INFO] Using worker: sync
Jun 29 11:35:16 ip-172-31-36-197 web[32944]: [2025-06-29 11:35:16 +0000] [32944] [INFO] Booting worker with pid: 32944
Jun 29 11:35:16 ip-172-31-36-197 web[32944]: [2025-06-29 11:35:16 +0000] [32944] [ERROR] Exception in worker process
Jun 29 11:35:16 ip-172-31-36-197 web[32944]: Traceback (most recent call last):
Jun 29 11:35:16 ip-172-31-36-197 web[32944]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
Jun 29 11:35:16 ip-172-31-36-197 web[32944]:    worker.init_process()
Jun 29 11:35:16 ip-172-31-36-197 web[32944]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 135, in init_process
Jun 29 11:35:16 ip-172-31-36-197 web[32944]:    self.load_wsgi()
Jun 29 11:35:16 ip-172-31-36-197 web[32944]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
Jun 29 11:35:16 ip-172-31-36-197 web[32944]:    self.wsgi = self.app.wsgi()
Jun 29 11:35:16 ip-172-31-36-197 web[32944]:                ^^^^^^^^^^^^^^^
Jun 29 11:35:16 ip-172-31-36-197 web[32944]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/base.py", line 66, in wsgi
Jun 29 11:35:16 ip-172-31-36-197 web[32944]:    self.callable = self.load()
Jun 29 11:35:16 ip-172-31-36-197 web[32944]:                    ^^^^^^^^^^^
Jun 29 11:35:16 ip-172-31-36-197 web[32944]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
Jun 29 11:35:16 ip-172-31-36-197 web[32944]:    return self.load_wsgiapp()
Jun 29 11:35:16 ip-172-31-36-197 web[32944]:           ^^^^^^^^^^^^^^^^^^^
Jun 29 11:35:16 ip-172-31-36-197 web[32944]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
Jun 29 11:35:16 ip-172-31-36-197 web[32944]:    return util.import_app(self.app_uri)
Jun 29 11:35:16 ip-172-31-36-197 web[32944]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:35:16 ip-172-31-36-197 web[32944]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/util.py", line 370, in import_app
Jun 29 11:35:16 ip-172-31-36-197 web[32944]:    mod = importlib.import_module(module)
Jun 29 11:35:16 ip-172-31-36-197 web[32944]:          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:35:16 ip-172-31-36-197 web[32944]:  File "/usr/lib64/python3.11/importlib/__init__.py", line 126, in import_module
Jun 29 11:35:16 ip-172-31-36-197 web[32944]:    return _bootstrap._gcd_import(name[level:], package, level)
Jun 29 11:35:16 ip-172-31-36-197 web[32944]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:35:16 ip-172-31-36-197 web[32944]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:35:16 ip-172-31-36-197 web[32944]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:35:16 ip-172-31-36-197 web[32944]:  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
Jun 29 11:35:16 ip-172-31-36-197 web[32944]:  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
Jun 29 11:35:16 ip-172-31-36-197 web[32944]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:35:16 ip-172-31-36-197 web[32944]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:35:16 ip-172-31-36-197 web[32944]:  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
Jun 29 11:35:16 ip-172-31-36-197 web[32944]: ModuleNotFoundError: No module named 'aws_processing_api'
Jun 29 11:35:16 ip-172-31-36-197 web[32944]: [2025-06-29 11:35:16 +0000] [32944] [INFO] Worker exiting (pid: 32944)
Jun 29 11:35:16 ip-172-31-36-197 web[32940]: [2025-06-29 11:35:16 +0000] [32940] [ERROR] Worker (pid:32944) exited with code 3
Jun 29 11:35:16 ip-172-31-36-197 web[32940]: [2025-06-29 11:35:16 +0000] [32940] [ERROR] Shutting down: Master
Jun 29 11:35:16 ip-172-31-36-197 web[32940]: [2025-06-29 11:35:16 +0000] [32940] [ERROR] Reason: Worker failed to boot.
Jun 29 11:35:16 ip-172-31-36-197 web[32947]: [2025-06-29 11:35:16 +0000] [32947] [INFO] Starting gunicorn 23.0.0
Jun 29 11:35:16 ip-172-31-36-197 web[32947]: [2025-06-29 11:35:16 +0000] [32947] [INFO] Listening at: http://127.0.0.1:8000 (32947)
Jun 29 11:35:16 ip-172-31-36-197 web[32947]: [2025-06-29 11:35:16 +0000] [32947] [INFO] Using worker: sync
Jun 29 11:35:16 ip-172-31-36-197 web[32951]: [2025-06-29 11:35:16 +0000] [32951] [INFO] Booting worker with pid: 32951
Jun 29 11:35:16 ip-172-31-36-197 web[32951]: [2025-06-29 11:35:16 +0000] [32951] [ERROR] Exception in worker process
Jun 29 11:35:16 ip-172-31-36-197 web[32951]: Traceback (most recent call last):
Jun 29 11:35:16 ip-172-31-36-197 web[32951]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
Jun 29 11:35:16 ip-172-31-36-197 web[32951]:    worker.init_process()
Jun 29 11:35:16 ip-172-31-36-197 web[32951]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 135, in init_process
Jun 29 11:35:16 ip-172-31-36-197 web[32951]:    self.load_wsgi()
Jun 29 11:35:16 ip-172-31-36-197 web[32951]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
Jun 29 11:35:16 ip-172-31-36-197 web[32951]:    self.wsgi = self.app.wsgi()
Jun 29 11:35:16 ip-172-31-36-197 web[32951]:                ^^^^^^^^^^^^^^^
Jun 29 11:35:16 ip-172-31-36-197 web[32951]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/base.py", line 66, in wsgi
Jun 29 11:35:16 ip-172-31-36-197 web[32951]:    self.callable = self.load()
Jun 29 11:35:16 ip-172-31-36-197 web[32951]:                    ^^^^^^^^^^^
Jun 29 11:35:16 ip-172-31-36-197 web[32951]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
Jun 29 11:35:16 ip-172-31-36-197 web[32951]:    return self.load_wsgiapp()
Jun 29 11:35:16 ip-172-31-36-197 web[32951]:           ^^^^^^^^^^^^^^^^^^^
Jun 29 11:35:16 ip-172-31-36-197 web[32951]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
Jun 29 11:35:16 ip-172-31-36-197 web[32951]:    return util.import_app(self.app_uri)
Jun 29 11:35:16 ip-172-31-36-197 web[32951]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:35:16 ip-172-31-36-197 web[32951]:  File "/var/app/venv/staging-LQM1lest/lib64/python3.11/site-packages/gunicorn/util.py", line 370, in import_app
Jun 29 11:35:16 ip-172-31-36-197 web[32951]:    mod = importlib.import_module(module)
Jun 29 11:35:16 ip-172-31-36-197 web[32951]:          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:35:16 ip-172-31-36-197 web[32951]:  File "/usr/lib64/python3.11/importlib/__init__.py", line 126, in import_module
Jun 29 11:35:16 ip-172-31-36-197 web[32951]:    return _bootstrap._gcd_import(name[level:], package, level)
Jun 29 11:35:16 ip-172-31-36-197 web[32951]:           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Jun 29 11:35:16 ip-172-31-36-197 web[32951]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:35:16 ip-172-31-36-197 web[32951]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:35:16 ip-172-31-36-197 web[32951]:  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
Jun 29 11:35:16 ip-172-31-36-197 web[32951]:  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
Jun 29 11:35:16 ip-172-31-36-197 web[32951]:  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
Jun 29 11:35:16 ip-172-31-36-197 web[32951]:  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Jun 29 11:35:16 ip-172-31-36-197 web[32951]:  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
Jun 29 11:35:16 ip-172-31-36-197 web[32951]: ModuleNotFoundError: No module named 'aws_processing_api'
Jun 29 11:35:16 ip-172-31-36-197 web[32951]: [2025-06-29 11:35:16 +0000] [32951] [INFO] Worker exiting (pid: 32951)
Jun 29 11:35:16 ip-172-31-36-197 web[32947]: [2025-06-29 11:35:16 +0000] [32947] [ERROR] Worker (pid:32951) exited with code 3
Jun 29 11:35:16 ip-172-31-36-197 web[32947]: [2025-06-29 11:35:16 +0000] [32947] [ERROR] Shutting down: Master
Jun 29 11:35:16 ip-172-31-36-197 web[32947]: [2025-06-29 11:35:16 +0000] [32947] [ERROR] Reason: Worker failed to boot.
