[2025-06-28T21:54:45Z] Started EB User Data
+ SLEEP_TIME=2
+ SLEEP_TIME_MAX=3600
+ true
+ curl https://elasticbeanstalk-platform-assets-us-west-2.s3.us-west-2.amazonaws.com/stalks/eb_python311_amazon_linux_2023_1.0.797.0_20250625232415/lib/UserDataScript.sh
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100  4838  100  4838    0     0   108k      0 --:--:-- --:--:-- --:--:--  109k
+ RESULT=0
+ [[ 0 -ne 0 ]]
+ /bin/bash /tmp/ebbootstrap.sh 'https://cloudformation-waitcondition-us-west-2.s3-us-west-2.amazonaws.com/arn%3Aaws%3Acloudformation%3Aus-west-2%3A029530100737%3Astack/awseb-e-k4qmvnc3m7-stack/6540d6a0-546a-11f0-847c-0ab3c89e0719/6542d270-546a-11f0-847c-0ab3c89e0719/AWSEBInstanceLaunchWaitHandle?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250628T215401Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86399&X-Amz-Credential=AKIAJBJSWSW6NLR67N6A%2F20250628%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Signature=e05ff4bd777176328cd5d3d16144e376bd4d81fda271569d917d7fb4e8320e7d' arn:aws:cloudformation:us-west-2:029530100737:stack/awseb-e-k4qmvnc3m7-stack/6540d6a0-546a-11f0-847c-0ab3c89e0719 993b0c7a-e852-4d74-97d5-207f9ae1702e https://elasticbeanstalk-health.us-west-2.amazonaws.com '' https://elasticbeanstalk-platform-assets-us-west-2.s3.us-west-2.amazonaws.com/stalks/eb_python311_amazon_linux_2023_1.0.797.0_20250625232415 us-west-2
[2025-06-28T21:54:45.652Z] Started EB Bootstrapping Script.
[2025-06-28T21:54:45.656Z] Received parameters:
 TARBALLS = 
 EB_GEMS = 
 SIGNAL_URL = https://cloudformation-waitcondition-us-west-2.s3-us-west-2.amazonaws.com/arn%3Aaws%3Acloudformation%3Aus-west-2%3A029530100737%3Astack/awseb-e-k4qmvnc3m7-stack/6540d6a0-546a-11f0-847c-0ab3c89e0719/6542d270-546a-11f0-847c-0ab3c89e0719/AWSEBInstanceLaunchWaitHandle?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250628T215401Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86399&X-Amz-Credential=AKIAJBJSWSW6NLR67N6A%2F20250628%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Signature=e05ff4bd777176328cd5d3d16144e376bd4d81fda271569d917d7fb4e8320e7d
 STACK_ID = arn:aws:cloudformation:us-west-2:029530100737:stack/awseb-e-k4qmvnc3m7-stack/6540d6a0-546a-11f0-847c-0ab3c89e0719
 REGION = us-west-2
 GUID = 
 HEALTHD_GROUP_ID = 993b0c7a-e852-4d74-97d5-207f9ae1702e
 HEALTHD_ENDPOINT = https://elasticbeanstalk-health.us-west-2.amazonaws.com
 PROXY_SERVER = 
 HEALTHD_PROXY_LOG_LOCATION = 
 PLATFORM_ASSETS_URL = https://elasticbeanstalk-platform-assets-us-west-2.s3.us-west-2.amazonaws.com/stalks/eb_python311_amazon_linux_2023_1.0.797.0_20250625232415
[2025-06-28T21:54:45.662Z] engine url is set to https://elasticbeanstalk-platform-assets-us-west-2.s3.us-west-2.amazonaws.com/stalks/eb_python311_amazon_linux_2023_1.0.797.0_20250625232415/lib/platform-engine.zip
[2025-06-28T21:54:45.664Z] first init of instance.
[2025-06-28T21:54:45.667Z] Started executing cfn_init _OnInstanceBoot.
[2025-06-28T21:54:45.669Z] Running cfn-init ConfigSet: _OnInstanceBoot.
[2025-06-28T21:54:45.672Z] Using stack-id from userdata
[2025-06-28T21:54:46.530Z] Command Returned: 
[2025-06-28T21:54:46.532Z] Completed executing cfn_init.
[2025-06-28T21:54:46.534Z] finished _OnInstanceBoot
[2025-06-28T21:54:46.536Z] installing platform engine
2025-06-28 21:54:47 URL:https://elasticbeanstalk-platform-assets-us-west-2.s3.us-west-2.amazonaws.com/stalks/eb_python311_amazon_linux_2023_1.0.797.0_20250625232415/lib/platform-engine.zip [33307599/33307599] -> "/tmp/platform-engine.zip" [1]
[2025-06-28T21:54:47.627Z] executing platform engine. see /var/log/eb-engine.log.
[2025-06-28T21:54:49.333Z] Successfully bootstrapped instance.
[2025-06-28T21:54:49.339Z] Sending signal 0 to CFN wait condition https://cloudformation-waitcondition-us-west-2.s3-us-west-2.amazonaws.com/arn%3Aaws%3Acloudformation%3Aus-west-2%3A029530100737%3Astack/awseb-e-k4qmvnc3m7-stack/6540d6a0-546a-11f0-847c-0ab3c89e0719/6542d270-546a-11f0-847c-0ab3c89e0719/AWSEBInstanceLaunchWaitHandle?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250628T215401Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86399&X-Amz-Credential=AKIAJBJSWSW6NLR67N6A%2F20250628%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Signature=e05ff4bd777176328cd5d3d16144e376bd4d81fda271569d917d7fb4e8320e7d
CloudFormation signaled successfully with status SUCCESS
[2025-06-28T21:54:49.755Z] Tailing /var/log/eb-engine.log

******************* eb-engine taillog *******************
2025/06/28 21:54:48.486678 [INFO] 1001

2025/06/28 21:54:48.486956 [INFO] configure bundle log for healthd...
2025/06/28 21:54:48.487015 [INFO] Executing instruction: GetSetupProxyLog
2025/06/28 21:54:48.487081 [INFO] Skipping Install yum packages
2025/06/28 21:54:48.487085 [INFO] Skipping Install Python Bundle
2025/06/28 21:54:48.487089 [INFO] Skipping Configure Python site-packages
2025/06/28 21:54:48.487093 [INFO] Skipping Install Python Modules
2025/06/28 21:54:48.487097 [INFO] Skipping MarkBaked
2025/06/28 21:54:48.487102 [INFO] Instance has NOT been bootstrapped
2025/06/28 21:54:48.487104 [INFO] Executing instruction: TuneSystemSettings
2025/06/28 21:54:48.487106 [INFO] Starting TuneSystemSettings
2025/06/28 21:54:48.487109 [INFO] Instance has NOT been bootstrapped
2025/06/28 21:54:48.488494 [INFO] Executing instruction: GetSetupLogRotate
2025/06/28 21:54:48.488500 [INFO] Initialize LogRotate files and directories
2025/06/28 21:54:48.516537 [INFO] Instance has NOT been bootstrapped
2025/06/28 21:54:48.516548 [INFO] Executing instruction: BootstrapCFNHup
2025/06/28 21:54:48.516551 [INFO] Bootstrap cfn-hup
2025/06/28 21:54:48.518777 [INFO] Copying file /opt/elasticbeanstalk/config/private/aws-eb-command-handler.conf to /etc/cfn/hooks.d/aws-eb-command-handler.conf
2025/06/28 21:54:48.520904 [INFO] Executing instruction: StartCFNHup
2025/06/28 21:54:48.520911 [INFO] Start cfn-hup
2025/06/28 21:54:48.520924 [INFO] Running command: systemctl show -p PartOf cfn-hup.service
2025/06/28 21:54:48.534747 [INFO] cfn-hup is not registered with EB yet, registering it now
2025/06/28 21:54:48.534781 [INFO] Running command: systemctl show -p PartOf cfn-hup.service
2025/06/28 21:54:48.549328 [INFO] Running command: systemctl daemon-reload
2025/06/28 21:54:48.740936 [INFO] Running command: systemctl reset-failed
2025/06/28 21:54:48.752430 [INFO] Running command: systemctl is-enabled aws-eb.target
2025/06/28 21:54:48.761012 [INFO] Running command: systemctl enable aws-eb.target
2025/06/28 21:54:49.006245 [INFO] Running command: systemctl start aws-eb.target
2025/06/28 21:54:49.015639 [INFO] Running command: systemctl enable cfn-hup.service
2025/06/28 21:54:49.265163 [INFO] Created symlink /etc/systemd/system/multi-user.target.wants/cfn-hup.service → /etc/systemd/system/cfn-hup.service.

2025/06/28 21:54:49.265195 [INFO] Running command: systemctl is-active cfn-hup.service
2025/06/28 21:54:49.273745 [INFO] cfn-hup process is not running, starting it now
2025/06/28 21:54:49.273772 [INFO] Running command: systemctl show -p PartOf cfn-hup.service
2025/06/28 21:54:49.283966 [INFO] Running command: systemctl is-active cfn-hup.service
2025/06/28 21:54:49.291552 [INFO] Running command: systemctl start cfn-hup.service
2025/06/28 21:54:49.327992 [INFO] Instance has NOT been bootstrapped
2025/06/28 21:54:49.328009 [INFO] Executing instruction: SetupPublishLogCronjob
2025/06/28 21:54:49.328013 [INFO] Setup publish logs cron job...
2025/06/28 21:54:49.328017 [INFO] Copying file /opt/elasticbeanstalk/config/private/logtasks/cron/publishlogs to /etc/cron.d/publishlogs
2025/06/28 21:54:49.330185 [INFO] Instance has NOT been bootstrapped
2025/06/28 21:54:49.330195 [INFO] Executing instruction: MarkBootstrapped
2025/06/28 21:54:49.330199 [INFO] Starting MarkBootstrapped
2025/06/28 21:54:49.330204 [INFO] Instance has NOT been bootstrapped
2025/06/28 21:54:49.330283 [INFO] Marked instance as Bootstrapped
2025/06/28 21:54:49.330287 [INFO] Executing instruction: Save CFN Stack Info
2025/06/28 21:54:49.330344 [INFO] Executing cleanup logic
2025/06/28 21:54:49.330354 [INFO] Platform Engine finished execution on command: env-launch
******************* End of taillog *******************


[2025-06-28T21:54:49.759Z] Tailing /var/log/eb-tools.log

******************* eb-tools taillog *******************
***eb-tools is not available yet.***
******************* End of taillog *******************


[2025-06-28T21:54:49.761Z] Tailing /var/log/eb-hooks.log

******************* eb-hooks taillog *******************
***eb-hooks is not available yet.***
******************* End of taillog *******************


[2025-06-28T21:54:49.762Z] Tailing /var/log/cfn-init.log

******************* cfn-init taillog *******************
2025-06-28 21:54:46,244 [INFO] -----------------------Starting build-----------------------
2025-06-28 21:54:46,249 [INFO] Running configSets: _OnInstanceBoot
2025-06-28 21:54:46,251 [INFO] Running configSet _OnInstanceBoot
2025-06-28 21:54:46,254 [INFO] Running config AWSEBBaseConfig
2025-06-28 21:54:46,491 [INFO] Command clearbackupfiles succeeded
2025-06-28 21:54:46,494 [INFO] Running config AWSEBCfnHupEndpointOverride
2025-06-28 21:54:46,498 [INFO] Command clearbackupfiles succeeded
2025-06-28 21:54:46,499 [INFO] ConfigSets completed
2025-06-28 21:54:46,500 [INFO] -----------------------Build complete-----------------------
******************* End of taillog *******************


[2025-06-28T21:54:49.765Z] Tailing /var/log/cfn-hup.log

******************* cfn-hup taillog *******************
2025-06-28 21:54:49,614 [DEBUG] CloudFormation client initialized with endpoint https://cloudformation.us-west-2.amazonaws.com
2025-06-28 21:54:49,615 [DEBUG] SQS client initialized with endpoint https://sqs.us-west-2.amazonaws.com
2025-06-28 21:54:49,615 [DEBUG] CloudFormation client initialized with endpoint https://cloudformation.us-west-2.amazonaws.com
2025-06-28 21:54:49,615 [DEBUG] Enabled single threading mode.
2025-06-28 21:54:49,615 [DEBUG] Creating /var/lib/cfn-hup/data
2025-06-28 21:54:49,624 [INFO] No umask value specified in config file. Using the default one: 0o22
2025-06-28 21:54:49,692 [INFO] Pid: 1787
******************* End of taillog *******************


[2025-06-28T21:54:49.768Z] Completed EB Bootstrapping Script.
+ RESULT=0
+ [[ 0 -ne 0 ]]
+ exit 0
