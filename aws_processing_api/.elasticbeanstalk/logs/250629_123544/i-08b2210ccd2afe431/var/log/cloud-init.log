2025-06-28 21:54:43,606 - util.py[DEBUG]: Cloud-init v. 22.2.2 running 'init' at Sat, 28 Jun 2025 21:54:43 +0000. Up 6.20 seconds.
2025-06-28 21:54:43,606 - main.py[DEBUG]: No kernel command line url found.
2025-06-28 21:54:43,606 - main.py[DEBUG]: Closing stdin.
2025-06-28 21:54:43,606 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud (recursive=False)
2025-06-28 21:54:43,607 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud (recursive=True)
2025-06-28 21:54:43,611 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/scripts (recursive=False)
2025-06-28 21:54:43,613 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/scripts (recursive=True)
2025-06-28 21:54:43,615 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/scripts/per-instance (recursive=False)
2025-06-28 21:54:43,615 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/scripts (recursive=True)
2025-06-28 21:54:43,618 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/scripts/per-once (recursive=False)
2025-06-28 21:54:43,619 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/scripts (recursive=True)
2025-06-28 21:54:43,623 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/scripts/per-boot (recursive=False)
2025-06-28 21:54:43,623 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/scripts (recursive=True)
2025-06-28 21:54:43,628 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/scripts/vendor (recursive=False)
2025-06-28 21:54:43,628 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud (recursive=True)
2025-06-28 21:54:43,636 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/seed (recursive=False)
2025-06-28 21:54:43,636 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud (recursive=True)
2025-06-28 21:54:43,645 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances (recursive=False)
2025-06-28 21:54:43,646 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud (recursive=True)
2025-06-28 21:54:43,656 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/handlers (recursive=False)
2025-06-28 21:54:43,656 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud (recursive=True)
2025-06-28 21:54:43,667 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/sem (recursive=False)
2025-06-28 21:54:43,668 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/data (recursive=False)
2025-06-28 21:54:43,669 - util.py[DEBUG]: Restoring selinux mode for /run/cloud-init (recursive=True)
2025-06-28 21:54:43,669 - util.py[DEBUG]: Restoring selinux mode for /run/cloud-init/sem (recursive=False)
2025-06-28 21:54:43,670 - util.py[DEBUG]: Writing to /var/log/cloud-init.log - ab: [640] 0 bytes
2025-06-28 21:54:43,671 - util.py[DEBUG]: Restoring selinux mode for /var/log/cloud-init.log (recursive=False)
2025-06-28 21:54:43,671 - util.py[DEBUG]: Restoring selinux mode for /var/log/cloud-init.log (recursive=False)
2025-06-28 21:54:43,672 - util.py[DEBUG]: Changing the ownership of /var/log/cloud-init.log to 0:4
2025-06-28 21:54:43,672 - util.py[DEBUG]: Writing to /var/lib/cloud/data/python-version - wb: [644] 3 bytes
2025-06-28 21:54:43,673 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/data/python-version (recursive=False)
2025-06-28 21:54:43,674 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/data/python-version (recursive=False)
2025-06-28 21:54:43,674 - subp.py[DEBUG]: Running command ['ip', '--json', 'addr'] with allowed return codes [0] (shell=False, capture=True)
2025-06-28 21:54:43,679 - subp.py[DEBUG]: Running command ['ip', '-o', 'route', 'list'] with allowed return codes [0] (shell=False, capture=True)
2025-06-28 21:54:43,683 - subp.py[DEBUG]: Running command ['ip', '--oneline', '-6', 'route', 'list', 'table', 'all'] with allowed return codes [0, 1] (shell=False, capture=True)
2025-06-28 21:54:43,688 - handlers.py[DEBUG]: start: init-network/check-cache: attempting to read from cache [trust]
2025-06-28 21:54:43,688 - util.py[DEBUG]: Reading from /var/lib/cloud/instance/obj.pkl (quiet=False)
2025-06-28 21:54:43,688 - stages.py[DEBUG]: no cache found
2025-06-28 21:54:43,688 - handlers.py[DEBUG]: finish: init-network/check-cache: SUCCESS: no cache found
2025-06-28 21:54:43,689 - util.py[DEBUG]: Attempting to remove /var/lib/cloud/instance
2025-06-28 21:54:43,695 - stages.py[DEBUG]: Using distro class <class 'cloudinit.distros.amazon.Distro'>
2025-06-28 21:54:43,695 - __init__.py[DEBUG]: Looking for data source in: ['Ec2', 'None'], via packages ['', 'cloudinit.sources'] that matches dependencies ['FILESYSTEM', 'NETWORK']
2025-06-28 21:54:43,702 - __init__.py[DEBUG]: Searching for network data source in: ['DataSourceEc2', 'DataSourceNone']
2025-06-28 21:54:43,702 - handlers.py[DEBUG]: start: init-network/search-Ec2: searching for network data from DataSourceEc2
2025-06-28 21:54:43,702 - __init__.py[DEBUG]: Seeing if we can get any data from <class 'cloudinit.sources.DataSourceEc2.DataSourceEc2'>
2025-06-28 21:54:43,702 - __init__.py[DEBUG]: Update datasource metadata and network config due to events: boot-new-instance
2025-06-28 21:54:43,702 - util.py[DEBUG]: Reading from /sys/hypervisor/uuid (quiet=False)
2025-06-28 21:54:43,702 - dmi.py[DEBUG]: querying dmi data /sys/class/dmi/id/product_uuid
2025-06-28 21:54:43,702 - dmi.py[DEBUG]: querying dmi data /sys/class/dmi/id/product_serial
2025-06-28 21:54:43,702 - dmi.py[DEBUG]: querying dmi data /sys/class/dmi/id/chassis_asset_tag
2025-06-28 21:54:43,702 - dmi.py[DEBUG]: querying dmi data /sys/class/dmi/id/sys_vendor
2025-06-28 21:54:43,702 - DataSourceEc2.py[DEBUG]: strict_mode: warn, cloud_name=aws cloud_platform=ec2
2025-06-28 21:54:43,703 - util.py[DEBUG]: Skipping DNS checks of IP address ***************
2025-06-28 21:54:43,703 - util.py[DEBUG]: Resolving URL: http://***************:80 took 0.000 seconds
2025-06-28 21:54:43,703 - util.py[DEBUG]: Skipping DNS checks of IP address fd00:ec2::254
2025-06-28 21:54:43,703 - util.py[DEBUG]: Resolving URL: http://[fd00:ec2::254]:80 took 0.000 seconds
2025-06-28 21:54:43,703 - DataSourceEc2.py[DEBUG]: Fetching Ec2 IMDSv2 API Token
2025-06-28 21:54:43,703 - url_helper.py[DEBUG]: [0/1] open 'http://***************:80/latest/api/token' with {'url': 'http://***************:80/latest/api/token', 'allow_redirects': True, 'method': 'PUT', 'timeout': 50.0, 'headers': {'User-Agent': 'Cloud-Init/22.2.2', 'X-aws-ec2-metadata-token-ttl-seconds': 'REDACTED'}} configuration
2025-06-28 21:54:43,709 - url_helper.py[DEBUG]: Read from http://***************:80/latest/api/token (200, 56b) after 1 attempts
2025-06-28 21:54:43,710 - DataSourceEc2.py[DEBUG]: Using metadata source: 'http://***************:80'
2025-06-28 21:54:43,710 - url_helper.py[DEBUG]: [0/1] open 'http://***************:80/2021-03-23/meta-data/instance-id' with {'url': 'http://***************:80/2021-03-23/meta-data/instance-id', 'allow_redirects': True, 'method': 'GET', 'headers': {'User-Agent': 'Cloud-Init/22.2.2', 'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,746 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/instance-id (200, 19b) after 1 attempts
2025-06-28 21:54:43,747 - DataSourceEc2.py[DEBUG]: Found preferred metadata version 2021-03-23
2025-06-28 21:54:43,747 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/user-data' with {'url': 'http://***************:80/2021-03-23/user-data', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,753 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/user-data (200, 2478b) after 1 attempts
2025-06-28 21:54:43,754 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/' with {'url': 'http://***************:80/2021-03-23/meta-data/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,758 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/ (200, 318b) after 1 attempts
2025-06-28 21:54:43,758 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/block-device-mapping/' with {'url': 'http://***************:80/2021-03-23/meta-data/block-device-mapping/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,760 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/block-device-mapping/ (200, 8b) after 1 attempts
2025-06-28 21:54:43,760 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/block-device-mapping/ami' with {'url': 'http://***************:80/2021-03-23/meta-data/block-device-mapping/ami', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,762 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/block-device-mapping/ami (200, 4b) after 1 attempts
2025-06-28 21:54:43,762 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/block-device-mapping/root' with {'url': 'http://***************:80/2021-03-23/meta-data/block-device-mapping/root', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,764 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/block-device-mapping/root (200, 9b) after 1 attempts
2025-06-28 21:54:43,764 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/events/' with {'url': 'http://***************:80/2021-03-23/meta-data/events/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,766 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/events/ (200, 12b) after 1 attempts
2025-06-28 21:54:43,766 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/events/maintenance/' with {'url': 'http://***************:80/2021-03-23/meta-data/events/maintenance/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,768 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/events/maintenance/ (200, 17b) after 1 attempts
2025-06-28 21:54:43,768 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/events/maintenance/history' with {'url': 'http://***************:80/2021-03-23/meta-data/events/maintenance/history', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,772 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/events/maintenance/history (200, 2b) after 1 attempts
2025-06-28 21:54:43,772 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/events/maintenance/scheduled' with {'url': 'http://***************:80/2021-03-23/meta-data/events/maintenance/scheduled', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,774 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/events/maintenance/scheduled (200, 2b) after 1 attempts
2025-06-28 21:54:43,774 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/iam/' with {'url': 'http://***************:80/2021-03-23/meta-data/iam/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,776 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/iam/ (200, 26b) after 1 attempts
2025-06-28 21:54:43,777 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/iam/info' with {'url': 'http://***************:80/2021-03-23/meta-data/iam/info', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,778 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/iam/info (200, 216b) after 1 attempts
2025-06-28 21:54:43,779 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/identity-credentials/' with {'url': 'http://***************:80/2021-03-23/meta-data/identity-credentials/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,781 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/identity-credentials/ (200, 4b) after 1 attempts
2025-06-28 21:54:43,781 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/identity-credentials/ec2/' with {'url': 'http://***************:80/2021-03-23/meta-data/identity-credentials/ec2/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,783 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/identity-credentials/ec2/ (200, 26b) after 1 attempts
2025-06-28 21:54:43,783 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/identity-credentials/ec2/info' with {'url': 'http://***************:80/2021-03-23/meta-data/identity-credentials/ec2/info', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,785 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/identity-credentials/ec2/info (200, 98b) after 1 attempts
2025-06-28 21:54:43,785 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/metrics/' with {'url': 'http://***************:80/2021-03-23/meta-data/metrics/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,786 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/metrics/ (200, 7b) after 1 attempts
2025-06-28 21:54:43,787 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/metrics/vhostmd' with {'url': 'http://***************:80/2021-03-23/meta-data/metrics/vhostmd', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,788 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/metrics/vhostmd (200, 38b) after 1 attempts
2025-06-28 21:54:43,789 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/' with {'url': 'http://***************:80/2021-03-23/meta-data/network/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,790 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/ (200, 11b) after 1 attempts
2025-06-28 21:54:43,791 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,793 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/ (200, 5b) after 1 attempts
2025-06-28 21:54:43,793 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,795 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/ (200, 18b) after 1 attempts
2025-06-28 21:54:43,795 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,797 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/ (200, 230b) after 1 attempts
2025-06-28 21:54:43,797 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/ipv4-associations/' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/ipv4-associations/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,799 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/ipv4-associations/ (200, 14b) after 1 attempts
2025-06-28 21:54:43,800 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/ipv4-associations/**************' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/ipv4-associations/**************', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,803 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/ipv4-associations/************** (200, 13b) after 1 attempts
2025-06-28 21:54:43,803 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/device-number' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/device-number', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,806 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/device-number (200, 1b) after 1 attempts
2025-06-28 21:54:43,806 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/interface-id' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/interface-id', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,808 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/interface-id (200, 21b) after 1 attempts
2025-06-28 21:54:43,809 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/local-hostname' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/local-hostname', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,811 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/local-hostname (200, 43b) after 1 attempts
2025-06-28 21:54:43,811 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/local-ipv4s' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/local-ipv4s', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,813 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/local-ipv4s (200, 13b) after 1 attempts
2025-06-28 21:54:43,813 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/mac' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/mac', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,816 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/mac (200, 17b) after 1 attempts
2025-06-28 21:54:43,816 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/owner-id' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/owner-id', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,820 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/owner-id (200, 12b) after 1 attempts
2025-06-28 21:54:43,821 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/public-hostname' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/public-hostname', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,823 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/public-hostname (200, 50b) after 1 attempts
2025-06-28 21:54:43,823 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/public-ipv4s' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/public-ipv4s', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,834 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/public-ipv4s (200, 14b) after 1 attempts
2025-06-28 21:54:43,834 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/security-group-ids' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/security-group-ids', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,837 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/security-group-ids (200, 20b) after 1 attempts
2025-06-28 21:54:43,837 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/security-groups' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/security-groups', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,840 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/security-groups (200, 56b) after 1 attempts
2025-06-28 21:54:43,840 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/subnet-id' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/subnet-id', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,842 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/subnet-id (200, 24b) after 1 attempts
2025-06-28 21:54:43,842 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/subnet-ipv4-cidr-block' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/subnet-ipv4-cidr-block', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,845 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/subnet-ipv4-cidr-block (200, 14b) after 1 attempts
2025-06-28 21:54:43,845 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/vpc-id' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/vpc-id', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,847 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/vpc-id (200, 21b) after 1 attempts
2025-06-28 21:54:43,847 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/vpc-ipv4-cidr-block' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/vpc-ipv4-cidr-block', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,849 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/vpc-ipv4-cidr-block (200, 13b) after 1 attempts
2025-06-28 21:54:43,849 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/vpc-ipv4-cidr-blocks' with {'url': 'http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/vpc-ipv4-cidr-blocks', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,852 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/network/interfaces/macs/06:f5:76:99:df:0d/vpc-ipv4-cidr-blocks (200, 13b) after 1 attempts
2025-06-28 21:54:43,852 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/placement/' with {'url': 'http://***************:80/2021-03-23/meta-data/placement/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,854 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/placement/ (200, 45b) after 1 attempts
2025-06-28 21:54:43,855 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/placement/availability-zone' with {'url': 'http://***************:80/2021-03-23/meta-data/placement/availability-zone', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,857 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/placement/availability-zone (200, 10b) after 1 attempts
2025-06-28 21:54:43,857 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/placement/availability-zone-id' with {'url': 'http://***************:80/2021-03-23/meta-data/placement/availability-zone-id', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,859 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/placement/availability-zone-id (200, 8b) after 1 attempts
2025-06-28 21:54:43,859 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/placement/region' with {'url': 'http://***************:80/2021-03-23/meta-data/placement/region', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,861 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/placement/region (200, 9b) after 1 attempts
2025-06-28 21:54:43,861 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/public-keys/' with {'url': 'http://***************:80/2021-03-23/meta-data/public-keys/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,863 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/public-keys/ (200, 8b) after 1 attempts
2025-06-28 21:54:43,864 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/public-keys/0/openssh-key' with {'url': 'http://***************:80/2021-03-23/meta-data/public-keys/0/openssh-key', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,866 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/public-keys/0/openssh-key (200, 88b) after 1 attempts
2025-06-28 21:54:43,866 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/services/' with {'url': 'http://***************:80/2021-03-23/meta-data/services/', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,868 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/services/ (200, 16b) after 1 attempts
2025-06-28 21:54:43,868 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/services/domain' with {'url': 'http://***************:80/2021-03-23/meta-data/services/domain', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,870 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/services/domain (200, 13b) after 1 attempts
2025-06-28 21:54:43,870 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/services/partition' with {'url': 'http://***************:80/2021-03-23/meta-data/services/partition', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,873 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/services/partition (200, 3b) after 1 attempts
2025-06-28 21:54:43,874 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/ami-id' with {'url': 'http://***************:80/2021-03-23/meta-data/ami-id', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,876 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/ami-id (200, 21b) after 1 attempts
2025-06-28 21:54:43,876 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/ami-launch-index' with {'url': 'http://***************:80/2021-03-23/meta-data/ami-launch-index', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,878 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/ami-launch-index (200, 1b) after 1 attempts
2025-06-28 21:54:43,878 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/ami-manifest-path' with {'url': 'http://***************:80/2021-03-23/meta-data/ami-manifest-path', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,880 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/ami-manifest-path (200, 9b) after 1 attempts
2025-06-28 21:54:43,880 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/hostname' with {'url': 'http://***************:80/2021-03-23/meta-data/hostname', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,882 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/hostname (200, 43b) after 1 attempts
2025-06-28 21:54:43,882 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/instance-action' with {'url': 'http://***************:80/2021-03-23/meta-data/instance-action', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,884 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/instance-action (200, 4b) after 1 attempts
2025-06-28 21:54:43,884 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/instance-id' with {'url': 'http://***************:80/2021-03-23/meta-data/instance-id', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,886 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/instance-id (200, 19b) after 1 attempts
2025-06-28 21:54:43,887 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/instance-life-cycle' with {'url': 'http://***************:80/2021-03-23/meta-data/instance-life-cycle', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,888 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/instance-life-cycle (200, 9b) after 1 attempts
2025-06-28 21:54:43,889 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/instance-type' with {'url': 'http://***************:80/2021-03-23/meta-data/instance-type', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,891 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/instance-type (200, 8b) after 1 attempts
2025-06-28 21:54:43,891 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/local-hostname' with {'url': 'http://***************:80/2021-03-23/meta-data/local-hostname', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,893 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/local-hostname (200, 43b) after 1 attempts
2025-06-28 21:54:43,893 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/local-ipv4' with {'url': 'http://***************:80/2021-03-23/meta-data/local-ipv4', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,895 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/local-ipv4 (200, 13b) after 1 attempts
2025-06-28 21:54:43,896 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/mac' with {'url': 'http://***************:80/2021-03-23/meta-data/mac', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,898 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/mac (200, 17b) after 1 attempts
2025-06-28 21:54:43,899 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/profile' with {'url': 'http://***************:80/2021-03-23/meta-data/profile', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,901 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/profile (200, 11b) after 1 attempts
2025-06-28 21:54:43,901 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/public-hostname' with {'url': 'http://***************:80/2021-03-23/meta-data/public-hostname', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,903 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/public-hostname (200, 50b) after 1 attempts
2025-06-28 21:54:43,903 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/public-ipv4' with {'url': 'http://***************:80/2021-03-23/meta-data/public-ipv4', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,906 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/public-ipv4 (200, 14b) after 1 attempts
2025-06-28 21:54:43,906 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/reservation-id' with {'url': 'http://***************:80/2021-03-23/meta-data/reservation-id', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,908 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/reservation-id (200, 19b) after 1 attempts
2025-06-28 21:54:43,908 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/meta-data/security-groups' with {'url': 'http://***************:80/2021-03-23/meta-data/security-groups', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,911 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/meta-data/security-groups (200, 56b) after 1 attempts
2025-06-28 21:54:43,911 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/dynamic/instance-identity' with {'url': 'http://***************:80/2021-03-23/dynamic/instance-identity', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,913 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/dynamic/instance-identity (200, 32b) after 1 attempts
2025-06-28 21:54:43,913 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/dynamic/instance-identity/document' with {'url': 'http://***************:80/2021-03-23/dynamic/instance-identity/document', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,915 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/dynamic/instance-identity/document (200, 477b) after 1 attempts
2025-06-28 21:54:43,915 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/dynamic/instance-identity/pkcs7' with {'url': 'http://***************:80/2021-03-23/dynamic/instance-identity/pkcs7', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,917 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/dynamic/instance-identity/pkcs7 (200, 1171b) after 1 attempts
2025-06-28 21:54:43,918 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/dynamic/instance-identity/rsa2048' with {'url': 'http://***************:80/2021-03-23/dynamic/instance-identity/rsa2048', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,920 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/dynamic/instance-identity/rsa2048 (200, 1491b) after 1 attempts
2025-06-28 21:54:43,921 - url_helper.py[DEBUG]: [0/6] open 'http://***************:80/2021-03-23/dynamic/instance-identity/signature' with {'url': 'http://***************:80/2021-03-23/dynamic/instance-identity/signature', 'allow_redirects': True, 'method': 'GET', 'timeout': 5.0, 'headers': {'X-aws-ec2-metadata-token': 'REDACTED'}} configuration
2025-06-28 21:54:43,923 - url_helper.py[DEBUG]: Read from http://***************:80/2021-03-23/dynamic/instance-identity/signature (200, 174b) after 1 attempts
2025-06-28 21:54:43,923 - util.py[DEBUG]: Crawl of metadata service took 0.221 seconds
2025-06-28 21:54:43,925 - util.py[DEBUG]: Writing to /run/cloud-init/cloud-id-aws - wb: [644] 4 bytes
2025-06-28 21:54:43,926 - util.py[DEBUG]: Restoring selinux mode for /run/cloud-init/cloud-id-aws (recursive=False)
2025-06-28 21:54:43,927 - util.py[DEBUG]: Restoring selinux mode for /run/cloud-init/cloud-id-aws (recursive=False)
2025-06-28 21:54:43,927 - util.py[DEBUG]: Creating symbolic link from '/run/cloud-init/cloud-id' => '/run/cloud-init/cloud-id-aws'
2025-06-28 21:54:43,928 - atomic_helper.py[DEBUG]: Atomically writing to file /run/cloud-init/instance-data-sensitive.json (via temporary file /run/cloud-init/tmpf5emo24i) - w: [600] 12278 bytes/chars
2025-06-28 21:54:43,929 - atomic_helper.py[DEBUG]: Atomically writing to file /run/cloud-init/instance-data.json (via temporary file /run/cloud-init/tmpjw74_q_4) - w: [644] 8136 bytes/chars
2025-06-28 21:54:43,929 - handlers.py[DEBUG]: finish: init-network/search-Ec2: SUCCESS: found network data from DataSourceEc2
2025-06-28 21:54:43,929 - stages.py[INFO]: Loaded datasource DataSourceEc2 - DataSourceEc2
2025-06-28 21:54:43,929 - util.py[DEBUG]: Reading from /etc/cloud/cloud.cfg (quiet=False)
2025-06-28 21:54:43,929 - util.py[DEBUG]: Read 2553 bytes from /etc/cloud/cloud.cfg
2025-06-28 21:54:43,929 - util.py[DEBUG]: Attempting to load yaml from string of length 2553 with allowed root types (<class 'dict'>,)
2025-06-28 21:54:43,936 - util.py[DEBUG]: Reading from /etc/cloud/cloud.cfg.d/40_selinux-reboot.cfg (quiet=False)
2025-06-28 21:54:43,936 - util.py[DEBUG]: Read 174 bytes from /etc/cloud/cloud.cfg.d/40_selinux-reboot.cfg
2025-06-28 21:54:43,936 - util.py[DEBUG]: Attempting to load yaml from string of length 174 with allowed root types (<class 'dict'>,)
2025-06-28 21:54:43,937 - util.py[DEBUG]: Reading from /etc/cloud/cloud.cfg.d/10_aws_dnfvars.cfg (quiet=False)
2025-06-28 21:54:43,937 - util.py[DEBUG]: Read 591 bytes from /etc/cloud/cloud.cfg.d/10_aws_dnfvars.cfg
2025-06-28 21:54:43,937 - util.py[DEBUG]: Attempting to load yaml from string of length 591 with allowed root types (<class 'dict'>,)
2025-06-28 21:54:43,939 - util.py[DEBUG]: Reading from /etc/cloud/cloud.cfg.d/05_logging.cfg (quiet=False)
2025-06-28 21:54:43,939 - util.py[DEBUG]: Read 2070 bytes from /etc/cloud/cloud.cfg.d/05_logging.cfg
2025-06-28 21:54:43,939 - util.py[DEBUG]: Attempting to load yaml from string of length 2070 with allowed root types (<class 'dict'>,)
2025-06-28 21:54:43,942 - util.py[DEBUG]: Reading from /etc/cloud/cloud.cfg.d/01_amazon-ec2.cfg (quiet=False)
2025-06-28 21:54:43,942 - util.py[DEBUG]: Read 237 bytes from /etc/cloud/cloud.cfg.d/01_amazon-ec2.cfg
2025-06-28 21:54:43,942 - util.py[DEBUG]: Attempting to load yaml from string of length 237 with allowed root types (<class 'dict'>,)
2025-06-28 21:54:43,943 - util.py[DEBUG]: Reading from /run/cloud-init/cloud.cfg (quiet=False)
2025-06-28 21:54:43,943 - util.py[DEBUG]: Attempting to load yaml from string of length 0 with allowed root types (<class 'dict'>,)
2025-06-28 21:54:43,943 - util.py[DEBUG]: loaded blob returned None, returning default.
2025-06-28 21:54:43,944 - util.py[DEBUG]: Attempting to remove /var/lib/cloud/instance
2025-06-28 21:54:43,944 - util.py[DEBUG]: Creating symbolic link from '/var/lib/cloud/instance' => '/var/lib/cloud/instances/i-08b2210ccd2afe431'
2025-06-28 21:54:43,944 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431 (recursive=True)
2025-06-28 21:54:43,948 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/handlers (recursive=False)
2025-06-28 21:54:43,948 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431 (recursive=True)
2025-06-28 21:54:43,952 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/scripts (recursive=False)
2025-06-28 21:54:43,952 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431 (recursive=True)
2025-06-28 21:54:43,957 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem (recursive=False)
2025-06-28 21:54:43,957 - util.py[DEBUG]: Reading from /var/lib/cloud/instances/i-08b2210ccd2afe431/datasource (quiet=False)
2025-06-28 21:54:43,957 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-08b2210ccd2afe431/datasource - wb: [644] 29 bytes
2025-06-28 21:54:43,958 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/datasource (recursive=False)
2025-06-28 21:54:43,958 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/datasource (recursive=False)
2025-06-28 21:54:43,959 - util.py[DEBUG]: Writing to /var/lib/cloud/data/previous-datasource - wb: [644] 29 bytes
2025-06-28 21:54:43,959 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/data/previous-datasource (recursive=False)
2025-06-28 21:54:43,960 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/data/previous-datasource (recursive=False)
2025-06-28 21:54:43,960 - util.py[DEBUG]: Reading from /var/lib/cloud/data/instance-id (quiet=False)
2025-06-28 21:54:43,960 - stages.py[DEBUG]: previous iid found to be NO_PREVIOUS_INSTANCE_ID
2025-06-28 21:54:43,960 - util.py[DEBUG]: Writing to /var/lib/cloud/data/instance-id - wb: [644] 20 bytes
2025-06-28 21:54:43,960 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/data/instance-id (recursive=False)
2025-06-28 21:54:43,961 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/data/instance-id (recursive=False)
2025-06-28 21:54:43,961 - util.py[DEBUG]: Writing to /run/cloud-init/.instance-id - wb: [644] 20 bytes
2025-06-28 21:54:43,962 - util.py[DEBUG]: Restoring selinux mode for /run/cloud-init/.instance-id (recursive=False)
2025-06-28 21:54:43,962 - util.py[DEBUG]: Restoring selinux mode for /run/cloud-init/.instance-id (recursive=False)
2025-06-28 21:54:43,962 - util.py[DEBUG]: Writing to /var/lib/cloud/data/previous-instance-id - wb: [644] 24 bytes
2025-06-28 21:54:43,963 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/data/previous-instance-id (recursive=False)
2025-06-28 21:54:43,963 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/data/previous-instance-id (recursive=False)
2025-06-28 21:54:43,964 - util.py[DEBUG]: Writing to /var/lib/cloud/instance/obj.pkl - wb: [400] 12743 bytes
2025-06-28 21:54:43,964 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/obj.pkl (recursive=False)
2025-06-28 21:54:43,965 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/obj.pkl (recursive=False)
2025-06-28 21:54:43,965 - main.py[DEBUG]: [net] init will now be targeting instance id: i-08b2210ccd2afe431. new=True
2025-06-28 21:54:43,965 - util.py[DEBUG]: Reading from /etc/cloud/cloud.cfg (quiet=False)
2025-06-28 21:54:43,966 - util.py[DEBUG]: Read 2553 bytes from /etc/cloud/cloud.cfg
2025-06-28 21:54:43,966 - util.py[DEBUG]: Attempting to load yaml from string of length 2553 with allowed root types (<class 'dict'>,)
2025-06-28 21:54:43,972 - util.py[DEBUG]: Reading from /etc/cloud/cloud.cfg.d/40_selinux-reboot.cfg (quiet=False)
2025-06-28 21:54:43,972 - util.py[DEBUG]: Read 174 bytes from /etc/cloud/cloud.cfg.d/40_selinux-reboot.cfg
2025-06-28 21:54:43,972 - util.py[DEBUG]: Attempting to load yaml from string of length 174 with allowed root types (<class 'dict'>,)
2025-06-28 21:54:43,973 - util.py[DEBUG]: Reading from /etc/cloud/cloud.cfg.d/10_aws_dnfvars.cfg (quiet=False)
2025-06-28 21:54:43,973 - util.py[DEBUG]: Read 591 bytes from /etc/cloud/cloud.cfg.d/10_aws_dnfvars.cfg
2025-06-28 21:54:43,973 - util.py[DEBUG]: Attempting to load yaml from string of length 591 with allowed root types (<class 'dict'>,)
2025-06-28 21:54:43,975 - util.py[DEBUG]: Reading from /etc/cloud/cloud.cfg.d/05_logging.cfg (quiet=False)
2025-06-28 21:54:43,975 - util.py[DEBUG]: Read 2070 bytes from /etc/cloud/cloud.cfg.d/05_logging.cfg
2025-06-28 21:54:43,975 - util.py[DEBUG]: Attempting to load yaml from string of length 2070 with allowed root types (<class 'dict'>,)
2025-06-28 21:54:43,977 - util.py[DEBUG]: Reading from /etc/cloud/cloud.cfg.d/01_amazon-ec2.cfg (quiet=False)
2025-06-28 21:54:43,977 - util.py[DEBUG]: Read 237 bytes from /etc/cloud/cloud.cfg.d/01_amazon-ec2.cfg
2025-06-28 21:54:43,978 - util.py[DEBUG]: Attempting to load yaml from string of length 237 with allowed root types (<class 'dict'>,)
2025-06-28 21:54:43,979 - util.py[DEBUG]: Reading from /run/cloud-init/cloud.cfg (quiet=False)
2025-06-28 21:54:43,979 - util.py[DEBUG]: Attempting to load yaml from string of length 0 with allowed root types (<class 'dict'>,)
2025-06-28 21:54:43,979 - util.py[DEBUG]: loaded blob returned None, returning default.
2025-06-28 21:54:43,980 - util.py[DEBUG]: Reading from /sys/class/net/ens5/address (quiet=False)
2025-06-28 21:54:43,980 - util.py[DEBUG]: Read 18 bytes from /sys/class/net/ens5/address
2025-06-28 21:54:43,980 - util.py[DEBUG]: Reading from /sys/class/net/lo/address (quiet=False)
2025-06-28 21:54:43,980 - util.py[DEBUG]: Read 18 bytes from /sys/class/net/lo/address
2025-06-28 21:54:43,980 - util.py[DEBUG]: Reading from /sys/class/net/ens5/name_assign_type (quiet=False)
2025-06-28 21:54:43,980 - util.py[DEBUG]: Read 2 bytes from /sys/class/net/ens5/name_assign_type
2025-06-28 21:54:43,980 - util.py[DEBUG]: Reading from /sys/class/net/ens5/address (quiet=False)
2025-06-28 21:54:43,980 - util.py[DEBUG]: Read 18 bytes from /sys/class/net/ens5/address
2025-06-28 21:54:43,981 - util.py[DEBUG]: Reading from /sys/class/net/ens5/carrier (quiet=False)
2025-06-28 21:54:43,981 - util.py[DEBUG]: Read 2 bytes from /sys/class/net/ens5/carrier
2025-06-28 21:54:43,981 - util.py[DEBUG]: Reading from /sys/class/net/ens5/addr_assign_type (quiet=False)
2025-06-28 21:54:43,981 - util.py[DEBUG]: Read 2 bytes from /sys/class/net/ens5/addr_assign_type
2025-06-28 21:54:43,981 - util.py[DEBUG]: Reading from /sys/class/net/ens5/uevent (quiet=False)
2025-06-28 21:54:43,981 - util.py[DEBUG]: Read 25 bytes from /sys/class/net/ens5/uevent
2025-06-28 21:54:43,981 - util.py[DEBUG]: Reading from /sys/class/net/ens5/address (quiet=False)
2025-06-28 21:54:43,981 - util.py[DEBUG]: Read 18 bytes from /sys/class/net/ens5/address
2025-06-28 21:54:43,981 - __init__.py[DEBUG]: ovs-vsctl not in PATH; not detecting Open vSwitch interfaces
2025-06-28 21:54:43,981 - util.py[DEBUG]: Reading from /sys/class/net/ens5/device/device (quiet=False)
2025-06-28 21:54:43,981 - util.py[DEBUG]: Read 7 bytes from /sys/class/net/ens5/device/device
2025-06-28 21:54:43,981 - util.py[DEBUG]: Reading from /sys/class/net/lo/addr_assign_type (quiet=False)
2025-06-28 21:54:43,981 - util.py[DEBUG]: Read 2 bytes from /sys/class/net/lo/addr_assign_type
2025-06-28 21:54:43,981 - util.py[DEBUG]: Reading from /sys/class/net/lo/uevent (quiet=False)
2025-06-28 21:54:43,981 - util.py[DEBUG]: Read 23 bytes from /sys/class/net/lo/uevent
2025-06-28 21:54:43,981 - util.py[DEBUG]: Reading from /sys/class/net/lo/address (quiet=False)
2025-06-28 21:54:43,981 - util.py[DEBUG]: Read 18 bytes from /sys/class/net/lo/address
2025-06-28 21:54:43,981 - util.py[DEBUG]: Reading from /sys/class/net/lo/device/device (quiet=False)
2025-06-28 21:54:43,982 - util.py[DEBUG]: Reading from /sys/class/net/ens5/type (quiet=False)
2025-06-28 21:54:43,982 - util.py[DEBUG]: Read 2 bytes from /sys/class/net/ens5/type
2025-06-28 21:54:43,982 - util.py[DEBUG]: Reading from /sys/class/net/lo/type (quiet=False)
2025-06-28 21:54:43,982 - util.py[DEBUG]: Read 4 bytes from /sys/class/net/lo/type
2025-06-28 21:54:43,982 - stages.py[DEBUG]: network config disabled by system_cfg
2025-06-28 21:54:43,982 - stages.py[INFO]: network config is disabled by system_cfg
2025-06-28 21:54:43,982 - handlers.py[DEBUG]: start: init-network/setup-datasource: setting up datasource
2025-06-28 21:54:43,982 - handlers.py[DEBUG]: finish: init-network/setup-datasource: SUCCESS: setting up datasource
2025-06-28 21:54:43,982 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-08b2210ccd2afe431/user-data.txt - wb: [600] 2478 bytes
2025-06-28 21:54:43,982 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/user-data.txt (recursive=False)
2025-06-28 21:54:43,983 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/user-data.txt (recursive=False)
2025-06-28 21:54:43,985 - util.py[DEBUG]: Attempting to load yaml from string of length 104 with allowed root types (<class 'dict'>,)
2025-06-28 21:54:43,987 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-08b2210ccd2afe431/user-data.txt.i - wb: [600] 2500 bytes
2025-06-28 21:54:43,988 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/user-data.txt.i (recursive=False)
2025-06-28 21:54:43,989 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/user-data.txt.i (recursive=False)
2025-06-28 21:54:43,989 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-08b2210ccd2afe431/vendor-data.txt - wb: [600] 0 bytes
2025-06-28 21:54:43,989 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/vendor-data.txt (recursive=False)
2025-06-28 21:54:43,990 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/vendor-data.txt (recursive=False)
2025-06-28 21:54:43,991 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-08b2210ccd2afe431/vendor-data.txt.i - wb: [600] 308 bytes
2025-06-28 21:54:43,991 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/vendor-data.txt.i (recursive=False)
2025-06-28 21:54:43,992 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/vendor-data.txt.i (recursive=False)
2025-06-28 21:54:43,993 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-08b2210ccd2afe431/vendor-data2.txt - wb: [600] 0 bytes
2025-06-28 21:54:43,993 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/vendor-data2.txt (recursive=False)
2025-06-28 21:54:43,994 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/vendor-data2.txt (recursive=False)
2025-06-28 21:54:43,995 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-08b2210ccd2afe431/vendor-data2.txt.i - wb: [600] 308 bytes
2025-06-28 21:54:43,995 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/vendor-data2.txt.i (recursive=False)
2025-06-28 21:54:43,996 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/vendor-data2.txt.i (recursive=False)
2025-06-28 21:54:43,996 - stages.py[DEBUG]: Using distro class <class 'cloudinit.distros.amazon.Distro'>
2025-06-28 21:54:43,997 - cc_set_hostname.py[DEBUG]: Setting the hostname to ip-172-31-36-197.us-west-2.compute.internal (ip-172-31-36-197)
2025-06-28 21:54:43,997 - subp.py[DEBUG]: Running command ['hostnamectl', 'set-hostname', 'ip-172-31-36-197.us-west-2.compute.internal'] with allowed return codes [0] (shell=False, capture=True)
2025-06-28 21:54:44,089 - __init__.py[DEBUG]: Non-persistently setting the system hostname to ip-172-31-36-197.us-west-2.compute.internal
2025-06-28 21:54:44,089 - subp.py[DEBUG]: Running command ['hostname', 'ip-172-31-36-197.us-west-2.compute.internal'] with allowed return codes [0] (shell=False, capture=True)
2025-06-28 21:54:44,094 - atomic_helper.py[DEBUG]: Atomically writing to file /var/lib/cloud/data/set-hostname (via temporary file /var/lib/cloud/data/tmpcar21of7) - w: [644] 92 bytes/chars
2025-06-28 21:54:44,094 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/consume_data - wb: [644] 24 bytes
2025-06-28 21:54:44,095 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/consume_data (recursive=False)
2025-06-28 21:54:44,096 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/consume_data (recursive=False)
2025-06-28 21:54:44,097 - helpers.py[DEBUG]: Running consume_data using lock (<FileLock using file '/var/lib/cloud/instances/i-08b2210ccd2afe431/sem/consume_data'>)
2025-06-28 21:54:44,097 - handlers.py[DEBUG]: start: init-network/consume-user-data: reading and applying user-data
2025-06-28 21:54:44,097 - launch_index.py[DEBUG]: Discarding 0 multipart messages which do not match launch index 0
2025-06-28 21:54:44,098 - stages.py[DEBUG]: Added default handler for {'text/cloud-config-jsonp', 'text/cloud-config'} from CloudConfigPartHandler: [['text/cloud-config', 'text/cloud-config-jsonp']]
2025-06-28 21:54:44,098 - stages.py[DEBUG]: Added default handler for {'text/x-shellscript'} from ShellScriptPartHandler: [['text/x-shellscript']]
2025-06-28 21:54:44,098 - stages.py[DEBUG]: Added default handler for {'text/x-shellscript-per-boot'} from ShellScriptByFreqPartHandler: [['text/x-shellscript-per-boot']]
2025-06-28 21:54:44,098 - stages.py[DEBUG]: Added default handler for {'text/x-shellscript-per-instance'} from ShellScriptByFreqPartHandler: [['text/x-shellscript-per-instance']]
2025-06-28 21:54:44,098 - stages.py[DEBUG]: Added default handler for {'text/x-shellscript-per-once'} from ShellScriptByFreqPartHandler: [['text/x-shellscript-per-once']]
2025-06-28 21:54:44,098 - stages.py[DEBUG]: Added default handler for {'text/cloud-boothook'} from BootHookPartHandler: [['text/cloud-boothook']]
2025-06-28 21:54:44,098 - stages.py[DEBUG]: Added default handler for {'text/jinja2'} from JinjaTemplatePartHandler: [['text/jinja2']]
2025-06-28 21:54:44,098 - __init__.py[DEBUG]: Calling handler CloudConfigPartHandler: [['text/cloud-config', 'text/cloud-config-jsonp']] (__begin__, None, 3) with frequency once-per-instance
2025-06-28 21:54:44,098 - __init__.py[DEBUG]: Calling handler ShellScriptPartHandler: [['text/x-shellscript']] (__begin__, None, 2) with frequency once-per-instance
2025-06-28 21:54:44,098 - __init__.py[DEBUG]: Calling handler ShellScriptByFreqPartHandler: [['text/x-shellscript-per-boot']] (__begin__, None, 2) with frequency once-per-instance
2025-06-28 21:54:44,098 - __init__.py[DEBUG]: Calling handler ShellScriptByFreqPartHandler: [['text/x-shellscript-per-instance']] (__begin__, None, 2) with frequency once-per-instance
2025-06-28 21:54:44,099 - __init__.py[DEBUG]: Calling handler ShellScriptByFreqPartHandler: [['text/x-shellscript-per-once']] (__begin__, None, 2) with frequency once-per-instance
2025-06-28 21:54:44,099 - __init__.py[DEBUG]: Calling handler BootHookPartHandler: [['text/cloud-boothook']] (__begin__, None, 2) with frequency once-per-instance
2025-06-28 21:54:44,099 - __init__.py[DEBUG]: Calling handler JinjaTemplatePartHandler: [['text/jinja2']] (__begin__, None, 3) with frequency once-per-instance
2025-06-28 21:54:44,099 - __init__.py[DEBUG]: {'Content-Type': 'text/cloud-config; charset="us-ascii"', 'MIME-Version': '1.0', 'Content-Transfer-Encoding': '7bit', 'Content-Disposition': 'attachment; filename="cloud-config.txt"'}
2025-06-28 21:54:44,099 - __init__.py[DEBUG]: Calling handler CloudConfigPartHandler: [['text/cloud-config', 'text/cloud-config-jsonp']] (text/cloud-config, cloud-config.txt, 3) with frequency once-per-instance
2025-06-28 21:54:44,099 - util.py[DEBUG]: Attempting to load yaml from string of length 104 with allowed root types (<class 'dict'>,)
2025-06-28 21:54:44,100 - cloud_config.py[DEBUG]: Merging by applying [('dict', ['replace']), ('list', []), ('str', [])]
2025-06-28 21:54:44,100 - __init__.py[DEBUG]: {'Content-Type': 'text/x-shellscript; charset="us-ascii"', 'MIME-Version': '1.0', 'Content-Transfer-Encoding': '7bit', 'Content-Disposition': 'attachment; filename="user-data.txt"'}
2025-06-28 21:54:44,100 - __init__.py[DEBUG]: Calling handler ShellScriptPartHandler: [['text/x-shellscript']] (text/x-shellscript, user-data.txt, 2) with frequency once-per-instance
2025-06-28 21:54:44,100 - util.py[DEBUG]: Writing to /var/lib/cloud/instance/scripts/user-data.txt - wb: [700] 1829 bytes
2025-06-28 21:54:44,101 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/scripts/user-data.txt (recursive=False)
2025-06-28 21:54:44,102 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/scripts/user-data.txt (recursive=False)
2025-06-28 21:54:44,102 - __init__.py[DEBUG]: Calling handler CloudConfigPartHandler: [['text/cloud-config', 'text/cloud-config-jsonp']] (__end__, None, 3) with frequency once-per-instance
2025-06-28 21:54:44,103 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-08b2210ccd2afe431/cloud-config.txt - wb: [600] 154 bytes
2025-06-28 21:54:44,103 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/cloud-config.txt (recursive=False)
2025-06-28 21:54:44,104 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/cloud-config.txt (recursive=False)
2025-06-28 21:54:44,105 - __init__.py[DEBUG]: Calling handler ShellScriptPartHandler: [['text/x-shellscript']] (__end__, None, 2) with frequency once-per-instance
2025-06-28 21:54:44,105 - __init__.py[DEBUG]: Calling handler ShellScriptByFreqPartHandler: [['text/x-shellscript-per-boot']] (__end__, None, 2) with frequency once-per-instance
2025-06-28 21:54:44,105 - __init__.py[DEBUG]: Calling handler ShellScriptByFreqPartHandler: [['text/x-shellscript-per-instance']] (__end__, None, 2) with frequency once-per-instance
2025-06-28 21:54:44,105 - __init__.py[DEBUG]: Calling handler ShellScriptByFreqPartHandler: [['text/x-shellscript-per-once']] (__end__, None, 2) with frequency once-per-instance
2025-06-28 21:54:44,105 - __init__.py[DEBUG]: Calling handler BootHookPartHandler: [['text/cloud-boothook']] (__end__, None, 2) with frequency once-per-instance
2025-06-28 21:54:44,105 - __init__.py[DEBUG]: Calling handler JinjaTemplatePartHandler: [['text/jinja2']] (__end__, None, 3) with frequency once-per-instance
2025-06-28 21:54:44,105 - handlers.py[DEBUG]: finish: init-network/consume-user-data: SUCCESS: reading and applying user-data
2025-06-28 21:54:44,105 - handlers.py[DEBUG]: start: init-network/consume-vendor-data: reading and applying vendor-data
2025-06-28 21:54:44,105 - stages.py[DEBUG]: no vendordata from datasource
2025-06-28 21:54:44,105 - handlers.py[DEBUG]: finish: init-network/consume-vendor-data: SUCCESS: reading and applying vendor-data
2025-06-28 21:54:44,105 - handlers.py[DEBUG]: start: init-network/consume-vendor-data2: reading and applying vendor-data2
2025-06-28 21:54:44,105 - stages.py[DEBUG]: no vendordata2 from datasource
2025-06-28 21:54:44,105 - handlers.py[DEBUG]: finish: init-network/consume-vendor-data2: SUCCESS: reading and applying vendor-data2
2025-06-28 21:54:44,105 - util.py[DEBUG]: Reading from /etc/cloud/cloud.cfg (quiet=False)
2025-06-28 21:54:44,105 - util.py[DEBUG]: Read 2553 bytes from /etc/cloud/cloud.cfg
2025-06-28 21:54:44,105 - util.py[DEBUG]: Attempting to load yaml from string of length 2553 with allowed root types (<class 'dict'>,)
2025-06-28 21:54:44,112 - util.py[DEBUG]: Reading from /etc/cloud/cloud.cfg.d/40_selinux-reboot.cfg (quiet=False)
2025-06-28 21:54:44,112 - util.py[DEBUG]: Read 174 bytes from /etc/cloud/cloud.cfg.d/40_selinux-reboot.cfg
2025-06-28 21:54:44,112 - util.py[DEBUG]: Attempting to load yaml from string of length 174 with allowed root types (<class 'dict'>,)
2025-06-28 21:54:44,113 - util.py[DEBUG]: Reading from /etc/cloud/cloud.cfg.d/10_aws_dnfvars.cfg (quiet=False)
2025-06-28 21:54:44,113 - util.py[DEBUG]: Read 591 bytes from /etc/cloud/cloud.cfg.d/10_aws_dnfvars.cfg
2025-06-28 21:54:44,113 - util.py[DEBUG]: Attempting to load yaml from string of length 591 with allowed root types (<class 'dict'>,)
2025-06-28 21:54:44,115 - util.py[DEBUG]: Reading from /etc/cloud/cloud.cfg.d/05_logging.cfg (quiet=False)
2025-06-28 21:54:44,115 - util.py[DEBUG]: Read 2070 bytes from /etc/cloud/cloud.cfg.d/05_logging.cfg
2025-06-28 21:54:44,115 - util.py[DEBUG]: Attempting to load yaml from string of length 2070 with allowed root types (<class 'dict'>,)
2025-06-28 21:54:44,118 - util.py[DEBUG]: Reading from /etc/cloud/cloud.cfg.d/01_amazon-ec2.cfg (quiet=False)
2025-06-28 21:54:44,118 - util.py[DEBUG]: Read 237 bytes from /etc/cloud/cloud.cfg.d/01_amazon-ec2.cfg
2025-06-28 21:54:44,118 - util.py[DEBUG]: Attempting to load yaml from string of length 237 with allowed root types (<class 'dict'>,)
2025-06-28 21:54:44,119 - util.py[DEBUG]: Reading from /run/cloud-init/cloud.cfg (quiet=False)
2025-06-28 21:54:44,119 - util.py[DEBUG]: Attempting to load yaml from string of length 0 with allowed root types (<class 'dict'>,)
2025-06-28 21:54:44,119 - util.py[DEBUG]: loaded blob returned None, returning default.
2025-06-28 21:54:44,119 - util.py[DEBUG]: Reading from /var/lib/cloud/instance/cloud-config.txt (quiet=False)
2025-06-28 21:54:44,119 - util.py[DEBUG]: Read 154 bytes from /var/lib/cloud/instance/cloud-config.txt
2025-06-28 21:54:44,119 - util.py[DEBUG]: Attempting to load yaml from string of length 154 with allowed root types (<class 'dict'>,)
2025-06-28 21:54:44,121 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-28 21:54:44,121 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-28 21:54:44,189 - util.py[DEBUG]: Reading from /var/lib/cloud/instance/cloud-config.txt (quiet=False)
2025-06-28 21:54:44,189 - util.py[DEBUG]: Read 154 bytes from /var/lib/cloud/instance/cloud-config.txt
2025-06-28 21:54:44,189 - util.py[DEBUG]: Attempting to load yaml from string of length 154 with allowed root types (<class 'dict'>,)
2025-06-28 21:54:44,193 - handlers.py[DEBUG]: start: init-network/activate-datasource: activating datasource
2025-06-28 21:54:44,193 - util.py[DEBUG]: Writing to /var/lib/cloud/instance/obj.pkl - wb: [400] 16920 bytes
2025-06-28 21:54:44,195 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/obj.pkl (recursive=False)
2025-06-28 21:54:44,195 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/obj.pkl (recursive=False)
2025-06-28 21:54:44,196 - handlers.py[DEBUG]: finish: init-network/activate-datasource: SUCCESS: activating datasource
2025-06-28 21:54:44,196 - main.py[DEBUG]: no di_report found in config.
2025-06-28 21:54:44,198 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-28 21:54:44,198 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-28 21:54:44,201 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-28 21:54:44,201 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-28 21:54:44,203 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-28 21:54:44,203 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-28 21:54:44,205 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-28 21:54:44,206 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-28 21:54:44,208 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-28 21:54:44,208 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-28 21:54:44,212 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-28 21:54:44,212 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-28 21:54:44,215 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-28 21:54:44,215 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-28 21:54:44,221 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-28 21:54:44,221 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-28 21:54:44,223 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-28 21:54:44,223 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-28 21:54:44,226 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-28 21:54:44,226 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-28 21:54:44,228 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-28 21:54:44,228 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-28 21:54:44,230 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-28 21:54:44,231 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-28 21:54:44,233 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-28 21:54:44,233 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-28 21:54:44,236 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-28 21:54:44,236 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-28 21:54:44,239 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-28 21:54:44,239 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-28 21:54:44,241 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-28 21:54:44,242 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-28 21:54:44,243 - stages.py[DEBUG]: Using distro class <class 'cloudinit.distros.amazon.Distro'>
2025-06-28 21:54:44,244 - modules.py[DEBUG]: Running module migrator (<module 'cloudinit.config.cc_migrator' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_migrator.py'>) with frequency always
2025-06-28 21:54:44,244 - handlers.py[DEBUG]: start: init-network/config-migrator: running config-migrator with frequency always
2025-06-28 21:54:44,244 - helpers.py[DEBUG]: Running config-migrator using lock (<cloudinit.helpers.DummyLock object at 0x7fdac268d940>)
2025-06-28 21:54:44,244 - cc_migrator.py[DEBUG]: Migrated 0 semaphore files to there canonicalized names
2025-06-28 21:54:44,244 - handlers.py[DEBUG]: finish: init-network/config-migrator: SUCCESS: config-migrator ran successfully
2025-06-28 21:54:44,244 - modules.py[DEBUG]: Running module seed_random (<module 'cloudinit.config.cc_seed_random' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_seed_random.py'>) with frequency once-per-instance
2025-06-28 21:54:44,245 - handlers.py[DEBUG]: start: init-network/config-seed_random: running config-seed_random with frequency once-per-instance
2025-06-28 21:54:44,245 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_seed_random - wb: [644] 25 bytes
2025-06-28 21:54:44,245 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_seed_random (recursive=False)
2025-06-28 21:54:44,246 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_seed_random (recursive=False)
2025-06-28 21:54:44,246 - helpers.py[DEBUG]: Running config-seed_random using lock (<FileLock using file '/var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_seed_random'>)
2025-06-28 21:54:44,247 - cc_seed_random.py[DEBUG]: no command provided
2025-06-28 21:54:44,247 - handlers.py[DEBUG]: finish: init-network/config-seed_random: SUCCESS: config-seed_random ran successfully
2025-06-28 21:54:44,247 - modules.py[DEBUG]: Running module bootcmd (<module 'cloudinit.config.cc_bootcmd' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_bootcmd.py'>) with frequency always
2025-06-28 21:54:44,247 - handlers.py[DEBUG]: start: init-network/config-bootcmd: running config-bootcmd with frequency always
2025-06-28 21:54:44,247 - helpers.py[DEBUG]: Running config-bootcmd using lock (<cloudinit.helpers.DummyLock object at 0x7fdac268d940>)
2025-06-28 21:54:44,247 - cc_bootcmd.py[DEBUG]: Skipping module named bootcmd, no 'bootcmd' key in configuration
2025-06-28 21:54:44,247 - handlers.py[DEBUG]: finish: init-network/config-bootcmd: SUCCESS: config-bootcmd ran successfully
2025-06-28 21:54:44,247 - modules.py[DEBUG]: Running module write-files (<module 'cloudinit.config.cc_write_files' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_write_files.py'>) with frequency once-per-instance
2025-06-28 21:54:44,247 - handlers.py[DEBUG]: start: init-network/config-write-files: running config-write-files with frequency once-per-instance
2025-06-28 21:54:44,247 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_write_files - wb: [644] 25 bytes
2025-06-28 21:54:44,248 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_write_files (recursive=False)
2025-06-28 21:54:44,248 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_write_files (recursive=False)
2025-06-28 21:54:44,249 - helpers.py[DEBUG]: Running config-write-files using lock (<FileLock using file '/var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_write_files'>)
2025-06-28 21:54:44,249 - cc_write_files.py[DEBUG]: Skipping module named write-files, no/empty 'write_files' key in configuration
2025-06-28 21:54:44,249 - handlers.py[DEBUG]: finish: init-network/config-write-files: SUCCESS: config-write-files ran successfully
2025-06-28 21:54:44,249 - modules.py[DEBUG]: Running module write-metadata (<module 'cloudinit.config.cc_write_metadata' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_write_metadata.py'>) with frequency once-per-instance
2025-06-28 21:54:44,249 - handlers.py[DEBUG]: start: init-network/config-write-metadata: running config-write-metadata with frequency once-per-instance
2025-06-28 21:54:44,249 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_write_metadata - wb: [644] 25 bytes
2025-06-28 21:54:44,250 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_write_metadata (recursive=False)
2025-06-28 21:54:44,250 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_write_metadata (recursive=False)
2025-06-28 21:54:44,251 - helpers.py[DEBUG]: Running config-write-metadata using lock (<FileLock using file '/var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_write_metadata'>)
2025-06-28 21:54:44,251 - util.py[DEBUG]: Writing to /etc/dnf/vars/awsregion - wb: [644] 9 bytes
2025-06-28 21:54:44,253 - util.py[DEBUG]: Restoring selinux mode for /etc/dnf/vars/awsregion (recursive=False)
2025-06-28 21:54:44,254 - util.py[DEBUG]: Restoring selinux mode for /etc/dnf/vars/awsregion (recursive=False)
2025-06-28 21:54:44,254 - util.py[DEBUG]: Changing the ownership of /etc/dnf/vars/awsregion to 0:0
2025-06-28 21:54:44,254 - util.py[DEBUG]: Writing to /etc/dnf/vars/awsdomain - wb: [644] 13 bytes
2025-06-28 21:54:44,254 - util.py[DEBUG]: Restoring selinux mode for /etc/dnf/vars/awsdomain (recursive=False)
2025-06-28 21:54:44,255 - util.py[DEBUG]: Restoring selinux mode for /etc/dnf/vars/awsdomain (recursive=False)
2025-06-28 21:54:44,255 - util.py[DEBUG]: Changing the ownership of /etc/dnf/vars/awsdomain to 0:0
2025-06-28 21:54:44,255 - handlers.py[DEBUG]: finish: init-network/config-write-metadata: SUCCESS: config-write-metadata ran successfully
2025-06-28 21:54:44,255 - modules.py[DEBUG]: Running module growpart (<module 'cloudinit.config.cc_growpart' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_growpart.py'>) with frequency always
2025-06-28 21:54:44,256 - handlers.py[DEBUG]: start: init-network/config-growpart: running config-growpart with frequency always
2025-06-28 21:54:44,256 - helpers.py[DEBUG]: Running config-growpart using lock (<cloudinit.helpers.DummyLock object at 0x7fdac268db50>)
2025-06-28 21:54:44,256 - cc_growpart.py[DEBUG]: No 'growpart' entry in cfg.  Using default: {'mode': 'auto', 'devices': ['/'], 'ignore_growroot_disabled': False}
2025-06-28 21:54:44,256 - subp.py[DEBUG]: Running command ['growpart', '--help'] with allowed return codes [0] (shell=False, capture=True)
2025-06-28 21:54:44,276 - util.py[DEBUG]: Reading from /proc/1452/mountinfo (quiet=False)
2025-06-28 21:54:44,276 - util.py[DEBUG]: Read 2939 bytes from /proc/1452/mountinfo
2025-06-28 21:54:44,277 - util.py[DEBUG]: Reading from /sys/class/block/nvme0n1p1/partition (quiet=False)
2025-06-28 21:54:44,277 - util.py[DEBUG]: Read 2 bytes from /sys/class/block/nvme0n1p1/partition
2025-06-28 21:54:44,277 - util.py[DEBUG]: Reading from /sys/devices/pci0000:00/0000:00:04.0/nvme/nvme0/nvme0n1/dev (quiet=False)
2025-06-28 21:54:44,277 - util.py[DEBUG]: Read 6 bytes from /sys/devices/pci0000:00/0000:00:04.0/nvme/nvme0/nvme0n1/dev
2025-06-28 21:54:44,277 - subp.py[DEBUG]: Running command ['growpart', '--dry-run', '/dev/nvme0n1', '1'] with allowed return codes [0] (shell=False, capture=True)
2025-06-28 21:54:44,327 - util.py[DEBUG]: resize_devices took 0.051 seconds
2025-06-28 21:54:44,327 - cc_growpart.py[DEBUG]: '/' NOCHANGE: no change necessary (/dev/nvme0n1, 1)
2025-06-28 21:54:44,327 - handlers.py[DEBUG]: finish: init-network/config-growpart: SUCCESS: config-growpart ran successfully
2025-06-28 21:54:44,327 - modules.py[DEBUG]: Running module resizefs (<module 'cloudinit.config.cc_resizefs' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_resizefs.py'>) with frequency always
2025-06-28 21:54:44,328 - handlers.py[DEBUG]: start: init-network/config-resizefs: running config-resizefs with frequency always
2025-06-28 21:54:44,328 - helpers.py[DEBUG]: Running config-resizefs using lock (<cloudinit.helpers.DummyLock object at 0x7fdac268d940>)
2025-06-28 21:54:44,328 - util.py[DEBUG]: Reading from /proc/1452/mountinfo (quiet=False)
2025-06-28 21:54:44,328 - util.py[DEBUG]: Read 2939 bytes from /proc/1452/mountinfo
2025-06-28 21:54:44,328 - cc_resizefs.py[DEBUG]: resize_info: dev=/dev/nvme0n1p1 mnt_point=/ path=/
2025-06-28 21:54:44,328 - cc_resizefs.py[DEBUG]: Resizing / (xfs) using xfs_growfs /
2025-06-28 21:54:44,329 - util.py[DEBUG]: Forked child 1571 who will run callback log_time
2025-06-28 21:54:44,330 - cc_resizefs.py[DEBUG]: Resizing (via forking) root filesystem (type=xfs, val=noblock)
2025-06-28 21:54:44,330 - subp.py[DEBUG]: Running command ('xfs_growfs', '/') with allowed return codes [0] (shell=False, capture=True)
2025-06-28 21:54:44,330 - handlers.py[DEBUG]: finish: init-network/config-resizefs: SUCCESS: config-resizefs ran successfully
2025-06-28 21:54:44,330 - modules.py[DEBUG]: Running module disk_setup (<module 'cloudinit.config.cc_disk_setup' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_disk_setup.py'>) with frequency once-per-instance
2025-06-28 21:54:44,331 - handlers.py[DEBUG]: start: init-network/config-disk_setup: running config-disk_setup with frequency once-per-instance
2025-06-28 21:54:44,331 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_disk_setup - wb: [644] 25 bytes
2025-06-28 21:54:44,333 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_disk_setup (recursive=False)
2025-06-28 21:54:44,335 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_disk_setup (recursive=False)
2025-06-28 21:54:44,335 - helpers.py[DEBUG]: Running config-disk_setup using lock (<FileLock using file '/var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_disk_setup'>)
2025-06-28 21:54:44,335 - handlers.py[DEBUG]: finish: init-network/config-disk_setup: SUCCESS: config-disk_setup ran successfully
2025-06-28 21:54:44,335 - modules.py[DEBUG]: Running module mounts (<module 'cloudinit.config.cc_mounts' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_mounts.py'>) with frequency once-per-instance
2025-06-28 21:54:44,336 - handlers.py[DEBUG]: start: init-network/config-mounts: running config-mounts with frequency once-per-instance
2025-06-28 21:54:44,336 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_mounts - wb: [644] 25 bytes
2025-06-28 21:54:44,336 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_mounts (recursive=False)
2025-06-28 21:54:44,337 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_mounts (recursive=False)
2025-06-28 21:54:44,337 - helpers.py[DEBUG]: Running config-mounts using lock (<FileLock using file '/var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_mounts'>)
2025-06-28 21:54:44,338 - cc_mounts.py[DEBUG]: mounts configuration is []
2025-06-28 21:54:44,338 - util.py[DEBUG]: Reading from /etc/fstab (quiet=False)
2025-06-28 21:54:44,338 - util.py[DEBUG]: Read 217 bytes from /etc/fstab
2025-06-28 21:54:44,338 - cc_mounts.py[DEBUG]: Attempting to determine the real name of ephemeral0
2025-06-28 21:54:44,338 - DataSourceEc2.py[DEBUG]: Unable to convert ephemeral0 to a device
2025-06-28 21:54:44,338 - cc_mounts.py[DEBUG]: changed default device ephemeral0 => None
2025-06-28 21:54:44,338 - cc_mounts.py[DEBUG]: Ignoring nonexistent default named mount ephemeral0
2025-06-28 21:54:44,338 - cc_mounts.py[DEBUG]: Attempting to determine the real name of swap
2025-06-28 21:54:44,338 - DataSourceEc2.py[DEBUG]: Unable to convert swap to a device
2025-06-28 21:54:44,338 - cc_mounts.py[DEBUG]: changed default device swap => None
2025-06-28 21:54:44,338 - cc_mounts.py[DEBUG]: Ignoring nonexistent default named mount swap
2025-06-28 21:54:44,338 - cc_mounts.py[DEBUG]: no need to setup swap
2025-06-28 21:54:44,338 - cc_mounts.py[DEBUG]: No modifications to fstab needed
2025-06-28 21:54:44,338 - handlers.py[DEBUG]: finish: init-network/config-mounts: SUCCESS: config-mounts ran successfully
2025-06-28 21:54:44,338 - modules.py[DEBUG]: Running module set_hostname (<module 'cloudinit.config.cc_set_hostname' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_set_hostname.py'>) with frequency always
2025-06-28 21:54:44,339 - handlers.py[DEBUG]: start: init-network/config-set_hostname: running config-set_hostname with frequency always
2025-06-28 21:54:44,339 - helpers.py[DEBUG]: Running config-set_hostname using lock (<cloudinit.helpers.DummyLock object at 0x7fdac268dd30>)
2025-06-28 21:54:44,339 - util.py[DEBUG]: Reading from /var/lib/cloud/data/set-hostname (quiet=False)
2025-06-28 21:54:44,339 - util.py[DEBUG]: Read 92 bytes from /var/lib/cloud/data/set-hostname
2025-06-28 21:54:44,339 - cc_set_hostname.py[DEBUG]: No hostname changes. Skipping set-hostname
2025-06-28 21:54:44,339 - handlers.py[DEBUG]: finish: init-network/config-set_hostname: SUCCESS: config-set_hostname ran successfully
2025-06-28 21:54:44,339 - modules.py[DEBUG]: Running module update_hostname (<module 'cloudinit.config.cc_update_hostname' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_update_hostname.py'>) with frequency always
2025-06-28 21:54:44,340 - handlers.py[DEBUG]: start: init-network/config-update_hostname: running config-update_hostname with frequency always
2025-06-28 21:54:44,340 - helpers.py[DEBUG]: Running config-update_hostname using lock (<cloudinit.helpers.DummyLock object at 0x7fdac268db50>)
2025-06-28 21:54:44,340 - cc_update_hostname.py[DEBUG]: Updating hostname to ip-172-31-36-197.us-west-2.compute.internal (ip-172-31-36-197)
2025-06-28 21:54:44,340 - subp.py[DEBUG]: Running command ['hostname'] with allowed return codes [0] (shell=False, capture=True)
2025-06-28 21:54:44,342 - util.py[DEBUG]: backgrounded Resizing took 0.012 seconds
2025-06-28 21:54:44,344 - __init__.py[DEBUG]: Attempting to update hostname to ip-172-31-36-197.us-west-2.compute.internal in 1 files
2025-06-28 21:54:44,344 - util.py[DEBUG]: Writing to /var/lib/cloud/data/previous-hostname - wb: [644] 43 bytes
2025-06-28 21:54:44,345 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/data/previous-hostname (recursive=False)
2025-06-28 21:54:44,346 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/data/previous-hostname (recursive=False)
2025-06-28 21:54:44,346 - handlers.py[DEBUG]: finish: init-network/config-update_hostname: SUCCESS: config-update_hostname ran successfully
2025-06-28 21:54:44,346 - modules.py[DEBUG]: Running module update_etc_hosts (<module 'cloudinit.config.cc_update_etc_hosts' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_update_etc_hosts.py'>) with frequency always
2025-06-28 21:54:44,347 - handlers.py[DEBUG]: start: init-network/config-update_etc_hosts: running config-update_etc_hosts with frequency always
2025-06-28 21:54:44,347 - helpers.py[DEBUG]: Running config-update_etc_hosts using lock (<cloudinit.helpers.DummyLock object at 0x7fdac268dd30>)
2025-06-28 21:54:44,347 - cc_update_etc_hosts.py[DEBUG]: Configuration option 'manage_etc_hosts' is not set, not managing /etc/hosts in module update_etc_hosts
2025-06-28 21:54:44,347 - handlers.py[DEBUG]: finish: init-network/config-update_etc_hosts: SUCCESS: config-update_etc_hosts ran successfully
2025-06-28 21:54:44,347 - modules.py[DEBUG]: Running module ca-certs (<module 'cloudinit.config.cc_ca_certs' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_ca_certs.py'>) with frequency once-per-instance
2025-06-28 21:54:44,347 - handlers.py[DEBUG]: start: init-network/config-ca-certs: running config-ca-certs with frequency once-per-instance
2025-06-28 21:54:44,347 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_ca_certs - wb: [644] 25 bytes
2025-06-28 21:54:44,347 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_ca_certs (recursive=False)
2025-06-28 21:54:44,348 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_ca_certs (recursive=False)
2025-06-28 21:54:44,348 - helpers.py[DEBUG]: Running config-ca-certs using lock (<FileLock using file '/var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_ca_certs'>)
2025-06-28 21:54:44,348 - cc_ca_certs.py[DEBUG]: Skipping module named ca-certs, no 'ca_certs' key in configuration
2025-06-28 21:54:44,349 - handlers.py[DEBUG]: finish: init-network/config-ca-certs: SUCCESS: config-ca-certs ran successfully
2025-06-28 21:54:44,349 - modules.py[DEBUG]: Running module rsyslog (<module 'cloudinit.config.cc_rsyslog' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_rsyslog.py'>) with frequency once-per-instance
2025-06-28 21:54:44,349 - handlers.py[DEBUG]: start: init-network/config-rsyslog: running config-rsyslog with frequency once-per-instance
2025-06-28 21:54:44,349 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_rsyslog - wb: [644] 25 bytes
2025-06-28 21:54:44,349 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_rsyslog (recursive=False)
2025-06-28 21:54:44,350 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_rsyslog (recursive=False)
2025-06-28 21:54:44,350 - helpers.py[DEBUG]: Running config-rsyslog using lock (<FileLock using file '/var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_rsyslog'>)
2025-06-28 21:54:44,350 - cc_rsyslog.py[DEBUG]: Skipping module named rsyslog, no 'rsyslog' key in configuration
2025-06-28 21:54:44,350 - handlers.py[DEBUG]: finish: init-network/config-rsyslog: SUCCESS: config-rsyslog ran successfully
2025-06-28 21:54:44,350 - modules.py[DEBUG]: Running module selinux (<module 'cloudinit.config.cc_selinux' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_selinux.py'>) with frequency once-per-instance
2025-06-28 21:54:44,351 - handlers.py[DEBUG]: start: init-network/config-selinux: running config-selinux with frequency once-per-instance
2025-06-28 21:54:44,351 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_selinux - wb: [644] 25 bytes
2025-06-28 21:54:44,351 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_selinux (recursive=False)
2025-06-28 21:54:44,352 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_selinux (recursive=False)
2025-06-28 21:54:44,352 - helpers.py[DEBUG]: Running config-selinux using lock (<FileLock using file '/var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_selinux'>)
2025-06-28 21:54:44,358 - cc_selinux.py[DEBUG]: Current SELinux state is enabled
2025-06-28 21:54:44,358 - cc_selinux.py[DEBUG]: Current SELinux mode is permissive
2025-06-28 21:54:44,358 - cc_selinux.py[DEBUG]: Configured SELinux mode is permissive
2025-06-28 21:54:44,358 - cc_selinux.py[DEBUG]: Current SELinux policy is targeted
2025-06-28 21:54:44,358 - cc_selinux.py[DEBUG]: No SELinux configuration, skipping
2025-06-28 21:54:44,358 - handlers.py[DEBUG]: finish: init-network/config-selinux: SUCCESS: config-selinux ran successfully
2025-06-28 21:54:44,358 - modules.py[DEBUG]: Running module users-groups (<module 'cloudinit.config.cc_users_groups' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_users_groups.py'>) with frequency once-per-instance
2025-06-28 21:54:44,358 - handlers.py[DEBUG]: start: init-network/config-users-groups: running config-users-groups with frequency once-per-instance
2025-06-28 21:54:44,358 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_users_groups - wb: [644] 25 bytes
2025-06-28 21:54:44,359 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_users_groups (recursive=False)
2025-06-28 21:54:44,360 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_users_groups (recursive=False)
2025-06-28 21:54:44,361 - helpers.py[DEBUG]: Running config-users-groups using lock (<FileLock using file '/var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_users_groups'>)
2025-06-28 21:54:44,361 - __init__.py[INFO]: User ec2-user already exists, skipping.
2025-06-28 21:54:44,361 - subp.py[DEBUG]: Running command ['passwd', '-l', 'ec2-user'] with allowed return codes [0] (shell=False, capture=True)
2025-06-28 21:54:44,421 - util.py[DEBUG]: Reading from /etc/sudoers (quiet=False)
2025-06-28 21:54:44,422 - util.py[DEBUG]: Read 4375 bytes from /etc/sudoers
2025-06-28 21:54:44,423 - util.py[DEBUG]: Restoring selinux mode for /etc/sudoers.d (recursive=False)
2025-06-28 21:54:44,424 - util.py[DEBUG]: Writing to /etc/sudoers.d/90-cloud-init-users - ab: [None] 59 bytes
2025-06-28 21:54:44,425 - util.py[DEBUG]: Restoring selinux mode for /etc/sudoers.d/90-cloud-init-users (recursive=False)
2025-06-28 21:54:44,426 - handlers.py[DEBUG]: finish: init-network/config-users-groups: SUCCESS: config-users-groups ran successfully
2025-06-28 21:54:44,426 - modules.py[DEBUG]: Running module ssh (<module 'cloudinit.config.cc_ssh' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_ssh.py'>) with frequency once-per-instance
2025-06-28 21:54:44,426 - handlers.py[DEBUG]: start: init-network/config-ssh: running config-ssh with frequency once-per-instance
2025-06-28 21:54:44,426 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_ssh - wb: [644] 25 bytes
2025-06-28 21:54:44,427 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_ssh (recursive=False)
2025-06-28 21:54:44,428 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_ssh (recursive=False)
2025-06-28 21:54:44,428 - helpers.py[DEBUG]: Running config-ssh using lock (<FileLock using file '/var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_ssh'>)
2025-06-28 21:54:44,429 - subp.py[DEBUG]: Running command ['ssh-keygen', '-t', 'ed25519', '-N', '', '-f', '/etc/ssh/ssh_host_ed25519_key'] with allowed return codes [0] (shell=False, capture=True)
2025-06-28 21:54:44,442 - util.py[DEBUG]: Restoring selinux mode for /etc/ssh (recursive=True)
2025-06-28 21:54:44,449 - subp.py[DEBUG]: Running command ['ssh-keygen', '-t', 'ecdsa', '-N', '', '-f', '/etc/ssh/ssh_host_ecdsa_key'] with allowed return codes [0] (shell=False, capture=True)
2025-06-28 21:54:44,457 - util.py[DEBUG]: Restoring selinux mode for /etc/ssh (recursive=True)
2025-06-28 21:54:44,462 - util.py[DEBUG]: Reading from /etc/ssh/ssh_host_ed25519_key.pub (quiet=False)
2025-06-28 21:54:44,462 - util.py[DEBUG]: Read 130 bytes from /etc/ssh/ssh_host_ed25519_key.pub
2025-06-28 21:54:44,462 - util.py[DEBUG]: Reading from /etc/ssh/ssh_host_ecdsa_key.pub (quiet=False)
2025-06-28 21:54:44,462 - util.py[DEBUG]: Read 210 bytes from /etc/ssh/ssh_host_ecdsa_key.pub
2025-06-28 21:54:44,463 - util.py[DEBUG]: Reading from /etc/ssh/sshd_config (quiet=False)
2025-06-28 21:54:44,463 - util.py[DEBUG]: Read 3854 bytes from /etc/ssh/sshd_config
2025-06-28 21:54:44,464 - util.py[DEBUG]: Changing the ownership of /home/<USER>/.ssh to 1000:1000
2025-06-28 21:54:44,465 - util.py[DEBUG]: Restoring selinux mode for /home/<USER>/.ssh (recursive=False)
2025-06-28 21:54:44,465 - util.py[DEBUG]: Writing to /home/<USER>/.ssh/authorized_keys - wb: [600] 0 bytes
2025-06-28 21:54:44,465 - util.py[DEBUG]: Restoring selinux mode for /home/<USER>/.ssh/authorized_keys (recursive=False)
2025-06-28 21:54:44,466 - util.py[DEBUG]: Restoring selinux mode for /home/<USER>/.ssh/authorized_keys (recursive=False)
2025-06-28 21:54:44,466 - util.py[DEBUG]: Changing the ownership of /home/<USER>/.ssh/authorized_keys to 1000:1000
2025-06-28 21:54:44,466 - util.py[DEBUG]: Reading from /home/<USER>/.ssh/authorized_keys (quiet=False)
2025-06-28 21:54:44,466 - util.py[DEBUG]: Read 0 bytes from /home/<USER>/.ssh/authorized_keys
2025-06-28 21:54:44,466 - util.py[DEBUG]: Writing to /home/<USER>/.ssh/authorized_keys - wb: [600] 88 bytes
2025-06-28 21:54:44,466 - util.py[DEBUG]: Restoring selinux mode for /home/<USER>/.ssh/authorized_keys (recursive=False)
2025-06-28 21:54:44,466 - util.py[DEBUG]: Restoring selinux mode for /home/<USER>/.ssh/authorized_keys (recursive=False)
2025-06-28 21:54:44,467 - util.py[DEBUG]: Restoring selinux mode for /home/<USER>/.ssh (recursive=True)
2025-06-28 21:54:44,468 - util.py[DEBUG]: Reading from /etc/ssh/sshd_config (quiet=False)
2025-06-28 21:54:44,468 - util.py[DEBUG]: Read 3854 bytes from /etc/ssh/sshd_config
2025-06-28 21:54:44,468 - util.py[DEBUG]: Restoring selinux mode for /root/.ssh (recursive=True)
2025-06-28 21:54:44,469 - util.py[DEBUG]: Writing to /root/.ssh/authorized_keys - wb: [600] 0 bytes
2025-06-28 21:54:44,470 - util.py[DEBUG]: Restoring selinux mode for /root/.ssh/authorized_keys (recursive=False)
2025-06-28 21:54:44,470 - util.py[DEBUG]: Restoring selinux mode for /root/.ssh/authorized_keys (recursive=False)
2025-06-28 21:54:44,470 - util.py[DEBUG]: Changing the ownership of /root/.ssh/authorized_keys to 0:0
2025-06-28 21:54:44,470 - util.py[DEBUG]: Reading from /root/.ssh/authorized_keys (quiet=False)
2025-06-28 21:54:44,470 - util.py[DEBUG]: Read 0 bytes from /root/.ssh/authorized_keys
2025-06-28 21:54:44,470 - util.py[DEBUG]: Writing to /root/.ssh/authorized_keys - wb: [600] 254 bytes
2025-06-28 21:54:44,470 - util.py[DEBUG]: Restoring selinux mode for /root/.ssh/authorized_keys (recursive=False)
2025-06-28 21:54:44,471 - util.py[DEBUG]: Restoring selinux mode for /root/.ssh/authorized_keys (recursive=False)
2025-06-28 21:54:44,471 - util.py[DEBUG]: Restoring selinux mode for /root/.ssh (recursive=True)
2025-06-28 21:54:44,472 - handlers.py[DEBUG]: finish: init-network/config-ssh: SUCCESS: config-ssh ran successfully
2025-06-28 21:54:44,472 - main.py[DEBUG]: Ran 17 modules with 0 failures
2025-06-28 21:54:44,472 - atomic_helper.py[DEBUG]: Atomically writing to file /var/lib/cloud/data/status.json (via temporary file /var/lib/cloud/data/tmp10ichyo3) - w: [644] 486 bytes/chars
2025-06-28 21:54:44,473 - util.py[DEBUG]: Reading from /proc/uptime (quiet=False)
2025-06-28 21:54:44,473 - util.py[DEBUG]: Read 10 bytes from /proc/uptime
2025-06-28 21:54:44,473 - util.py[DEBUG]: cloud-init mode 'init' took 0.940 seconds (0.94)
2025-06-28 21:54:44,473 - handlers.py[DEBUG]: finish: init-network: SUCCESS: searching for network datasources
2025-06-28 21:54:45,094 - util.py[DEBUG]: Cloud-init v. 22.2.2 running 'modules:config' at Sat, 28 Jun 2025 21:54:45 +0000. Up 7.66 seconds.
2025-06-28 21:54:45,098 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-28 21:54:45,098 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-28 21:54:45,100 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-28 21:54:45,101 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-28 21:54:45,103 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-28 21:54:45,103 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-28 21:54:45,105 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-28 21:54:45,105 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-28 21:54:45,107 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-28 21:54:45,107 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-28 21:54:45,109 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-28 21:54:45,109 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-28 21:54:45,113 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-28 21:54:45,113 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-28 21:54:45,115 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-28 21:54:45,115 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-28 21:54:45,117 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-28 21:54:45,117 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-28 21:54:45,119 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-28 21:54:45,119 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-28 21:54:45,120 - stages.py[DEBUG]: Using distro class <class 'cloudinit.distros.amazon.Distro'>
2025-06-28 21:54:45,120 - modules.py[INFO]: Skipping modules 'ssh-import-id' because they are not verified on distro 'amazon'.  To run anyway, add them to 'unverified_modules' in config.
2025-06-28 21:54:45,121 - modules.py[DEBUG]: Running module keyboard (<module 'cloudinit.config.cc_keyboard' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_keyboard.py'>) with frequency once-per-instance
2025-06-28 21:54:45,121 - handlers.py[DEBUG]: start: modules-config/config-keyboard: running config-keyboard with frequency once-per-instance
2025-06-28 21:54:45,121 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_keyboard - wb: [644] 25 bytes
2025-06-28 21:54:45,124 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_keyboard (recursive=False)
2025-06-28 21:54:45,126 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_keyboard (recursive=False)
2025-06-28 21:54:45,127 - helpers.py[DEBUG]: Running config-keyboard using lock (<FileLock using file '/var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_keyboard'>)
2025-06-28 21:54:45,127 - cc_keyboard.py[DEBUG]: Skipping module named keyboard, no 'keyboard' section found
2025-06-28 21:54:45,127 - handlers.py[DEBUG]: finish: modules-config/config-keyboard: SUCCESS: config-keyboard ran successfully
2025-06-28 21:54:45,127 - modules.py[DEBUG]: Running module locale (<module 'cloudinit.config.cc_locale' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_locale.py'>) with frequency once-per-instance
2025-06-28 21:54:45,127 - handlers.py[DEBUG]: start: modules-config/config-locale: running config-locale with frequency once-per-instance
2025-06-28 21:54:45,127 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_locale - wb: [644] 25 bytes
2025-06-28 21:54:45,128 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_locale (recursive=False)
2025-06-28 21:54:45,128 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_locale (recursive=False)
2025-06-28 21:54:45,129 - helpers.py[DEBUG]: Running config-locale using lock (<FileLock using file '/var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_locale'>)
2025-06-28 21:54:45,129 - util.py[DEBUG]: Reading from /etc/locale.conf (quiet=False)
2025-06-28 21:54:45,129 - util.py[DEBUG]: Read 17 bytes from /etc/locale.conf
2025-06-28 21:54:45,129 - cc_locale.py[DEBUG]: Setting locale to en_US.UTF-8
2025-06-28 21:54:45,129 - util.py[DEBUG]: Reading from /etc/locale.conf (quiet=False)
2025-06-28 21:54:45,129 - util.py[DEBUG]: Read 17 bytes from /etc/locale.conf
2025-06-28 21:54:45,129 - util.py[DEBUG]: Writing to /etc/locale.conf - wb: [644] 17 bytes
2025-06-28 21:54:45,131 - util.py[DEBUG]: Restoring selinux mode for /etc/locale.conf (recursive=False)
2025-06-28 21:54:45,133 - util.py[DEBUG]: Restoring selinux mode for /etc/locale.conf (recursive=False)
2025-06-28 21:54:45,133 - handlers.py[DEBUG]: finish: modules-config/config-locale: SUCCESS: config-locale ran successfully
2025-06-28 21:54:45,133 - modules.py[DEBUG]: Running module set-passwords (<module 'cloudinit.config.cc_set_passwords' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_set_passwords.py'>) with frequency once-per-instance
2025-06-28 21:54:45,134 - handlers.py[DEBUG]: start: modules-config/config-set-passwords: running config-set-passwords with frequency once-per-instance
2025-06-28 21:54:45,134 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_set_passwords - wb: [644] 24 bytes
2025-06-28 21:54:45,134 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_set_passwords (recursive=False)
2025-06-28 21:54:45,135 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_set_passwords (recursive=False)
2025-06-28 21:54:45,135 - helpers.py[DEBUG]: Running config-set-passwords using lock (<FileLock using file '/var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_set_passwords'>)
2025-06-28 21:54:45,136 - subp.py[DEBUG]: Running command ['systemctl', 'status', 'sshd'] with allowed return codes [0] (shell=False, capture=True)
2025-06-28 21:54:45,156 - util.py[DEBUG]: Reading from /etc/ssh/sshd_config (quiet=False)
2025-06-28 21:54:45,156 - util.py[DEBUG]: Read 3854 bytes from /etc/ssh/sshd_config
2025-06-28 21:54:45,157 - ssh_util.py[DEBUG]: line 65: option PasswordAuthentication already set to no
2025-06-28 21:54:45,157 - cc_set_passwords.py[DEBUG]: No need to restart SSH service, PasswordAuthentication not updated.
2025-06-28 21:54:45,157 - handlers.py[DEBUG]: finish: modules-config/config-set-passwords: SUCCESS: config-set-passwords ran successfully
2025-06-28 21:54:45,157 - modules.py[DEBUG]: Running module yum-variables (<module 'cloudinit.config.cc_yum_variables' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_yum_variables.py'>) with frequency once-per-instance
2025-06-28 21:54:45,157 - handlers.py[DEBUG]: start: modules-config/config-yum-variables: running config-yum-variables with frequency once-per-instance
2025-06-28 21:54:45,158 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_yum_variables - wb: [644] 25 bytes
2025-06-28 21:54:45,158 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_yum_variables (recursive=False)
2025-06-28 21:54:45,159 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_yum_variables (recursive=False)
2025-06-28 21:54:45,160 - helpers.py[DEBUG]: Running config-yum-variables using lock (<FileLock using file '/var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_yum_variables'>)
2025-06-28 21:54:45,160 - cc_yum_variables.py[INFO]: handling yum-variables
2025-06-28 21:54:45,160 - cc_yum_variables.py[INFO]: cfg = {'cloud_final_modules': [['scripts-user', 'always']], 'repo_releasever': 2023.7, 'repo_upgrade': 'none', 'power_state': {'delay': 'now', 'mode': 'reboot', 'message': 'Rebooting machine to apply SELinux kernel commandline setting', 'condition': 'test -f /run/cloud-init-selinux-reboot'}, 'write_metadata': [{'path': '/etc/dnf/vars/awsregion', 'data': [{'identity': 'region'}, 'default']}, {'path': '/etc/dnf/vars/awsdomain', 'data': [{'metadata': 'services/domain'}, 'amazonaws.com']}], '_log': ['[loggers]\nkeys=root,cloudinit\n\n[handlers]\nkeys=consoleHandler,cloudLogHandler\n\n[formatters]\nkeys=simpleFormatter,arg0Formatter\n\n[logger_root]\nlevel=DEBUG\nhandlers=consoleHandler,cloudLogHandler\n\n[logger_cloudinit]\nlevel=DEBUG\nqualname=cloudinit\nhandlers=\npropagate=1\n\n[handler_consoleHandler]\nclass=StreamHandler\nlevel=WARNING\nformatter=arg0Formatter\nargs=(sys.stderr,)\n\n[formatter_arg0Formatter]\nformat=%(asctime)s - %(filename)s[%(levelname)s]: %(message)s\n\n[formatter_simpleFormatter]\nformat=[CLOUDINIT] %(filename)s[%(levelname)s]: %(message)s\n', "[handler_cloudLogHandler]\nclass=FileHandler\nlevel=DEBUG\nformatter=arg0Formatter\nargs=('/var/log/cloud-init.log', 'a', 'UTF-8')\n", '[handler_cloudLogHandler]\nclass=handlers.SysLogHandler\nlevel=DEBUG\nformatter=simpleFormatter\nargs=("/dev/log", handlers.SysLogHandler.LOG_USER)\n'], 'log_cfgs': [['[loggers]\nkeys=root,cloudinit\n\n[handlers]\nkeys=consoleHandler,cloudLogHandler\n\n[formatters]\nkeys=simpleFormatter,arg0Formatter\n\n[logger_root]\nlevel=DEBUG\nhandlers=consoleHandler,cloudLogHandler\n\n[logger_cloudinit]\nlevel=DEBUG\nqualname=cloudinit\nhandlers=\npropagate=1\n\n[handler_consoleHandler]\nclass=StreamHandler\nlevel=WARNING\nformatter=arg0Formatter\nargs=(sys.stderr,)\n\n[formatter_arg0Formatter]\nformat=%(asctime)s - %(filename)s[%(levelname)s]: %(message)s\n\n[formatter_simpleFormatter]\nformat=[CLOUDINIT] %(filename)s[%(levelname)s]: %(message)s\n', "[handler_cloudLogHandler]\nclass=FileHandler\nlevel=DEBUG\nformatter=arg0Formatter\nargs=('/var/log/cloud-init.log', 'a', 'UTF-8')\n"]], 'output': {'all': '| tee -a /var/log/cloud-init-output.log'}, 'network': {'config': 'disabled'}, 'datasource_list': ['Ec2', 'None'], 'datasource': {'Ec2': {'metadata_urls': ['http://***************:80', 'http://[fd00:ec2::254]:80'], 'apply_full_imds_network_config': False}}, 'ssh_genkeytypes': ['ed25519', 'ecdsa'], 'users': ['default'], 'disable_root': True, 'mount_default_fields': [None, None, 'auto', 'defaults,nofail', '0', '2'], 'resize_rootfs': 'noblock', 'resize_rootfs_tmp': '/dev', 'ssh_pwauth': False, 'preserve_hostname': False, 'cloud_init_modules': ['migrator', 'seed_random', 'bootcmd', 'write-files', 'write-metadata', 'growpart', 'resizefs', 'disk_setup', 'mounts', 'set_hostname', 'update_hostname', 'update_etc_hosts', 'ca-certs', 'rsyslog', 'selinux', 'users-groups', 'ssh'], 'cloud_config_modules': ['ssh-import-id', 'keyboard', 'locale', 'set-passwords', 'yum-variables', 'yum-add-repo', 'ntp', 'timezone', 'disable-ec2-metadata', 'runcmd'], 'def_log_file': '/var/log/cloud-init.log', 'syslog_fix_perms': ['syslog:adm', 'root:adm', 'root:wheel', 'root:root'], 'vendor_data': {'enabled': True, 'prefix': []}, 'vendor_data2': {'enabled': True, 'prefix': []}}
2025-06-28 21:54:45,160 - cc_yum_variables.py[INFO]: No yum vars to delete
2025-06-28 21:54:45,160 - cc_yum_variables.py[INFO]: No yum vars to set
2025-06-28 21:54:45,160 - handlers.py[DEBUG]: finish: modules-config/config-yum-variables: SUCCESS: config-yum-variables ran successfully
2025-06-28 21:54:45,160 - modules.py[DEBUG]: Running module yum-add-repo (<module 'cloudinit.config.cc_yum_add_repo' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_yum_add_repo.py'>) with frequency once-per-instance
2025-06-28 21:54:45,160 - handlers.py[DEBUG]: start: modules-config/config-yum-add-repo: running config-yum-add-repo with frequency once-per-instance
2025-06-28 21:54:45,160 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_yum_add_repo - wb: [644] 24 bytes
2025-06-28 21:54:45,161 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_yum_add_repo (recursive=False)
2025-06-28 21:54:45,162 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_yum_add_repo (recursive=False)
2025-06-28 21:54:45,162 - helpers.py[DEBUG]: Running config-yum-add-repo using lock (<FileLock using file '/var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_yum_add_repo'>)
2025-06-28 21:54:45,162 - cc_yum_add_repo.py[DEBUG]: Skipping module named yum-add-repo, no 'yum_repos' configuration found
2025-06-28 21:54:45,162 - handlers.py[DEBUG]: finish: modules-config/config-yum-add-repo: SUCCESS: config-yum-add-repo ran successfully
2025-06-28 21:54:45,162 - modules.py[DEBUG]: Running module ntp (<module 'cloudinit.config.cc_ntp' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_ntp.py'>) with frequency once-per-instance
2025-06-28 21:54:45,162 - handlers.py[DEBUG]: start: modules-config/config-ntp: running config-ntp with frequency once-per-instance
2025-06-28 21:54:45,163 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_ntp - wb: [644] 25 bytes
2025-06-28 21:54:45,163 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_ntp (recursive=False)
2025-06-28 21:54:45,164 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_ntp (recursive=False)
2025-06-28 21:54:45,164 - helpers.py[DEBUG]: Running config-ntp using lock (<FileLock using file '/var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_ntp'>)
2025-06-28 21:54:45,164 - cc_ntp.py[DEBUG]: Skipping module named ntp, not present or disabled by cfg
2025-06-28 21:54:45,164 - handlers.py[DEBUG]: finish: modules-config/config-ntp: SUCCESS: config-ntp ran successfully
2025-06-28 21:54:45,164 - modules.py[DEBUG]: Running module timezone (<module 'cloudinit.config.cc_timezone' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_timezone.py'>) with frequency once-per-instance
2025-06-28 21:54:45,164 - handlers.py[DEBUG]: start: modules-config/config-timezone: running config-timezone with frequency once-per-instance
2025-06-28 21:54:45,164 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_timezone - wb: [644] 25 bytes
2025-06-28 21:54:45,165 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_timezone (recursive=False)
2025-06-28 21:54:45,165 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_timezone (recursive=False)
2025-06-28 21:54:45,166 - helpers.py[DEBUG]: Running config-timezone using lock (<FileLock using file '/var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_timezone'>)
2025-06-28 21:54:45,166 - cc_timezone.py[DEBUG]: Skipping module named timezone, no 'timezone' specified
2025-06-28 21:54:45,166 - handlers.py[DEBUG]: finish: modules-config/config-timezone: SUCCESS: config-timezone ran successfully
2025-06-28 21:54:45,166 - modules.py[DEBUG]: Running module disable-ec2-metadata (<module 'cloudinit.config.cc_disable_ec2_metadata' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_disable_ec2_metadata.py'>) with frequency always
2025-06-28 21:54:45,166 - handlers.py[DEBUG]: start: modules-config/config-disable-ec2-metadata: running config-disable-ec2-metadata with frequency always
2025-06-28 21:54:45,166 - helpers.py[DEBUG]: Running config-disable-ec2-metadata using lock (<cloudinit.helpers.DummyLock object at 0x7f4550b3c220>)
2025-06-28 21:54:45,166 - cc_disable_ec2_metadata.py[DEBUG]: Skipping module named disable-ec2-metadata, disabling the ec2 route not enabled
2025-06-28 21:54:45,166 - handlers.py[DEBUG]: finish: modules-config/config-disable-ec2-metadata: SUCCESS: config-disable-ec2-metadata ran successfully
2025-06-28 21:54:45,166 - modules.py[DEBUG]: Running module runcmd (<module 'cloudinit.config.cc_runcmd' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_runcmd.py'>) with frequency once-per-instance
2025-06-28 21:54:45,166 - handlers.py[DEBUG]: start: modules-config/config-runcmd: running config-runcmd with frequency once-per-instance
2025-06-28 21:54:45,166 - util.py[DEBUG]: Writing to /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_runcmd - wb: [644] 24 bytes
2025-06-28 21:54:45,167 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_runcmd (recursive=False)
2025-06-28 21:54:45,167 - util.py[DEBUG]: Restoring selinux mode for /var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_runcmd (recursive=False)
2025-06-28 21:54:45,168 - helpers.py[DEBUG]: Running config-runcmd using lock (<FileLock using file '/var/lib/cloud/instances/i-08b2210ccd2afe431/sem/config_runcmd'>)
2025-06-28 21:54:45,168 - cc_runcmd.py[DEBUG]: Skipping module named runcmd, no 'runcmd' key in configuration
2025-06-28 21:54:45,168 - handlers.py[DEBUG]: finish: modules-config/config-runcmd: SUCCESS: config-runcmd ran successfully
2025-06-28 21:54:45,168 - main.py[DEBUG]: Ran 9 modules with 0 failures
2025-06-28 21:54:45,168 - atomic_helper.py[DEBUG]: Atomically writing to file /var/lib/cloud/data/status.json (via temporary file /var/lib/cloud/data/tmpg560i0aj) - w: [644] 513 bytes/chars
2025-06-28 21:54:45,169 - util.py[DEBUG]: Reading from /proc/uptime (quiet=False)
2025-06-28 21:54:45,169 - util.py[DEBUG]: Read 10 bytes from /proc/uptime
2025-06-28 21:54:45,169 - util.py[DEBUG]: cloud-init mode 'modules' took 0.154 seconds (0.15)
2025-06-28 21:54:45,169 - handlers.py[DEBUG]: finish: modules-config: SUCCESS: running modules for config
2025-06-28 21:54:45,564 - util.py[DEBUG]: Cloud-init v. 22.2.2 running 'modules:final' at Sat, 28 Jun 2025 21:54:45 +0000. Up 8.13 seconds.
2025-06-28 21:54:45,567 - util.py[DEBUG]: Reading from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json (quiet=False)
2025-06-28 21:54:45,567 - util.py[DEBUG]: Read 102081 bytes from /usr/lib/python3.9/site-packages/cloudinit/config/schemas/schema-cloud-config-v1.json
2025-06-28 21:54:45,569 - stages.py[DEBUG]: Using distro class <class 'cloudinit.distros.amazon.Distro'>
2025-06-28 21:54:45,569 - modules.py[DEBUG]: Running module scripts-user (<module 'cloudinit.config.cc_scripts_user' from '/usr/lib/python3.9/site-packages/cloudinit/config/cc_scripts_user.py'>) with frequency always
2025-06-28 21:54:45,570 - handlers.py[DEBUG]: start: modules-final/config-scripts-user: running config-scripts-user with frequency always
2025-06-28 21:54:45,570 - helpers.py[DEBUG]: Running config-scripts-user using lock (<cloudinit.helpers.DummyLock object at 0x7fab2a3fdf70>)
2025-06-28 21:54:45,570 - subp.py[DEBUG]: Running command ['/var/lib/cloud/instance/scripts/user-data.txt'] with allowed return codes [0] (shell=False, capture=False)
2025-06-28 21:54:49,772 - handlers.py[DEBUG]: finish: modules-final/config-scripts-user: SUCCESS: config-scripts-user ran successfully
2025-06-28 21:54:49,772 - main.py[DEBUG]: Ran 1 modules with 0 failures
2025-06-28 21:54:49,773 - atomic_helper.py[DEBUG]: Atomically writing to file /var/lib/cloud/data/status.json (via temporary file /var/lib/cloud/data/tmptwjtgpqa) - w: [644] 541 bytes/chars
2025-06-28 21:54:49,773 - atomic_helper.py[DEBUG]: Atomically writing to file /var/lib/cloud/data/result.json (via temporary file /var/lib/cloud/data/tmpibyo2e7m) - w: [644] 64 bytes/chars
2025-06-28 21:54:49,773 - util.py[DEBUG]: Creating symbolic link from '/run/cloud-init/result.json' => '../../var/lib/cloud/data/result.json'
2025-06-28 21:54:49,773 - util.py[DEBUG]: Reading from /proc/uptime (quiet=False)
2025-06-28 21:54:49,774 - util.py[DEBUG]: Read 12 bytes from /proc/uptime
2025-06-28 21:54:49,774 - util.py[DEBUG]: cloud-init mode 'modules' took 4.278 seconds (4.28)
2025-06-28 21:54:49,774 - handlers.py[DEBUG]: finish: modules-final: SUCCESS: running modules for final
