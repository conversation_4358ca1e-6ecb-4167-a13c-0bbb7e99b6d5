Jun 29 00:01:01 ip-172-31-36-197 CROND[6837]: (root) C<PERSON> (run-parts /etc/cron.hourly)
Jun 29 00:01:01 ip-172-31-36-197 run-parts[6837]: (/etc/cron.hourly) starting 0anacron
Jun 29 00:01:01 ip-172-31-36-197 anacron[6848]: Anacron started on 2025-06-29
Jun 29 00:01:01 ip-172-31-36-197 anacron[6848]: Normal exit (0 jobs run)
Jun 29 00:01:01 ip-172-31-36-197 run-parts[6837]: (/etc/cron.hourly) finished 0anacron
Jun 29 00:01:01 ip-172-31-36-197 run-parts[6837]: (/etc/cron.hourly) starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 29 00:01:01 ip-172-31-36-197 run-parts[6837]: (/etc/cron.hourly) finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 29 00:01:01 ip-172-31-36-197 run-parts[6837]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 29 00:01:01 ip-172-31-36-197 run-parts[6837]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 29 00:01:01 ip-172-31-36-197 run-parts[6837]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 29 00:01:02 ip-172-31-36-197 run-parts[6837]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 29 00:01:02 ip-172-31-36-197 run-parts[6837]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 29 00:01:02 ip-172-31-36-197 run-parts[6837]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 29 00:01:02 ip-172-31-36-197 run-parts[6837]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 29 00:01:02 ip-172-31-36-197 run-parts[6837]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 29 00:01:02 ip-172-31-36-197 run-parts[6837]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 29 00:01:02 ip-172-31-36-197 run-parts[6837]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 29 00:01:02 ip-172-31-36-197 run-parts[6837]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 29 00:01:02 ip-172-31-36-197 run-parts[6837]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 29 00:01:02 ip-172-31-36-197 CROND[6836]: (root) CMDEND (run-parts /etc/cron.hourly)
Jun 29 00:10:01 ip-172-31-36-197 CROND[7257]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 00:10:01 ip-172-31-36-197 CROND[7256]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 00:30:01 ip-172-31-36-197 CROND[7964]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 00:30:01 ip-172-31-36-197 CROND[7963]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 00:50:01 ip-172-31-36-197 CROND[8667]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 00:50:01 ip-172-31-36-197 CROND[8666]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 01:01:01 ip-172-31-36-197 CROND[9091]: (root) CMD (run-parts /etc/cron.hourly)
Jun 29 01:01:01 ip-172-31-36-197 run-parts[9091]: (/etc/cron.hourly) starting 0anacron
Jun 29 01:01:01 ip-172-31-36-197 anacron[9102]: Anacron started on 2025-06-29
Jun 29 01:01:01 ip-172-31-36-197 anacron[9102]: Normal exit (0 jobs run)
Jun 29 01:01:01 ip-172-31-36-197 run-parts[9091]: (/etc/cron.hourly) finished 0anacron
Jun 29 01:01:01 ip-172-31-36-197 run-parts[9091]: (/etc/cron.hourly) starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 29 01:01:01 ip-172-31-36-197 run-parts[9091]: (/etc/cron.hourly) finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 29 01:01:01 ip-172-31-36-197 run-parts[9091]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 29 01:01:01 ip-172-31-36-197 run-parts[9091]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 29 01:01:01 ip-172-31-36-197 run-parts[9091]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 29 01:01:01 ip-172-31-36-197 run-parts[9091]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 29 01:01:01 ip-172-31-36-197 run-parts[9091]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 29 01:01:01 ip-172-31-36-197 run-parts[9091]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 29 01:01:01 ip-172-31-36-197 run-parts[9091]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 29 01:01:01 ip-172-31-36-197 run-parts[9091]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 29 01:01:01 ip-172-31-36-197 run-parts[9091]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 29 01:01:01 ip-172-31-36-197 run-parts[9091]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 29 01:01:01 ip-172-31-36-197 run-parts[9091]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 29 01:01:01 ip-172-31-36-197 run-parts[9091]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 29 01:01:01 ip-172-31-36-197 CROND[9090]: (root) CMDEND (run-parts /etc/cron.hourly)
Jun 29 01:10:01 ip-172-31-36-197 CROND[9443]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 01:10:01 ip-172-31-36-197 CROND[9442]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 01:30:01 ip-172-31-36-197 CROND[10148]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 01:30:01 ip-172-31-36-197 CROND[10147]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 01:50:01 ip-172-31-36-197 CROND[10850]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 01:50:01 ip-172-31-36-197 CROND[10849]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 02:01:01 ip-172-31-36-197 CROND[11272]: (root) CMD (run-parts /etc/cron.hourly)
Jun 29 02:01:01 ip-172-31-36-197 run-parts[11272]: (/etc/cron.hourly) starting 0anacron
Jun 29 02:01:01 ip-172-31-36-197 anacron[11283]: Anacron started on 2025-06-29
Jun 29 02:01:01 ip-172-31-36-197 anacron[11283]: Normal exit (0 jobs run)
Jun 29 02:01:01 ip-172-31-36-197 run-parts[11272]: (/etc/cron.hourly) finished 0anacron
Jun 29 02:01:01 ip-172-31-36-197 run-parts[11272]: (/etc/cron.hourly) starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 29 02:01:01 ip-172-31-36-197 run-parts[11272]: (/etc/cron.hourly) finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 29 02:01:01 ip-172-31-36-197 run-parts[11272]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 29 02:01:01 ip-172-31-36-197 run-parts[11272]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 29 02:01:01 ip-172-31-36-197 run-parts[11272]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 29 02:01:01 ip-172-31-36-197 run-parts[11272]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 29 02:01:01 ip-172-31-36-197 run-parts[11272]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 29 02:01:01 ip-172-31-36-197 run-parts[11272]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 29 02:01:01 ip-172-31-36-197 run-parts[11272]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 29 02:01:01 ip-172-31-36-197 run-parts[11272]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 29 02:01:01 ip-172-31-36-197 run-parts[11272]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 29 02:01:01 ip-172-31-36-197 run-parts[11272]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 29 02:01:01 ip-172-31-36-197 run-parts[11272]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 29 02:01:01 ip-172-31-36-197 run-parts[11272]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 29 02:01:01 ip-172-31-36-197 CROND[11271]: (root) CMDEND (run-parts /etc/cron.hourly)
Jun 29 02:10:01 ip-172-31-36-197 CROND[11623]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 02:10:01 ip-172-31-36-197 CROND[11622]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 02:30:01 ip-172-31-36-197 CROND[12330]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 02:30:01 ip-172-31-36-197 CROND[12329]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 02:50:01 ip-172-31-36-197 CROND[13032]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 02:50:01 ip-172-31-36-197 CROND[13031]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 03:01:01 ip-172-31-36-197 CROND[13455]: (root) CMD (run-parts /etc/cron.hourly)
Jun 29 03:01:01 ip-172-31-36-197 run-parts[13455]: (/etc/cron.hourly) starting 0anacron
Jun 29 03:01:01 ip-172-31-36-197 anacron[13466]: Anacron started on 2025-06-29
Jun 29 03:01:01 ip-172-31-36-197 anacron[13466]: Will run job `cron.daily' in 47 min.
Jun 29 03:01:01 ip-172-31-36-197 anacron[13466]: Jobs will be executed sequentially
Jun 29 03:01:01 ip-172-31-36-197 run-parts[13455]: (/etc/cron.hourly) finished 0anacron
Jun 29 03:01:01 ip-172-31-36-197 run-parts[13455]: (/etc/cron.hourly) starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 29 03:01:01 ip-172-31-36-197 run-parts[13455]: (/etc/cron.hourly) finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 29 03:01:01 ip-172-31-36-197 run-parts[13455]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 29 03:01:01 ip-172-31-36-197 run-parts[13455]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 29 03:01:01 ip-172-31-36-197 run-parts[13455]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 29 03:01:01 ip-172-31-36-197 run-parts[13455]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 29 03:01:01 ip-172-31-36-197 run-parts[13455]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 29 03:01:01 ip-172-31-36-197 run-parts[13455]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 29 03:01:01 ip-172-31-36-197 run-parts[13455]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 29 03:01:01 ip-172-31-36-197 run-parts[13455]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 29 03:01:01 ip-172-31-36-197 run-parts[13455]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 29 03:01:01 ip-172-31-36-197 run-parts[13455]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 29 03:01:01 ip-172-31-36-197 run-parts[13455]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 29 03:01:01 ip-172-31-36-197 run-parts[13455]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 29 03:01:01 ip-172-31-36-197 CROND[13454]: (root) CMDEND (run-parts /etc/cron.hourly)
Jun 29 03:10:01 ip-172-31-36-197 CROND[13806]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 03:10:01 ip-172-31-36-197 CROND[13805]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 03:30:01 ip-172-31-36-197 CROND[14510]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 03:30:01 ip-172-31-36-197 CROND[14509]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 03:48:01 ip-172-31-36-197 anacron[13466]: Job `cron.daily' started
Jun 29 03:48:01 ip-172-31-36-197 anacron[13466]: Job `cron.daily' terminated
Jun 29 03:48:01 ip-172-31-36-197 anacron[13466]: Normal exit (1 job run)
Jun 29 03:50:01 ip-172-31-36-197 CROND[15278]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 03:50:01 ip-172-31-36-197 CROND[15277]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 04:01:01 ip-172-31-36-197 CROND[15702]: (root) CMD (run-parts /etc/cron.hourly)
Jun 29 04:01:01 ip-172-31-36-197 run-parts[15702]: (/etc/cron.hourly) starting 0anacron
Jun 29 04:01:01 ip-172-31-36-197 run-parts[15702]: (/etc/cron.hourly) finished 0anacron
Jun 29 04:01:01 ip-172-31-36-197 run-parts[15702]: (/etc/cron.hourly) starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 29 04:01:01 ip-172-31-36-197 run-parts[15702]: (/etc/cron.hourly) finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 29 04:01:01 ip-172-31-36-197 run-parts[15702]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 29 04:01:01 ip-172-31-36-197 run-parts[15702]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 29 04:01:01 ip-172-31-36-197 run-parts[15702]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 29 04:01:01 ip-172-31-36-197 run-parts[15702]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 29 04:01:01 ip-172-31-36-197 run-parts[15702]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 29 04:01:01 ip-172-31-36-197 run-parts[15702]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 29 04:01:01 ip-172-31-36-197 run-parts[15702]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 29 04:01:01 ip-172-31-36-197 run-parts[15702]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 29 04:01:01 ip-172-31-36-197 run-parts[15702]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 29 04:01:01 ip-172-31-36-197 run-parts[15702]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 29 04:01:01 ip-172-31-36-197 run-parts[15702]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 29 04:01:01 ip-172-31-36-197 run-parts[15702]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 29 04:01:01 ip-172-31-36-197 CROND[15701]: (root) CMDEND (run-parts /etc/cron.hourly)
Jun 29 04:10:01 ip-172-31-36-197 CROND[16051]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 04:10:01 ip-172-31-36-197 CROND[16050]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 04:30:01 ip-172-31-36-197 CROND[16757]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 04:30:01 ip-172-31-36-197 CROND[16756]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 04:50:01 ip-172-31-36-197 CROND[17460]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 04:50:02 ip-172-31-36-197 CROND[17459]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 05:01:01 ip-172-31-36-197 CROND[17880]: (root) CMD (run-parts /etc/cron.hourly)
Jun 29 05:01:01 ip-172-31-36-197 run-parts[17880]: (/etc/cron.hourly) starting 0anacron
Jun 29 05:01:01 ip-172-31-36-197 run-parts[17880]: (/etc/cron.hourly) finished 0anacron
Jun 29 05:01:01 ip-172-31-36-197 run-parts[17880]: (/etc/cron.hourly) starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 29 05:01:01 ip-172-31-36-197 run-parts[17880]: (/etc/cron.hourly) finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 29 05:01:01 ip-172-31-36-197 run-parts[17880]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 29 05:01:01 ip-172-31-36-197 run-parts[17880]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 29 05:01:01 ip-172-31-36-197 run-parts[17880]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 29 05:01:01 ip-172-31-36-197 run-parts[17880]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 29 05:01:01 ip-172-31-36-197 run-parts[17880]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 29 05:01:01 ip-172-31-36-197 run-parts[17880]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 29 05:01:01 ip-172-31-36-197 run-parts[17880]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 29 05:01:01 ip-172-31-36-197 run-parts[17880]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 29 05:01:01 ip-172-31-36-197 run-parts[17880]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 29 05:01:01 ip-172-31-36-197 run-parts[17880]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 29 05:01:01 ip-172-31-36-197 run-parts[17880]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 29 05:01:01 ip-172-31-36-197 run-parts[17880]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 29 05:01:01 ip-172-31-36-197 CROND[17879]: (root) CMDEND (run-parts /etc/cron.hourly)
Jun 29 05:10:01 ip-172-31-36-197 CROND[18229]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 05:10:01 ip-172-31-36-197 CROND[18228]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 05:30:01 ip-172-31-36-197 CROND[18932]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 05:30:01 ip-172-31-36-197 CROND[18931]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 05:50:01 ip-172-31-36-197 CROND[19635]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 05:50:01 ip-172-31-36-197 CROND[19634]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 06:01:01 ip-172-31-36-197 CROND[20055]: (root) CMD (run-parts /etc/cron.hourly)
Jun 29 06:01:01 ip-172-31-36-197 run-parts[20055]: (/etc/cron.hourly) starting 0anacron
Jun 29 06:01:01 ip-172-31-36-197 run-parts[20055]: (/etc/cron.hourly) finished 0anacron
Jun 29 06:01:01 ip-172-31-36-197 run-parts[20055]: (/etc/cron.hourly) starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 29 06:01:01 ip-172-31-36-197 run-parts[20055]: (/etc/cron.hourly) finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 29 06:01:01 ip-172-31-36-197 run-parts[20055]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 29 06:01:01 ip-172-31-36-197 run-parts[20055]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 29 06:01:01 ip-172-31-36-197 run-parts[20055]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 29 06:01:01 ip-172-31-36-197 run-parts[20055]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 29 06:01:01 ip-172-31-36-197 run-parts[20055]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 29 06:01:01 ip-172-31-36-197 run-parts[20055]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 29 06:01:01 ip-172-31-36-197 run-parts[20055]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 29 06:01:01 ip-172-31-36-197 run-parts[20055]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 29 06:01:01 ip-172-31-36-197 run-parts[20055]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 29 06:01:01 ip-172-31-36-197 run-parts[20055]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 29 06:01:01 ip-172-31-36-197 run-parts[20055]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 29 06:01:01 ip-172-31-36-197 run-parts[20055]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 29 06:01:01 ip-172-31-36-197 CROND[20054]: (root) CMDEND (run-parts /etc/cron.hourly)
Jun 29 06:10:01 ip-172-31-36-197 CROND[20467]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 06:10:01 ip-172-31-36-197 CROND[20466]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 06:30:01 ip-172-31-36-197 CROND[21233]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 06:30:01 ip-172-31-36-197 CROND[21232]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 06:50:01 ip-172-31-36-197 CROND[21938]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 06:50:01 ip-172-31-36-197 CROND[21937]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 07:01:01 ip-172-31-36-197 CROND[22358]: (root) CMD (run-parts /etc/cron.hourly)
Jun 29 07:01:01 ip-172-31-36-197 run-parts[22358]: (/etc/cron.hourly) starting 0anacron
Jun 29 07:01:01 ip-172-31-36-197 run-parts[22358]: (/etc/cron.hourly) finished 0anacron
Jun 29 07:01:01 ip-172-31-36-197 run-parts[22358]: (/etc/cron.hourly) starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 29 07:01:01 ip-172-31-36-197 run-parts[22358]: (/etc/cron.hourly) finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 29 07:01:01 ip-172-31-36-197 run-parts[22358]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 29 07:01:01 ip-172-31-36-197 run-parts[22358]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 29 07:01:01 ip-172-31-36-197 run-parts[22358]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 29 07:01:01 ip-172-31-36-197 run-parts[22358]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 29 07:01:01 ip-172-31-36-197 run-parts[22358]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 29 07:01:01 ip-172-31-36-197 run-parts[22358]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 29 07:01:01 ip-172-31-36-197 run-parts[22358]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 29 07:01:01 ip-172-31-36-197 run-parts[22358]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 29 07:01:01 ip-172-31-36-197 run-parts[22358]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 29 07:01:01 ip-172-31-36-197 run-parts[22358]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 29 07:01:01 ip-172-31-36-197 run-parts[22358]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 29 07:01:01 ip-172-31-36-197 run-parts[22358]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 29 07:01:01 ip-172-31-36-197 CROND[22357]: (root) CMDEND (run-parts /etc/cron.hourly)
Jun 29 07:10:01 ip-172-31-36-197 CROND[22707]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 07:10:01 ip-172-31-36-197 CROND[22706]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 07:30:01 ip-172-31-36-197 CROND[23411]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 07:30:01 ip-172-31-36-197 CROND[23410]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 07:50:01 ip-172-31-36-197 CROND[24119]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 07:50:01 ip-172-31-36-197 CROND[24118]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 08:01:01 ip-172-31-36-197 CROND[24539]: (root) CMD (run-parts /etc/cron.hourly)
Jun 29 08:01:01 ip-172-31-36-197 run-parts[24539]: (/etc/cron.hourly) starting 0anacron
Jun 29 08:01:01 ip-172-31-36-197 run-parts[24539]: (/etc/cron.hourly) finished 0anacron
Jun 29 08:01:01 ip-172-31-36-197 run-parts[24539]: (/etc/cron.hourly) starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 29 08:01:01 ip-172-31-36-197 run-parts[24539]: (/etc/cron.hourly) finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 29 08:01:01 ip-172-31-36-197 run-parts[24539]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 29 08:01:01 ip-172-31-36-197 run-parts[24539]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 29 08:01:01 ip-172-31-36-197 run-parts[24539]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 29 08:01:01 ip-172-31-36-197 run-parts[24539]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 29 08:01:01 ip-172-31-36-197 run-parts[24539]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 29 08:01:01 ip-172-31-36-197 run-parts[24539]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 29 08:01:01 ip-172-31-36-197 run-parts[24539]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 29 08:01:01 ip-172-31-36-197 run-parts[24539]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 29 08:01:01 ip-172-31-36-197 run-parts[24539]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 29 08:01:01 ip-172-31-36-197 run-parts[24539]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 29 08:01:01 ip-172-31-36-197 run-parts[24539]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 29 08:01:01 ip-172-31-36-197 run-parts[24539]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 29 08:01:01 ip-172-31-36-197 CROND[24538]: (root) CMDEND (run-parts /etc/cron.hourly)
Jun 29 08:10:01 ip-172-31-36-197 CROND[24889]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 08:10:01 ip-172-31-36-197 CROND[24888]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 08:30:01 ip-172-31-36-197 CROND[25594]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 08:30:01 ip-172-31-36-197 CROND[25593]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 08:50:01 ip-172-31-36-197 CROND[26356]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 08:50:01 ip-172-31-36-197 CROND[26355]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 09:01:01 ip-172-31-36-197 CROND[26776]: (root) CMD (run-parts /etc/cron.hourly)
Jun 29 09:01:01 ip-172-31-36-197 run-parts[26776]: (/etc/cron.hourly) starting 0anacron
Jun 29 09:01:01 ip-172-31-36-197 run-parts[26776]: (/etc/cron.hourly) finished 0anacron
Jun 29 09:01:01 ip-172-31-36-197 run-parts[26776]: (/etc/cron.hourly) starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 29 09:01:01 ip-172-31-36-197 run-parts[26776]: (/etc/cron.hourly) finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 29 09:01:01 ip-172-31-36-197 run-parts[26776]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 29 09:01:01 ip-172-31-36-197 run-parts[26776]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 29 09:01:01 ip-172-31-36-197 run-parts[26776]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 29 09:01:01 ip-172-31-36-197 run-parts[26776]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 29 09:01:01 ip-172-31-36-197 run-parts[26776]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 29 09:01:01 ip-172-31-36-197 run-parts[26776]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 29 09:01:01 ip-172-31-36-197 run-parts[26776]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 29 09:01:01 ip-172-31-36-197 run-parts[26776]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 29 09:01:01 ip-172-31-36-197 run-parts[26776]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 29 09:01:02 ip-172-31-36-197 run-parts[26776]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 29 09:01:02 ip-172-31-36-197 run-parts[26776]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 29 09:01:02 ip-172-31-36-197 run-parts[26776]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 29 09:01:02 ip-172-31-36-197 CROND[26775]: (root) CMDEND (run-parts /etc/cron.hourly)
Jun 29 09:10:01 ip-172-31-36-197 CROND[27125]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 09:10:01 ip-172-31-36-197 CROND[27124]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 09:30:01 ip-172-31-36-197 CROND[27829]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 09:30:01 ip-172-31-36-197 CROND[27828]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 09:50:01 ip-172-31-36-197 CROND[28533]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 09:50:01 ip-172-31-36-197 CROND[28532]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 10:01:01 ip-172-31-36-197 CROND[28955]: (root) CMD (run-parts /etc/cron.hourly)
Jun 29 10:01:01 ip-172-31-36-197 run-parts[28955]: (/etc/cron.hourly) starting 0anacron
Jun 29 10:01:01 ip-172-31-36-197 run-parts[28955]: (/etc/cron.hourly) finished 0anacron
Jun 29 10:01:01 ip-172-31-36-197 run-parts[28955]: (/etc/cron.hourly) starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 29 10:01:01 ip-172-31-36-197 run-parts[28955]: (/etc/cron.hourly) finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 29 10:01:01 ip-172-31-36-197 run-parts[28955]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 29 10:01:01 ip-172-31-36-197 run-parts[28955]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 29 10:01:01 ip-172-31-36-197 run-parts[28955]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 29 10:01:01 ip-172-31-36-197 run-parts[28955]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 29 10:01:01 ip-172-31-36-197 run-parts[28955]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 29 10:01:01 ip-172-31-36-197 run-parts[28955]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 29 10:01:01 ip-172-31-36-197 run-parts[28955]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 29 10:01:01 ip-172-31-36-197 run-parts[28955]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 29 10:01:01 ip-172-31-36-197 run-parts[28955]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 29 10:01:01 ip-172-31-36-197 run-parts[28955]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 29 10:01:01 ip-172-31-36-197 run-parts[28955]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 29 10:01:01 ip-172-31-36-197 run-parts[28955]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 29 10:01:01 ip-172-31-36-197 CROND[28954]: (root) CMDEND (run-parts /etc/cron.hourly)
Jun 29 10:10:01 ip-172-31-36-197 CROND[29304]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 10:10:01 ip-172-31-36-197 CROND[29303]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 10:30:01 ip-172-31-36-197 CROND[30008]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 10:30:01 ip-172-31-36-197 CROND[30007]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 10:50:01 ip-172-31-36-197 CROND[30770]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 10:50:01 ip-172-31-36-197 CROND[30769]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 11:01:01 ip-172-31-36-197 CROND[31189]: (root) CMD (run-parts /etc/cron.hourly)
Jun 29 11:01:01 ip-172-31-36-197 run-parts[31189]: (/etc/cron.hourly) starting 0anacron
Jun 29 11:01:01 ip-172-31-36-197 run-parts[31189]: (/etc/cron.hourly) finished 0anacron
Jun 29 11:01:01 ip-172-31-36-197 run-parts[31189]: (/etc/cron.hourly) starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 29 11:01:01 ip-172-31-36-197 run-parts[31189]: (/etc/cron.hourly) finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 29 11:01:01 ip-172-31-36-197 run-parts[31189]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 29 11:01:01 ip-172-31-36-197 run-parts[31189]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 29 11:01:01 ip-172-31-36-197 run-parts[31189]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 29 11:01:01 ip-172-31-36-197 run-parts[31189]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 29 11:01:01 ip-172-31-36-197 run-parts[31189]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 29 11:01:01 ip-172-31-36-197 run-parts[31189]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 29 11:01:01 ip-172-31-36-197 run-parts[31189]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 29 11:01:01 ip-172-31-36-197 run-parts[31189]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 29 11:01:01 ip-172-31-36-197 run-parts[31189]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 29 11:01:01 ip-172-31-36-197 run-parts[31189]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 29 11:01:01 ip-172-31-36-197 run-parts[31189]: (/etc/cron.hourly) starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 29 11:01:01 ip-172-31-36-197 run-parts[31189]: (/etc/cron.hourly) finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 29 11:01:01 ip-172-31-36-197 CROND[31188]: (root) CMDEND (run-parts /etc/cron.hourly)
Jun 29 11:10:01 ip-172-31-36-197 CROND[31538]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 11:10:01 ip-172-31-36-197 CROND[31537]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 11:30:01 ip-172-31-36-197 CROND[32243]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 29 11:30:01 ip-172-31-36-197 CROND[32242]: (root) CMDEND (/opt/elasticbeanstalk/bin/publishlogs -type publish)
