<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Photo Video Tool</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="dashboard-root">
        <div class="dashboard-main" style="width:100vw;">
            <main class="dashboard-content" role="main">
                <div class="container">
                    <section class="value-prop">
                        <h1 class="headline">MemorEase <span style="font-size:1.5rem;vertical-align:middle;">🦋</span></h1>
                        <div class="tagline" style="font-size:1.18rem;color:#007aff;font-weight:600;margin-bottom:18px;">Free your phone. Keep your memories. Forever.</div>
                        <p class="subheadline">Easily turn your photos & videos into high-quality videos for unlimited storage. Restore your memories anytime—no more deleting, no more extra storage needed.</p>
                        <button class="cta-btn" id="cta-btn" aria-label="Try Now">Try Now</button>
                        <button class="modern-btn blue-btn" id="show-tutorial-btn" style="margin-top:18px;">Show Tutorial</button>
                    </section>
                    <section id="tutorial-section" style="display:none;max-width:700px;margin:0 auto 24px auto;background:rgba(255,255,255,0.85);backdrop-filter:blur(8px);border-radius:18px;box-shadow:0 4px 24px 0 rgba(0,122,255,0.08);padding:32px 24px;">
                        <h2 style="color:#007aff;font-size:1.35rem;font-weight:700;margin-bottom:12px;">How MemorEase Solves Your Storage Pain</h2>
                        <p style="font-size:1.08rem;color:#444;margin-bottom:18px;">
                            <b>Is your phone full of photos and videos you don't want to delete?</b><br>
                            <span style="color:#007aff;font-weight:600;">MemorEase</span> lets you turn your media into a single, high-quality video you can upload to unlimited storage sites (like YouTube as a private link). When you want your photos back, you can restore them anytime using the generated metadata file—no need to buy extra storage or delete memories!
                        </p>
                        <ol style="font-size:1.05rem;color:#333;margin-bottom:18px;">
                            <li><b>Select your photos folder</b> in the "Create Video" tab.</li>
                            <li><b>Create a video and metadata file</b> with one click.</li>
                            <li><b>Upload the video</b> to YouTube (as a private/unlisted link) or any unlimited video storage service.</li>
                            <li><b>When you want your photos back</b>, use MemorEase to extract them from the video using the metadata file.</li>
                            <li><b>Enjoy peace of mind!</b> No more buying extra storage or deleting memories.</li>
                        </ol>
                        <div style="background:rgba(0,122,255,0.08);color:#007aff;padding:12px 18px;border-radius:10px;font-size:1.01rem;margin-bottom:0;">
                            <b>Tip:</b> You can store unlimited videos for free on YouTube as private links, and restore your photos anytime with MemorEase.
                        </div>
                        <button class="modern-btn" id="hide-tutorial-btn" style="margin-top:18px;">Hide Tutorial</button>
                    </section>
                </div>
                <div class="container">
                    <div class="main-action-area">
                        <div class="card-tabs-group">
                            <div class="dashboard-tabs" role="tablist" aria-label="Main actions">
                                <button class="tab-btn active" id="tab-create" role="tab" aria-selected="true" aria-controls="panel-create">Create Video</button>
                                <button class="tab-btn" id="tab-decompose" role="tab" aria-selected="false" aria-controls="panel-decompose">Decompose Video</button>
                                <button class="tab-btn" id="tab-activity" role="tab" aria-selected="false" aria-controls="panel-activity">Recent Activity</button>
                            </div>
                            <div class="tab-panel" id="panel-create" role="tabpanel" aria-labelledby="tab-create" style="display:block;">
                                <div class="modern-card action-card blue-card">
                                    {% if status_message %}<div class="status-inline {% if status_type %}status-{{ status_type }}{% endif %}" role="status">{{ status_message }}</div>{% endif %}
                                    <div class="modern-card-header blue">
                                        <span class="icon-svg">
                                            <svg width="28" height="28" fill="none" stroke="#2563eb" stroke-width="2" viewBox="0 0 24 24"><rect x="3" y="5" width="18" height="14" rx="2"/><path d="M7 5v14M17 5v14"/></svg>
                                        </span>
                                        <span class="modern-card-title blue">Create Video from Photos</span>
                                    </div>
                                    <p class="form-desc">Select a folder of photos and instantly turn them into a video. Perfect for slideshows, events, and memories.</p>
                                    <form action="https://winmemory-processing-env.eba-gu58unjs.us-west-2.elasticbeanstalk.com/create_video" method="post" enctype="multipart/form-data" aria-label="Create Video Form">
                                        <label>Input Photos Directory:</label>
                                        <div class="drag-drop-zone">
                                            <span class="icon-svg">
                                                <svg width="32" height="32" fill="none" stroke="#2563eb" stroke-width="2" viewBox="0 0 24 24"><path d="M3 7a2 2 0 0 1 2-2h4l2 2h8a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V7z"/></svg>
                                            </span>
                                            <div class="drag-drop-text">Drag & drop folder or click to browse</div>
                                            <div class="drag-drop-subtext">Drag & drop or click to browse</div>
                                            <input type="file" id="input_photos_dir" name="input_photos_dir" class="drag-input" webkitdirectory multiple required aria-label="Select photos folder">
                                        </div>
                                        <label>Output Video Filename (e.g., output.mp4):</label>
                                        <input type="text" id="output_video_name" name="output_video_name" class="modern-input" placeholder="e.g., my_new_video.mp4" required aria-label="Output video filename">
                                        <label>Output Metadata Filename (e.g., output.json):</label>
                                        <input type="text" id="output_metadata_name" name="output_metadata_name" class="modern-input" placeholder="e.g., video_metadata.json" required aria-label="Output metadata filename">
                                        <button type="submit" class="modern-btn blue-btn" title="Create a video from your photos" aria-label="Create Video">
                                            <span class="icon-svg">
                                                <svg width="22" height="22" fill="none" stroke="#fff" stroke-width="2" viewBox="0 0 24 24"><rect x="3" y="5" width="18" height="14" rx="2"/><path d="M7 5v14M17 5v14"/></svg>
                                            </span>
                                            Create Video
                                        </button>
                                    </form>
                                </div>
                                <div class="trust-block">
                                    <div class="testimonial">"WINmemory made it so easy to turn my vacation photos into a beautiful video!" <span class="testimonial-author">— Happy User</span></div>
                                    <div class="privacy-note">Your files are processed instantly and never stored on our servers. <span aria-label="privacy">🔒</span></div>
                                </div>
                                <button id="support-vote-btn" class="support-vote-btn" aria-label="Support and Vote">💡 Support & Vote</button>
                            </div>
                            <div class="tab-panel" id="panel-decompose" role="tabpanel" aria-labelledby="tab-decompose" style="display:none;">
                                <div class="modern-card action-card purple-card">
                                    {% if status_message %}<div class="status-inline {% if status_type %}status-{{ status_type }}{% endif %}" role="status">{{ status_message }}</div>{% endif %}
                                    <div class="modern-card-header purple">
                                        <span class="icon-svg">
                                            <svg width="28" height="28" fill="none" stroke="#a21caf" stroke-width="2" viewBox="0 0 24 24"><rect x="3" y="5" width="18" height="14" rx="2"/><circle cx="8.5" cy="10.5" r="1.5"/><path d="M21 19l-5.5-5.5a2.121 2.121 0 0 0-3 0L3 19"/></svg>
                                        </span>
                                        <span class="modern-card-title purple">Decompose Video to Photos</span>
                                    </div>
                                    <p class="form-desc">Upload a video and metadata to extract all photos as individual files. Great for archiving or editing.</p>
                                    <form action="{{ url_for('decompose_video_web') }}" method="post" enctype="multipart/form-data" aria-label="Decompose Video Form">
                                        <label>Input Video File:</label>
                                        <div class="drag-drop-zone">
                                            <span class="icon-svg">
                                                <svg width="32" height="32" fill="none" stroke="#a21caf" stroke-width="2" viewBox="0 0 24 24"><path d="M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-2"/><polyline points="7 9 12 4 17 9"/><line x1="12" y1="4" x2="12" y2="16"/></svg>
                                            </span>
                                            <div class="drag-drop-text">Drag & drop video file or click to browse</div>
                                            <div class="drag-drop-subtext">Drag & drop or click to browse</div>
                                            <input type="file" id="input_video_file" name="input_video_file" class="drag-input" accept="video/*" required aria-label="Select video file">
                                        </div>
                                        <label>Input Metadata File:</label>
                                        <div class="drag-drop-zone">
                                            <span class="icon-svg">
                                                <svg width="32" height="32" fill="none" stroke="#a21caf" stroke-width="2" viewBox="0 0 24 24"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/><polyline points="14 2 14 8 20 8"/></svg>
                                            </span>
                                            <div class="drag-drop-text">Drag & drop metadata file or click to browse</div>
                                            <div class="drag-drop-subtext">Drag & drop or click to browse</div>
                                            <input type="file" id="input_metadata_file" name="input_metadata_file" class="drag-input" accept=".json" required aria-label="Select metadata file">
                                        </div>
                                        <label>Output Photos Directory Name:</label>
                                        <input type="text" id="output_photos_dir_name" name="output_photos_dir_name" class="modern-input" placeholder="e.g., MyDecomposedPhotos/" required aria-label="Output photos directory name">
                                        <button type="submit" class="modern-btn purple-btn" title="Decompose a video into photos" aria-label="Decompose Video">
                                            <span class="icon-svg">
                                                <svg width="22" height="22" fill="none" stroke="#fff" stroke-width="2" viewBox="0 0 24 24"><rect x="3" y="5" width="18" height="14" rx="2"/><circle cx="8.5" cy="10.5" r="1.5"/><path d="M21 19l-5.5-5.5a2.121 2.121 0 0 0-3 0L3 19"/></svg>
                                            </span>
                                            Decompose Video
                                        </button>
                                    </form>
                                </div>
                                <div class="trust-block">
                                    <div class="testimonial">"WINmemory made it so easy to turn my vacation photos into a beautiful video!" <span class="testimonial-author">— Happy User</span></div>
                                    <div class="privacy-note">Your files are processed instantly and never stored on our servers. <span aria-label="privacy">🔒</span></div>
                                </div>
                                <button id="support-vote-btn" class="support-vote-btn" aria-label="Support and Vote">💡 Support & Vote</button>
                            </div>
                            <div class="tab-panel" id="panel-activity" role="tabpanel" aria-labelledby="tab-activity" style="display:none;">
                                <div class="modern-card widget-card">
                                    <div class="modern-card-header gray">
                                        <span class="icon-svg">🕒</span>
                                        <span class="modern-card-title gray">Recent Activity</span>
                                    </div>
                                    <div class="widget-content">
                                        <p>No recent activity yet. Your actions will appear here.</p>
                                    </div>
                                </div>
                            </div>
                            {% if video_filename or metadata_filename %}
                            <div class="download-section">
                                <h3>Download your results:</h3>
                                {% if video_filename %}
                                    <a href="{{ url_for('download_file', filename=video_filename) }}" class="modern-btn blue-btn" download title="Download the generated video">Download Video</a>
                                {% endif %}
                                {% if metadata_filename %}
                                    <a href="{{ url_for('download_file', filename=metadata_filename) }}" class="modern-btn purple-btn" download title="Download the metadata file">Download Metadata</a>
                                {% endif %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="container">
                    <footer class="dashboard-footer">
                        &copy; 2024 MemorEase &mdash; v1.0 | <a href="mailto:<EMAIL>">Contact Us</a> | <a href="https://github.com/your-repo" target="_blank">GitHub</a>
                    </footer>
                </div>
            </main>
        </div>
    </div>
    <div id="support-vote-modal" class="modal" role="dialog" aria-modal="true" aria-labelledby="modal-title" tabindex="-1">
        <div class="modal-content">
            <button class="close-modal" id="close-modal-btn" aria-label="Close">&times;</button>
            <h2 id="modal-title">Support the Project</h2>
            <a href="https://www.paypal.com/donate?hosted_button_id=YOUR_BUTTON_ID" target="_blank" class="donate-btn">Donate via PayPal</a>
            <div class="donation-note">Your support helps keep this project alive and free!</div>
            <hr style="margin: 24px 0;">
            <h3>Vote for New Features</h3>
            <form id="features-vote-form">
                <label><input type="checkbox" name="feature" value="cloud"> Cloud Storage Integration</label><br>
                <label><input type="checkbox" name="feature" value="batch"> Batch Video Processing</label><br>
                <label><input type="checkbox" name="feature" value="filters"> Photo/Video Filters</label><br>
                <label><input type="checkbox" name="feature" value="share"> One-click Social Sharing</label><br>
                <label><input type="checkbox" name="feature" value="mobile"> Mobile App Version</label><br>
                <button type="submit" class="modern-btn blue-btn">Submit Vote</button>
            </form>
            <div id="vote-thankyou" style="display:none; color:#22c55e; font-weight:600; margin-top:10px;">Thank you for your feedback!</div>
        </div>
    </div>
    <script src="{{ url_for('static', filename='script.js') }}"></script>
    <script>
        // Modal open/close logic
        const supportBtn = document.getElementById('support-vote-btn');
        const modal = document.getElementById('support-vote-modal');
        const closeModalBtn = document.getElementById('close-modal-btn');
        supportBtn.onclick = () => { modal.style.display = 'flex'; modal.focus(); };
        closeModalBtn.onclick = () => { modal.style.display = 'none'; };
        // Tutorial show/hide logic
        const showTutorialBtn = document.getElementById('show-tutorial-btn');
        const hideTutorialBtn = document.getElementById('hide-tutorial-btn');
        const tutorialSection = document.getElementById('tutorial-section');
        showTutorialBtn.onclick = () => { tutorialSection.style.display = 'block'; showTutorialBtn.style.display = 'none'; };
        hideTutorialBtn.onclick = () => { tutorialSection.style.display = 'none'; showTutorialBtn.style.display = 'inline-block'; };
    </script>
</body>
</html>